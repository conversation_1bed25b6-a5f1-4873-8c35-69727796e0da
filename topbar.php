<?php
// Definir a página ativa baseada no nome do arquivo atual
$pagina_atual = basename($_SERVER['PHP_SELF'], '.php');
$pagina_ativa = $pagina_atual;

// Tratar o caso especial do index.php
if ($pagina_ativa === 'index') {
    $pagina_ativa = 'inicio';
}

// Carregar permissões do usuário
include_once 'conexao.php';
$acessos = [];
$usuario_id = $_SESSION['usuario_id'] ?? null;
if ($usuario_id) {
    $sql = "SELECT menu, item, permitido FROM usuarios_acessos WHERE usuario_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $chave = $row['menu'] . ($row['item'] ? ('.' . $row['item']) : '');
        $acessos[$chave] = (int)$row['permitido'];
    }
}
function acesso_menu($menu, $acessos) {
    return !isset($acessos[$menu]) || $acessos[$menu];
}
function acesso_item($menu, $item, $acessos) {
    return !isset($acessos[$menu . '.' . $item]) || $acessos[$menu . '.' . $item];
}
?>

<div class="topbar">
    <div style="width:100%;text-align:center;padding:10px 0 8px 0;">
        <img src="assets/logo.png" alt="Logo" style="max-width:200px;max-height:150px;display:inline-block;">
    </div>
    <a href="index.php" class="topbar-button<?php echo (isset(
        $pagina_ativa) && $pagina_ativa === 'inicio') ? ' active' : ''; ?>">Início</a>

    <!-- Botão de Cadastros -->
    <?php if (acesso_menu('cadastros', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Cadastros</button>
        <div id="cadastrosMenu" class="dropdown">
            <?php if (acesso_item('cadastros', 'cadastro-produto', $acessos)): ?><a href="cadastro-produto.php">Cadastro de Produto</a><?php endif; ?>
            <?php if (acesso_item('cadastros', 'cadastro-pessoas', $acessos)): ?><a href="cadastro-pessoas.php">Cadastro de Pessoas</a><?php endif; ?>
            <?php if (acesso_item('cadastros', 'cadastro-empresas', $acessos)): ?><a href="cadastro-empresas.php">Cadastro de Empresas</a><?php endif; ?>
            <?php if (acesso_item('cadastros', 'cadastro-setor', $acessos)): ?><a href="cadastro-setor.php">Cadastro de Setor</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Botão de Tabelas -->
    <?php if (acesso_menu('tabelas', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Tabelas</button>
        <div id="tabelasMenu" class="dropdown">
            <?php if (acesso_item('tabelas', 'tabela-produtos', $acessos)): ?><a href="tabela-produtos.php">Tabela de Produtos</a><?php endif; ?>
            <?php if (acesso_item('tabelas', 'tabela-pessoas', $acessos)): ?><a href="tabela-pessoas.php">Tabela de Pessoas</a><?php endif; ?>
            <?php if (acesso_item('tabelas', 'tabela-empresas', $acessos)): ?><a href="tabela-empresas.php">Tabela de Empresas</a><?php endif; ?>
            <?php if (acesso_item('tabelas', 'tabela-setor', $acessos)): ?><a href="tabela-setor.php">Tabela de Setor</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Botão de Entradas -->
    <?php if (acesso_menu('entradas', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Entradas</button>
        <div id="entradasMenu" class="dropdown">
            <?php if (acesso_item('entradas', 'entrada-estoque', $acessos)): ?><a href="entrada-estoque.php">Entrada de Estoque</a><?php endif; ?>
            <?php if (acesso_item('entradas', 'registro-entradas', $acessos)): ?><a href="registro-entradas.php">Registros de Entrada</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Botão de Saída -->
    <?php if (acesso_menu('saidas', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Saída</button>
        <div id="saidasMenu" class="dropdown">
            <?php if (acesso_item('saidas', 'saida-estoque', $acessos)): ?><a href="saida-estoque.php">Saída de Estoque</a><?php endif; ?>
            <?php if (acesso_item('saidas', 'registro-saidas', $acessos)): ?><a href="registro-saidas.php">Registro de Saídas</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Botão de Solicitações -->
    <?php if (acesso_menu('solicitacoes', $acessos)): ?>
        <a href="todas-solitacoes-estoque.php" class="topbar-button<?php echo ($pagina_ativa === 'todas-solitacoes-estoque') ? ' active' : ''; ?>">Solicitações</a>
    <?php endif; ?>

    <!-- Botão de Fichas EPI -->
    <?php if (acesso_menu('epi', $acessos)): ?>
        <a href="fichas-epi.php" class="topbar-button<?php echo ($pagina_ativa === 'fichas-epi') ? ' active' : ''; ?>">Fichas EPI</a>
    <?php endif; ?>

    <!-- Botão de Devolução -->
    <?php if (acesso_menu('devolucao', $acessos)): ?>
        <a href="devolucao.php" class="topbar-button<?php echo ($pagina_ativa === 'devolucao') ? ' active' : ''; ?>">Devolução</a>
    <?php endif; ?>

    <!-- Botão de Limites de Gastos -->
    <?php if (acesso_menu('limites', $acessos)): ?>
        <a href="limites-gastos.php" class="topbar-button<?php echo ($pagina_ativa === 'limites-gastos') ? ' active' : ''; ?>">Limites de Gastos</a>
    <?php endif; ?>

    <div class="separator"></div>

    <!-- Botão de Fazer Pedido -->
    <?php if (acesso_menu('pedidos', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Fazer Pedido</button>
        <div id="pedidosMenu" class="dropdown"> 
            <?php if (acesso_item('pedidos', 'requisicoes', $acessos)): ?><a href="requisicoes.php">Requisições</a><?php endif; ?>    
            <?php if (acesso_item('pedidos', 'pedidos-mensais', $acessos)): ?><a href="pedidos-mensais.php">Pedidos mensais</a><?php endif; ?>
            <?php if (acesso_item('pedidos', 'produtos-pedido-mensal', $acessos)): ?><a href="produtos-pedido-mensal.php">Produtos Pedido Mensal</a><?php endif; ?>
            <?php if (acesso_item('pedidos', 'pedidos-especiais', $acessos)): ?><a href="Pedidos-especiais.php">Pedidos especiais</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Botão de Meus Pedidos -->
    <?php if (acesso_menu('meus-pedidos', $acessos)): ?>
        <a href="requisicoes-feitas.php" class="topbar-button<?php echo ($pagina_ativa === 'requisicoes-feitas') ? ' active' : ''; ?>">Meus Pedidos</a>
    <?php endif; ?>

    <!-- Menu de Usuário (apenas para administradores) -->
    <?php if ($_SESSION['tipo_usuario'] === 'administrador' && acesso_menu('usuarios', $acessos)): ?>
    <div class="menu-wrapper">
        <button class="topbar-button">Usuários</button>
        <div id="usuariosMenu" class="dropdown">
            <?php if (acesso_item('usuarios', 'cadastro-usuario', $acessos)): ?><a href="cadastro-usuario.php">Cadastrar Usuário</a><?php endif; ?>
            <?php if (acesso_item('usuarios', 'gerenciar-usuarios', $acessos)): ?><a href="gerenciar-usuarios.php">Gerenciar Usuários</a><?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Informações do usuário logado -->
    <div class="user-info" style="display: flex; align-items: center; flex-direction: row; justify-content: flex-start; gap: 16px; width: 100%; padding: 0 18px 18px 18px; min-height: 70px; border-top-right-radius: 32px; border-bottom-right-radius: 18px; background-color: #181818;">
        <div class="user-details" style="display: flex; flex-direction: column; flex-grow: 0.7; max-width: 180px; min-width: 0; flex-shrink: 1;">
            <span class="user-name" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"><?php echo htmlspecialchars($_SESSION['nome_usuario']); ?></span>
            <span class="user-type" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">(<?php echo ucfirst($_SESSION['tipo_usuario']); ?>)</span>
        </div>
        <div class="logout-wrapper" style="display: flex; align-items: center; flex-shrink: 0; margin-left: 8px;">
            <a href="logout.php" class="logout-btn" title="Sair" style="display: flex; align-items: center; justify-content: center; padding: 8px; border-radius: 6px; transition: background 0.2s;">
                <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"/>
                    <polyline points="10 17 15 12 10 7"/>
                    <line x1="15" y1="12" x2="3" y2="12"/>
                </svg>
            </a>
        </div>
    </div>
</div>

<!-- JavaScript para alternar os menus -->
<script>
    // Remover função toggleMenu e listener de click para dropdown
</script>

<!-- CSS -->
<style>
    .topbar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 270px;
        background: #111;
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        padding: 0;
        z-index: 10000;
        box-shadow: 2px 0 12px rgba(0,0,0,0.12);
        overflow: hidden;
    }
    .topbar a,
    .topbar button {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 14px 22px;
        color: #fff;
        text-decoration: none;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 15px;
        border-radius: 0 18px 18px 0;
        margin: 0;
        transition: background 0.2s, color 0.2s, font-weight 0.2s;
        outline: none;
        gap: 10px;
        position: relative;
        min-height: 56px;
        box-sizing: border-box;
    }
    .topbar a:hover,
    .topbar button:hover {
        background: #222;
        color: #fff;
        text-decoration: none;
        font-weight: bold;
    }
    .topbar a.active, .topbar button.active {
        background: #181818;
        color: #fff;
        border-left: 4px solid #fff;
        box-shadow: none;
    }
    .menu-wrapper {
        position: relative;
    }
    .dropdown {
        display: block;
        max-height: 0;
        opacity: 0;
        overflow: hidden;
        background: #23272e;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.08);
        transition: max-height 0.7s cubic-bezier(.4,0,.2,1), opacity 0s;
        z-index: 10001;
    }
    .menu-wrapper:hover > .dropdown,
    .menu-wrapper:focus-within > .dropdown {
        max-height: 500px;
        opacity: 1;
        overflow: visible;
        transition: max-height 0.7s cubic-bezier(.4,0,.2,1), opacity 0.7s;
    }
    .separator {
        height: 1px;
        width: 80%;
        background: #222;
        margin: 10px auto 10px auto;
    }
    .user-info {
        margin-top: auto;
        padding: 16px 22px;
        background: #181818;
        border-radius: 0 18px 18px 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        font-size: 16px;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.08);
    }
    .user-name {
        font-weight: bold;
        color: #fff;
        font-size: 17px;
    }
    .user-type {
        color: #bbb;
        font-size: 15px;
    }
    .logout-btn {
        background: #dc3545;
        color: white;
        padding: 7px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 15px;
        margin-top: 8px;
        transition: background 0.3s;
        border: none;
        display: inline-block;
    }
    .logout-btn:hover {
        background: #c82333;
        color: #fff;
        text-decoration: none;
    }
    @media (max-width: 700px) {
        .topbar {
            width: 100vw;
            height: auto;
            flex-direction: row;
            position: static;
            box-shadow: none;
        }
        .topbar a, .topbar button {
            border-radius: 0;
            padding: 10px 10px;
            font-size: 16px;
        }
        .dropdown {
            left: 0;
            top: 100%;
            min-width: 160px;
            border-radius: 0 0 8px 8px;
        }
        .user-info {
            margin-top: 0;
            padding: 10px;
            border-radius: 0;
            box-shadow: none;
        }
    }
    .dropdown a {
        padding-left: 32px;
    }
</style>

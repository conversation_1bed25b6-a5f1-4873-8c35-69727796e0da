<?php
require_once 'conexao.php';

echo "<h2>Debug - Produtos por Funcionário</h2>";

// 1. Verificar se existem pessoas com EPIs
$sql_pessoas_epi = "
    SELECT DISTINCT p.id, p.nome, COUNT(pe.id) as total_epis
    FROM pessoas p
    INNER JOIN pessoa_epi pe ON p.id = pe.pessoa_id
    WHERE p.status = 'ativo'
    GROUP BY p.id, p.nome
    ORDER BY p.nome
    LIMIT 5
";

$result = $conn->query($sql_pessoas_epi);

if ($result && $result->num_rows > 0) {
    echo "<h3>✅ Pessoas com EPIs:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Total EPIs</th><th>Testar Endpoint</th></tr>";
    
    $primeira_pessoa_id = null;
    while ($row = $result->fetch_assoc()) {
        if ($primeira_pessoa_id === null) {
            $primeira_pessoa_id = $row['id'];
        }
        
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nome'] . "</td>";
        echo "<td>" . $row['total_epis'] . "</td>";
        echo "<td><a href='obter-produtos-pessoa.php?pessoa_id=" . $row['id'] . "' target='_blank'>Testar API</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Testar consulta detalhada para primeira pessoa
    if ($primeira_pessoa_id) {
        echo "<h3>🔍 Teste Detalhado para Pessoa ID: $primeira_pessoa_id</h3>";
        
        // Consulta original (antes da correção)
        echo "<h4>Consulta Original (pode estar com problema):</h4>";
        $sql_original = "
            SELECT 
                pe.produto_id,
                p.nome,
                p.codigo,
                SUM(pe.quantidade) - COALESCE(SUM(pe.quantidade_devolvida), 0) as quantidade_disponivel,
                MAX(pe.data_entrega) as ultima_entrega
            FROM 
                pessoa_epi pe
            JOIN 
                produtos p ON pe.produto_id = p.codigo
            WHERE 
                pe.pessoa_id = $primeira_pessoa_id
            GROUP BY 
                pe.produto_id, p.nome, p.codigo
            HAVING 
                quantidade_disponivel > 0
            ORDER BY 
                p.nome ASC
        ";
        
        $result_original = $conn->query($sql_original);
        if ($result_original && $result_original->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Produto ID</th><th>Nome</th><th>Código</th><th>Qtd Disponível</th><th>Última Entrega</th></tr>";
            while ($row = $result_original->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['produto_id'] . "</td>";
                echo "<td>" . $row['nome'] . "</td>";
                echo "<td>" . $row['codigo'] . "</td>";
                echo "<td>" . $row['quantidade_disponivel'] . "</td>";
                echo "<td>" . $row['ultima_entrega'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Nenhum produto encontrado com a consulta original</p>";
            echo "<p><strong>Erro SQL:</strong> " . $conn->error . "</p>";
        }
        
        // Consulta alternativa (mais simples)
        echo "<h4>Consulta Alternativa (mais simples):</h4>";
        $sql_alternativa = "
            SELECT 
                pe.produto_id,
                p.nome,
                p.codigo,
                pe.quantidade,
                pe.data_entrega,
                COALESCE(pe.quantidade_devolvida, 0) as devolvida
            FROM 
                pessoa_epi pe
            JOIN 
                produtos p ON pe.produto_id = p.codigo
            WHERE 
                pe.pessoa_id = $primeira_pessoa_id
            ORDER BY 
                pe.data_entrega DESC, p.nome ASC
        ";
        
        $result_alt = $conn->query($sql_alternativa);
        if ($result_alt && $result_alt->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Produto ID</th><th>Nome</th><th>Quantidade</th><th>Devolvida</th><th>Data Entrega</th></tr>";
            while ($row = $result_alt->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['produto_id'] . "</td>";
                echo "<td>" . $row['nome'] . "</td>";
                echo "<td>" . $row['quantidade'] . "</td>";
                echo "<td>" . $row['devolvida'] . "</td>";
                echo "<td>" . $row['data_entrega'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ Nenhum produto encontrado com consulta alternativa</p>";
        }
        
        // 3. Verificar estrutura da tabela pessoa_epi
        echo "<h4>Estrutura da tabela pessoa_epi:</h4>";
        $result_struct = $conn->query("DESCRIBE pessoa_epi");
        if ($result_struct) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $result_struct->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} else {
    echo "<h3>⚠️ Nenhuma pessoa com EPIs encontrada</h3>";
    
    // Verificar se existem pessoas
    $result_pessoas = $conn->query("SELECT COUNT(*) as total FROM pessoas WHERE status = 'ativo'");
    $total_pessoas = $result_pessoas->fetch_assoc()['total'];
    echo "<p>Total de pessoas ativas: $total_pessoas</p>";
    
    // Verificar se existem EPIs
    $result_epis = $conn->query("SELECT COUNT(*) as total FROM pessoa_epi");
    $total_epis = $result_epis->fetch_assoc()['total'];
    echo "<p>Total de registros pessoa_epi: $total_epis</p>";
}

// 4. Testar endpoint diretamente
echo "<h3>🌐 Teste do Endpoint:</h3>";
if (isset($primeira_pessoa_id)) {
    echo "<p><a href='obter-produtos-pessoa.php?pessoa_id=$primeira_pessoa_id' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Testar obter-produtos-pessoa.php</a></p>";
} else {
    echo "<p>Nenhuma pessoa disponível para teste</p>";
}

echo "<h3>🔧 Possíveis Soluções:</h3>";
echo "<ol>";
echo "<li>Verificar se os campos quantidade_devolvida existem na tabela pessoa_epi</li>";
echo "<li>Verificar se há dados de teste suficientes</li>";
echo "<li>Verificar se a consulta SQL está correta</li>";
echo "<li>Verificar se há erros JavaScript no console do navegador</li>";
echo "</ol>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Voltar para Devolução</a>";
echo "</p>";

$conn->close();
?>

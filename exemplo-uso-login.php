<?php
// Exemplo de como usar o sistema de verificação de login em outras páginas

// 1. Para páginas que precisam apenas de login (qualquer usuário logado)
include 'verificar-login.php';
// O arquivo já verifica se o usuário está logado automaticamente

// 2. Para páginas que precisam de permissão de administrador
include 'verificar-login.php';
verificarAdmin(); // Verifica se é administrador

// 3. Para páginas que precisam de permissão de supervisor ou administrador
include 'verificar-login.php';
verificarSupervisor(); // Verifica se é supervisor ou administrador

// 4. Para páginas que precisam de permissão de almoxarife, supervisor ou administrador
include 'verificar-login.php';
verificarAlmoxarife(); // Verifica se é almoxarife, supervisor ou administrador

// 5. Para verificações condicionais no HTML
if ($_SESSION['tipo_usuario'] === 'administrador') {
    // Mostrar conteúdo apenas para administradores
    echo '<button>Gerenciar Usuários</button>';
}

// 6. Para mostrar informações do usuário logado
echo 'Bem-vindo, ' . htmlspecialchars($_SESSION['nome_usuario']) . '!';
echo 'Tipo: ' . ucfirst($_SESSION['tipo_usuario']);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemplo de Uso - Sistema de Login</title>
</head>
<body>
    <h1>Exemplo de Uso do Sistema de Login</h1>
    
    <h2>Informações do Usuário Logado:</h2>
    <p><strong>Nome:</strong> <?php echo htmlspecialchars($_SESSION['nome_usuario']); ?></p>
    <p><strong>Tipo:</strong> <?php echo ucfirst($_SESSION['tipo_usuario']); ?></p>
    <p><strong>ID:</strong> <?php echo $_SESSION['usuario_id']; ?></p>
    
    <h2>Conteúdo Condicional por Tipo de Usuário:</h2>
    
    <?php if ($_SESSION['tipo_usuario'] === 'administrador'): ?>
        <div style="background: #d4edda; padding: 10px; border-radius: 5px;">
            <h3>Conteúdo Exclusivo para Administradores</h3>
            <p>Você tem acesso total ao sistema.</p>
            <ul>
                <li>Gerenciar usuários</li>
                <li>Configurar sistema</li>
                <li>Acessar relatórios avançados</li>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if ($_SESSION['tipo_usuario'] === 'supervisor' || $_SESSION['tipo_usuario'] === 'administrador'): ?>
        <div style="background: #fff3cd; padding: 10px; border-radius: 5px;">
            <h3>Conteúdo para Supervisores e Administradores</h3>
            <p>Você pode visualizar relatórios e gerenciar estoque.</p>
            <ul>
                <li>Visualizar relatórios</li>
                <li>Gerenciar estoque</li>
                <li>Aprovar solicitações</li>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (in_array($_SESSION['tipo_usuario'], ['almoxarife', 'supervisor', 'administrador'])): ?>
        <div style="background: #d1ecf1; padding: 10px; border-radius: 5px;">
            <h3>Conteúdo para Almoxarifes, Supervisores e Administradores</h3>
            <p>Você pode fazer entradas e saídas de estoque.</p>
            <ul>
                <li>Fazer entradas de estoque</li>
                <li>Fazer saídas de estoque</li>
                <li>Consultar estoque</li>
            </ul>
        </div>
    <?php endif; ?>
    
    <h2>Exemplos de Uso em Diferentes Páginas:</h2>
    
    <h3>1. Página de Cadastro de Produto (qualquer usuário logado):</h3>
    <pre><code>
&lt;?php
include 'verificar-login.php';
// Apenas incluir o arquivo - ele já verifica se está logado
?&gt;
    </code></pre>
    
    <h3>2. Página de Gerenciamento de Usuários (apenas administradores):</h3>
    <pre><code>
&lt;?php
include 'verificar-login.php';
verificarAdmin(); // Verifica se é administrador
?&gt;
    </code></pre>
    
    <h3>3. Página de Relatórios (supervisores e administradores):</h3>
    <pre><code>
&lt;?php
include 'verificar-login.php';
verificarSupervisor(); // Verifica se é supervisor ou administrador
?&gt;
    </code></pre>
    
    <h3>4. Página de Entrada de Estoque (almoxarifes, supervisores e administradores):</h3>
    <pre><code>
&lt;?php
include 'verificar-login.php';
verificarAlmoxarife(); // Verifica se é almoxarife, supervisor ou administrador
?&gt;
    </code></pre>
    
    <h2>Funções Disponíveis:</h2>
    
    <h3>verificarAdmin()</h3>
    <p>Verifica se o usuário é administrador. Se não for, redireciona para index.php.</p>
    
    <h3>verificarSupervisor()</h3>
    <p>Verifica se o usuário é supervisor ou administrador. Se não for, redireciona para index.php.</p>
    
    <h3>verificarAlmoxarife()</h3>
    <p>Verifica se o usuário é almoxarife, supervisor ou administrador. Se não for, redireciona para index.php.</p>
    
    <h2>Variáveis de Sessão Disponíveis:</h2>
    <ul>
        <li><strong>$_SESSION['usuario_id']</strong> - ID do usuário no banco de dados</li>
        <li><strong>$_SESSION['nome_usuario']</strong> - Nome de usuário</li>
        <li><strong>$_SESSION['tipo_usuario']</strong> - Tipo de usuário (administrador, supervisor, almoxarife)</li>
    </ul>
    
    <p><a href="index.php">Voltar ao Início</a></p>
</body>
</html> 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
include 'conexao.php';

// Inicializar variáveis de feedback
$feedbackMessage = "";
$feedbackType = "";

// Verificar se há mensagens de status na URL
if (isset($_GET['status'])) {
    if ($_GET['status'] == 'success') {
        $feedbackMessage = "Operação realizada com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'error') {
        $feedbackMessage = "Erro ao realizar a operação.";
        if (isset($_GET['message'])) {
            $feedbackMessage .= " " . $_GET['message'];
        }
        $feedbackType = "error";
    }
}

// Verificar quais colunas existem nas tabelas
$check_requisicoes_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
$check_pedidos_mensais_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
$check_pedidos_especiais_data = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE '%data%'");

$check_requisicoes_status = $conn->query("SHOW COLUMNS FROM requisicoes WHERE Field = 'status'");
$check_pedidos_mensais_status = $conn->query("SHOW COLUMNS FROM pedidos_mensais WHERE Field = 'status'");
$check_pedidos_especiais_status = $conn->query("SHOW COLUMNS FROM pedidos_especiais WHERE Field = 'status'");

// Definir colunas de data padrão
$data_requisicoes = "NOW() as data_solicitacao";
$data_pedidos_mensais = "NOW() as data_solicitacao";
$data_pedidos_especiais = "NOW() as data_solicitacao";

// Definir colunas de status padrão
$status_requisicoes = "'pendente' as status";
$status_pedidos_mensais = "'pendente' as status";
$status_pedidos_especiais = "'pendente' as status";

// Verificar se encontramos colunas de data nas tabelas
if ($check_requisicoes_data && $check_requisicoes_data->num_rows > 0) {
    $col = $check_requisicoes_data->fetch_assoc();
    $data_requisicoes = $col['Field'] . " as data_solicitacao";
}

if ($check_pedidos_mensais_data && $check_pedidos_mensais_data->num_rows > 0) {
    $col = $check_pedidos_mensais_data->fetch_assoc();
    $data_pedidos_mensais = $col['Field'] . " as data_solicitacao";
}

if ($check_pedidos_especiais_data && $check_pedidos_especiais_data->num_rows > 0) {
    $col = $check_pedidos_especiais_data->fetch_assoc();
    $data_pedidos_especiais = $col['Field'] . " as data_solicitacao";
}

// Verificar se encontramos colunas de status nas tabelas
if ($check_requisicoes_status && $check_requisicoes_status->num_rows > 0) {
    $status_requisicoes = "status";
}

if ($check_pedidos_mensais_status && $check_pedidos_mensais_status->num_rows > 0) {
    $status_pedidos_mensais = "status";
}

if ($check_pedidos_especiais_status && $check_pedidos_especiais_status->num_rows > 0) {
    $status_pedidos_especiais = "status";
}

// Consulta para obter requisições normais
$sql_requisicoes = "SELECT
                    codigo_solicitacao as codigo,
                    'requisicao' as tipo,
                    solicitante,
                    empresa,
                    contrato,
                    finalidade,
                    funcionario,
                    funcao,
                    observacao,
                    COALESCE(requisicao_urgente, 0) as pedido_urgente,
                    COALESCE(motivo_urgente, '') as motivo_urgente,
                    $data_requisicoes,
                    $status_requisicoes
                FROM requisicoes";

// Consulta para obter pedidos mensais
$sql_pedidos_mensais = "SELECT
                        codigo_pedido as codigo,
                        'pedido_mensal' as tipo,
                        solicitante,
                        empresa,
                        contrato,
                        finalidade,
                        destinatario as funcionario,
                        funcao,
                        observacao,
                        COALESCE(pedido_urgente, 0) as pedido_urgente,
                        COALESCE(motivo_urgente, '') as motivo_urgente,
                        $data_pedidos_mensais,
                        $status_pedidos_mensais
                    FROM pedidos_mensais";

// Consulta para obter pedidos especiais
$sql_pedidos_especiais = "SELECT
                          codigo_pedido as codigo,
                          'pedido_especial' as tipo,
                          solicitante,
                          empresa,
                          contrato,
                          finalidade,
                          '' as funcionario,
                          '' as funcao,
                          detalhes as observacao,
                          0 as pedido_urgente,
                          '' as motivo_urgente,
                          $data_pedidos_especiais,
                          $status_pedidos_especiais
                      FROM pedidos_especiais";

// Combinar as consultas com UNION
$sql = "($sql_requisicoes) UNION ($sql_pedidos_mensais) UNION ($sql_pedidos_especiais) ORDER BY data_solicitacao DESC";

$result = $conn->query($sql);

// Verificar se a consulta foi bem-sucedida
if (!$result) {
    die("Erro na consulta: " . $conn->error);
}

// Separar solicitações pendentes e concluídas
$solicitacoes_pendentes = [];
$solicitacoes_concluidas = [];

while ($row = $result->fetch_assoc()) {
    if (isset($row['status']) && $row['status'] == 'concluido') {
        $solicitacoes_concluidas[] = $row;
    } else {
        $solicitacoes_pendentes[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todas as Solicitações de Estoque</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #fff;
            border-radius: 16px;
            margin: 20px 20px 20px 290px;
            padding: 32px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 2px 16px rgba(0,0,0,0.10);
            position: relative;
            z-index: 1;
        }
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: left;
            margin-bottom: 32px;
            letter-spacing: -0.5px;
        }
        .legenda {
            display: flex;
            gap: 18px;
            margin-bottom: 18px;
            flex-wrap: wrap;
        }
        .legenda-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #374151;
        }
        .legenda-cor {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }
        .tipo-requisicao { background: #4CAF50; }
        .tipo-pedido_mensal { background: #2196F3; }
        .tipo-pedido_especial { background: #FF9800; }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 60px;
            position: relative;
        }
        
        .header-container h1 {
            margin: 0;
        }
        
        .filter-btn {
            margin-left: 16px;
            /* O resto do estilo está inline no botão para garantir compatibilidade visual */
        }
        
        .filter-dropdown {
            position: absolute;
            top: 44px;
            right: 0;
            background: #fff;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: none;
            min-width: 160px;
        }
        
        .filter-option {
            padding: 10px 16px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
            color: #374151;
        }
        
        .filter-option:hover {
            background: #f3f4f6;
        }
        
        .filter-option:first-child {
            border-radius: 8px 8px 0 0;
        }
        
        .filter-option:last-child {
            border-radius: 0 0 8px 8px;
        }
        /* Abas minimalistas */
        .tabs-container {
            display: flex;
            gap: 0;
            margin-bottom: 32px;
            border-bottom: 2px solid #e5e7eb;
        }
        .tab-btn {
            background: none;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            padding: 14px 32px 12px 32px;
            cursor: pointer;
            border-radius: 12px 12px 0 0;
            transition: background 0.2s, color 0.2s;
            margin-right: 2px;
        }
        .tab-btn.active {
            background: #fff !important;
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
            /* Removido qualquer fundo azul */
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        /* Grid de cards minimalista */
        .solicitacoes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 18px;
            margin-bottom: 0;
        }
        .solicitacao-card {
            background: #fafafa;
            border-radius: 10px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.04);
            padding: 22px 20px 18px 20px;
            cursor: pointer;
            border: 1.5px solid #e5e7eb;
            transition: box-shadow 0.2s, border 0.2s, background 0.2s;
        }
        .solicitacao-card:hover {
            /* Removido efeito azul de hover */
            box-shadow: 0 1px 4px rgba(0,0,0,0.04);
            border-color: #e5e7eb;
            background: #fafafa;
        }
        .solicitacao-card h3 {
            margin-top: 0;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            color: #111827;
        }
        .solicitacao-card p {
            margin: 6px 0;
            color: #374151;
            font-size: 14px;
        }
        .solicitacao-card .data {
            font-size: 13px;
            color: #6b7280;
            margin-top: 10px;
            text-align: right;
        }
        .tipo-badge {
            padding: 4px 10px;
            border-radius: 5px;
            color: #fff;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }
        .solicitacao-concluida {
            opacity: 0.7;
            background: #f3f4f6;
        }
        .solicitacao-concluida:hover {
            opacity: 1;
        }

        /* Estilos para pedidos urgentes */
        .solicitacao-urgente {
            border: 2px solid #fca5a5 !important;
            background: #fef2f2 !important;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15) !important;
        }
        .solicitacao-urgente:hover {
            border-color: #f87171 !important;
            background: #fef2f2 !important;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.25) !important;
        }
        .solicitacao-urgente.solicitacao-concluida {
            border: 2px solid #fca5a5 !important;
            background: #fef2f2 !important;
            opacity: 0.8;
        }


        /* Aviso de pedido urgente no popup */
        .aviso-urgente {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fca5a5;
            border-radius: 8px;
            margin: 15px 20px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
        }
        .aviso-urgente-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .aviso-urgente-icon {
            font-size: 18px;
            animation: pulse-urgente 2s infinite;
        }
        .aviso-urgente-texto {
            color: #991b1b;
            font-weight: 600;
            font-size: 14px;
        }
        @keyframes pulse-urgente {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        /* Responsividade */
        @media (max-width: 900px) {
            .content-container {
                margin: 10px;
                border-radius: 12px;
                padding: 16px;
            }
            .solicitacoes-grid {
                grid-template-columns: 1fr;
            }
            .tab-btn {
                padding: 12px 10px 10px 10px;
                font-size: 15px;
            }
        }
        /* POPUPS MODERNOS */
        .popup-overlay, #produtoPopupOverlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(20, 23, 31, 0.65);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
            transition: background 0.2s;
        }
        /* Overlay de confirmação acima do popup principal */
        .confirm-overlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(20, 23, 31, 0.65);
            z-index: 3000; /* maior que o popup principal */
            display: none;
            justify-content: center;
            align-items: center;
        }
        .confirm-overlay[style*="flex"] {
            display: flex !important;
        }
        .confirm-popup {
            position: relative;
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 1.5px 8px rgba(0,0,0,0.08);
            padding: 36px 32px 28px 32px;
            min-width: 340px;
            max-width: 98vw;
            max-height: 92vh;
            overflow-y: auto;
            z-index: 3100; /* maior que o overlay */
            display: block;
        }
        .popup, .confirm-popup, #produtoPopup {
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 1.5px 8px rgba(0,0,0,0.08);
            padding: 20px 16px 16px 16px;
            min-width: 450px;
            max-width: 650px;
            max-height: 60vh;
            overflow-y: auto;
            z-index: 1100;
            display: none;
        }
        .popup-header {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 24px;
            border-bottom: 1.5px solid #f3f4f6;
            padding-bottom: 12px;
            gap: 0;
        }
        .popup-header-topbar {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 8px;
        }
        .btn-imprimir-azul, .icon-btn-edit {
            background: none;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            margin-right: 0;
            padding: 0;
            transition: background 0.2s;
            cursor: pointer;
        }
        .btn-imprimir-azul:hover, .icon-btn-edit:hover { background: #f3f4f6; }
        .btn-imprimir-azul svg, .icon-btn-edit svg {
            width: 20px;
            height: 20px;
            display: block;
        }
        .popup-header-row {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            position: relative;
            margin-top: 0;
            margin-bottom: 0;
        }
        .popup-header-title {
            text-align: center;
            font-size: 22px;
            color: #111827;
            font-weight: 600;
            margin: 16px auto 0 auto;
            white-space: nowrap;
            line-height: 32px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            right: 0;
            top: -21px;
            z-index: 10;
        }
        .close-btn:hover { background: #f3f4f6; color: #2563eb; }
        .btn-container, .confirm-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
        }
        .btn, .btn-primary, .btn-success, .btn-danger {
            padding: 10px 22px;
            border-radius: 7px;
            border: none;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .btn-primary { background: #2563eb; color: #fff; }
        .btn-primary:hover { background: #1d4ed8; }
        .btn-success { background: #22c55e; color: #fff; }
        .btn-success:hover { background: #16a34a; }
        .btn-danger { background: #ef4444; color: #fff; }
        .btn-danger:hover { background: #b91c1c; }
        .print-btn {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 8px 16px;
            margin-right: 8px;
            cursor: pointer;
            font-size: 15px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: background 0.2s;
        }
        .print-btn:hover { background: #1d4ed8; }
        /* Inputs e tabelas dentro do popup */
        .popup input, .popup textarea, .popup select, #produtoPopup input {
            width: 100%;
            padding: 10px 12px;
            border: 1.5px solid #e5e7eb;
            border-radius: 7px;
            font-size: 15px;
            background: #f9fafb;
            margin-bottom: 10px;
            color: #222;
            transition: border 0.2s;
        }
        .popup input:focus, .popup textarea:focus, .popup select:focus, #produtoPopup input:focus {
            border-color: #2563eb;
            outline: none;
        }
        .popup label, #produtoPopup label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 4px;
        }
        .popup .form-group, #produtoPopup .form-group {
            margin-bottom: 18px;
        }
        .itens-table, .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 18px 0 0 0;
            background: #fff;
            border-radius: 7px;
            overflow: hidden;
            font-size: 14px;
        }
        .itens-table th, .data-table th {
            background: #f3f4f6;
            color: #374151;
            font-weight: 600;
            padding: 10px 12px;
            border: none;
        }
        .itens-table td, .data-table td {
            padding: 10px 12px;
            color: #222;
            border: none;
        }
        .itens-table tr:nth-child(even), .data-table tr:nth-child(even) {
            background: #f9fafb;
        }
        .itens-table tr:last-child td, .data-table tr:last-child td {
            border-bottom: none;
        }
        /* Overlay visível */
        .popup-overlay[style*="block"], #produtoPopupOverlay[style*="block"] {
            display: flex !important;
        }
        .popup[style*="block"], .confirm-popup[style*="block"], #produtoPopup[style*="block"] {
            display: block !important;
        }

        /* Popup de confirmação de exclusão - maior z-index para aparecer na frente */
        #confirmacaoExclusaoOverlay {
            z-index: 2000 !important;
        }
        #confirmacaoExclusaoPopup {
            z-index: 2001 !important;
        }
        /* Confirmação */
        .confirm-popup h3 {
            font-size: 20px;
            color: #111827;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .confirm-popup p, .confirm-popup ul {
            color: #374151;
            font-size: 15px;
            margin-bottom: 10px;
        }
        .confirm-popup ul {
            margin-left: 18px;
        }
        /* Responsivo */
        @media (max-width: 600px) {
            .popup, .confirm-popup, #produtoPopup {
                min-width: 0;
                width: 98vw;
                padding: 18px 6vw 18px 6vw;
            }
            .popup-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        /* Garantir que o popup de confirmação fique centralizado e visível */
        .confirm-overlay[style*="flex"] .confirm-popup {
            display: block !important;
            position: relative;
            left: 0; top: 0;
            transform: none;
            margin: auto;
        }
        .status-badge-concluido {
            display: inline-block;
            background: #22c55e;
            color: #fff;
            font-weight: 600;
            font-size: 14px;
            border-radius: 6px;
            padding: 5px 16px;
            margin-bottom: 18px;
            margin-right: 10px;
            letter-spacing: 1px;
        }
        .status-concluido-top {
            display: block;
            text-align: center;
            color: #22c55e;
            font-weight: 700;
            font-size: 13px;
            letter-spacing: 1px;
            margin-bottom: 2px;
            margin-top: 8px;
            background: none;
            padding: 0;
            border-radius: 0;
        }
        .status-concluido-row {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            margin-top: 10px;
            margin-bottom: 2px;
            gap: 10px;
        }
        .status-concluido-label {
            color: #22c55e;
            font-weight: 700;
            font-size: 13px;
            letter-spacing: 1px;
            background: none;
            padding: 0;
            border-radius: 0;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        /* Estilos modernos para o popup */
        .modern-popup {
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
        }
        
        .modern-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px 16px;
            border-bottom: none;
            background: #fff;
            border-radius: 12px 12px 0 0;
            flex-direction: column;
        }
        
        .modern-header .header-content {
            width: 100%;
        }
        
        .modern-header .header-separator {
            width: 100%;
            height: 1px;
            background: #e5e7eb;
            margin-top: 10px;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-content h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .modern-content {
            padding: 20px 24px 24px;
        }
        
        .status-row {
            background: #10b981;
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .modern-form {
            max-width: 675px;
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 6px;
            letter-spacing: 0.3px;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            background: #ffffff;
            color: #111827;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-group input[readonly],
        .form-group textarea[readonly] {
            background: #f9fafb;
            color: #6b7280;
            cursor: default;
        }
        
        .section-divider {
            margin: 8px 0 21px;
            text-align: center;
        }
        .section-divider .divider {
            width: 100%;
            height: 1px;
            background: #d1d5db;
            margin-bottom: 8px;
        }
        .section-divider .divider-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            display: inline-block;
            background: transparent;
        }
        
        .itens-container {
            margin-bottom: 24px;
        }
        
        .table-wrapper {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            padding: 12px 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
        }
        
        .modern-table th.text-center {
            text-align: center;
        }
        
        .modern-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
        }
        
        .modern-table td.text-center {
            text-align: center;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table input[type="number"] {
            width: 80px;
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
        }
        
        .modern-table input[type="number"]:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }
        
        .estoque-ok {
            color: #059669;
            font-weight: 600;
        }
        
        .estoque-baixo {
            color: #dc2626;
            font-weight: 600;
        }
        
        .modern-btn-container {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .modern-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 10px 18px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }
        
        .modern-btn:hover {
            transform: translateY(-1px);
        }
        
        .modern-btn svg {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
        }
        
        .btn-primary.modern-btn {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary.modern-btn:hover {
            background: #1d4ed8;
        }
        
        .btn-success.modern-btn {
            background: #10b981;
            color: white;
        }
        
        .btn-success.modern-btn:hover {
            background: #059669;
        }
        
        .btn-danger.modern-btn {
            background: #dc2626;
            color: white;
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .btn-danger.modern-btn:hover {
            background: #b91c1c;
        }
        
        .imagem-container {
            margin: 20px 0;
            text-align: center;
        }
        
        .imagem-container h4 {
            margin-bottom: 12px;
            color: #111827;
            font-weight: 600;
        }
        
        .icon-btn-print,
        .icon-btn-edit {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }
        
        .icon-btn-print:hover,
        .icon-btn-edit:hover {
            background: rgba(37, 99, 235, 0.1);
            color: #2563eb;
        }
        
        .icon-btn-print svg,
        .icon-btn-edit svg {
            width: 18px;
            height: 18px;
        }
        .tab-btn:hover {
            background: #fff !important;
            color: #2563eb;
        }
    </style>

    <script>
        // Função essencial para mostrar detalhes - definida no head para estar disponível nos onclick
        function mostrarDetalhes(tipo, codigo) {
            try {
                // Aguardar o DOM estar carregado se necessário
                if (typeof window.mostrarDetalhesCompleta === 'function') {
                    window.mostrarDetalhesCompleta(tipo, codigo);
                } else {
                    // Se a função completa ainda não foi carregada, aguardar
                    setTimeout(function() {
                        if (typeof window.mostrarDetalhesCompleta === 'function') {
                            window.mostrarDetalhesCompleta(tipo, codigo);
                        } else {
                            console.error('Função mostrarDetalhesCompleta não encontrada');
                        }
                    }, 100);
                }
            } catch (error) {
                console.error('Erro ao mostrar detalhes:', error);
            }
        }

        // Função essencial para mostrar abas
        function mostrarAba(aba) {
            try {
                if (typeof window.mostrarAbaCompleta === 'function') {
                    window.mostrarAbaCompleta(aba);
                } else {
                    setTimeout(function() {
                        if (typeof window.mostrarAbaCompleta === 'function') {
                            window.mostrarAbaCompleta(aba);
                        } else {
                            console.error('Função mostrarAbaCompleta não encontrada');
                        }
                    }, 100);
                }
            } catch (error) {
                console.error('Erro ao mostrar aba:', error);
            }
        }
    </script>
</head>
<body>
    <audio id="notificacao-audio" src="assets/notificacao.mp3" preload="auto"></audio>
    <?php include 'topbar.php'; ?>
    
    <div class="content-container">
        <!-- Pop-up de feedback -->
        <?php if (!empty($feedbackMessage)): ?>
        <div id="feedback-popup" class="<?= $feedbackType ?>">
            <?= $feedbackMessage ?>
        </div>
        <?php endif; ?>
        
        <div class="header-container">
            <h1>Todas as Solicitações de Estoque</h1>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros" onclick="toggleFilterDropdown()" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;"><i class="fas fa-filter"></i></button>
        </div>
        
        <!-- Dropdown de filtro -->
        <div class="filter-dropdown" id="filterDropdown">
            <div class="filter-option" onclick="filtrarPorTipo('todos')">Todos os tipos</div>
            <div class="filter-option" onclick="filtrarPorTipo('requisicao')">Requisições</div>
            <div class="filter-option" onclick="filtrarPorTipo('pedido_mensal')">Pedidos Mensais</div>
            <div class="filter-option" onclick="filtrarPorTipo('pedido_especial')">Pedidos Especiais</div>
        </div>
        
        <!-- Abas para pendentes e concluídas -->
        <div class="tabs-container">
            <button class="tab-btn active" id="tab-pendentes" onclick="mostrarAba('pendentes')">Solicitações Pendentes</button>
            <button class="tab-btn" id="tab-concluidas" onclick="mostrarAba('concluidas')">Solicitações Concluídas</button>
        </div>
        <div class="tab-content active" id="conteudo-pendentes">
            <div class="solicitacoes-grid" id="solicitacoes-pendentes">
                <?php foreach (
                    $solicitacoes_pendentes as $solicitacao): 
                    $tipo = $solicitacao['tipo'];
                    $tipoClass = 'tipo-' . $tipo;
                    $tipoTexto = '';
                    switch($tipo) {
                        case 'requisicao': $tipoTexto = 'Requisição'; break;
                        case 'pedido_mensal': $tipoTexto = 'Pedido Mensal'; break;
                        case 'pedido_especial': $tipoTexto = 'Pedido Especial'; break;
                    }
                    $dataFormatada = isset($solicitacao['data_solicitacao']) ?
                        date('d/m/Y', strtotime($solicitacao['data_solicitacao'])) :
                        'Data não disponível';

                    // Verificar se é pedido urgente
                    $isUrgente = isset($solicitacao['pedido_urgente']) && $solicitacao['pedido_urgente'] == 1;
                    $classeUrgente = $isUrgente ? ' solicitacao-urgente' : '';
                ?>
                    <div class="solicitacao-card<?= $classeUrgente ?>" data-tipo="<?= $tipo ?>" data-codigo="<?= $solicitacao['codigo'] ?>" onclick="mostrarDetalhes('<?= $tipo ?>', <?= $solicitacao['codigo'] ?>)">
                        <h3>
                            #<?= $solicitacao['codigo'] ?>
                            <span class="tipo-badge <?= $tipoClass ?>"><?= $tipoTexto ?></span>
                        </h3>
                        <p><strong>Solicitante:</strong> <?= $solicitacao['solicitante'] ?></p>
                        <p><strong>Empresa:</strong> <?= $solicitacao['empresa'] ?></p>
                        <p><strong>Finalidade:</strong> <?= $solicitacao['finalidade'] ?></p>
                        <p class="data"><?= $dataFormatada ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <div class="tab-content" id="conteudo-concluidas">
            <div class="solicitacoes-grid" id="solicitacoes-concluidas">
                <?php foreach (
                    $solicitacoes_concluidas as $solicitacao): 
                    $tipo = $solicitacao['tipo'];
                    $tipoClass = 'tipo-' . $tipo;
                    $tipoTexto = '';
                    switch($tipo) {
                        case 'requisicao': $tipoTexto = 'Requisição'; break;
                        case 'pedido_mensal': $tipoTexto = 'Pedido Mensal'; break;
                        case 'pedido_especial': $tipoTexto = 'Pedido Especial'; break;
                    }
                    $dataFormatada = isset($solicitacao['data_solicitacao']) ?
                        date('d/m/Y', strtotime($solicitacao['data_solicitacao'])) :
                        'Data não disponível';

                    // Verificar se é pedido urgente
                    $isUrgente = isset($solicitacao['pedido_urgente']) && $solicitacao['pedido_urgente'] == 1;
                    $classeUrgente = $isUrgente ? ' solicitacao-urgente' : '';
                ?>
                    <div class="solicitacao-card solicitacao-concluida<?= $classeUrgente ?>" data-tipo="<?= $tipo ?>" data-codigo="<?= $solicitacao['codigo'] ?>" onclick="mostrarDetalhes('<?= $tipo ?>', <?= $solicitacao['codigo'] ?>)">
                        <h3>
                            #<?= $solicitacao['codigo'] ?>
                            <span class="tipo-badge <?= $tipoClass ?>"><?= $tipoTexto ?></span>
                        </h3>
                        <p><strong>Solicitante:</strong> <?= $solicitacao['solicitante'] ?></p>
                        <p><strong>Empresa:</strong> <?= $solicitacao['empresa'] ?></p>
                        <p><strong>Finalidade:</strong> <?= $solicitacao['finalidade'] ?></p>
                        <p class="data"><?= $dataFormatada ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- Pop-up de detalhes da solicitação -->
    <div id="popupOverlay" class="popup-overlay" onclick="fecharPopup()"></div>
    <div id="popup" class="popup modern-popup">
        <div class="popup-header modern-header">
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <h2 style="margin: 0;">Detalhes da Solicitação</h2>
                <div class="header-actions">
                    <button class="icon-btn-print" id="btn-imprimir-azul" title="Imprimir" onclick="imprimirSolicitacao()" style="display:none">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 6,2 18,2 18,9"></polyline>
                            <path d="M6,18H4a2,2 0 0,1-2-2V11a2,2 0 0,1,2-2H20a2,2 0 0,1,2,2v5a2,2 0 0,1-2,2h-2"></path>
                            <polyline points="6,14 6,22 18,22 18,14"></polyline>
                        </svg>
                    </button>
                    <button class="icon-btn-edit" id="btn-editar-solicitacao" title="Editar Solicitação" onclick="habilitarEdicaoSolicitacao()" style="display:none">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <button class="close-btn" onclick="fecharPopup()" title="Fechar">&times;</button>
            <div class="header-separator"></div>
        </div>

        <!-- Aviso de Pedido Urgente -->
        <div id="aviso-pedido-urgente" class="aviso-urgente" style="display: none;">
            <div class="aviso-urgente-content">
                <span class="aviso-urgente-icon">🚨</span>
                <span class="aviso-urgente-texto">Pedido urgente após valor limite excedido</span>
            </div>
        </div>
        
        <div class="popup-content modern-content">
            <!-- Status de concluído -->
            <div id="status-concluido-row" class="status-row" style="display:none">
                <span id="status-concluido-label" class="status-label"></span>
            </div>
            
            <form id="formDetalhes" class="modern-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="tipo_solicitacao">Tipo:</label>
                        <input type="text" id="tipo_solicitacao" readonly>
                    </div>
                    <div class="form-group">
                        <label for="codigo_solicitacao">Código:</label>
                        <input type="text" id="codigo_solicitacao" readonly>
                    </div>

                    <div class="form-group">
                        <label for="solicitante">Solicitante:</label>
                        <input type="text" id="solicitante" readonly>
                    </div>
                    <div class="form-group">
                        <label for="empresa">Empresa:</label>
                        <input type="text" id="empresa" readonly>
                    </div>
                    <div class="form-group">
                        <label for="contrato">Contrato:</label>
                        <input type="text" id="contrato" readonly>
                    </div>
                    <div class="form-group">
                        <label for="finalidade">Finalidade:</label>
                        <input type="text" id="finalidade" readonly>
                    </div>
                    <div class="form-group" id="funcionario_row">
                        <label for="funcionario">Funcionário/Destinatário:</label>
                        <input type="text" id="funcionario" readonly>
                    </div>
                    <div class="form-group" id="setor_row">
                        <label for="setor">Setor:</label>
                        <input type="text" id="setor" readonly>
                    </div>
                    <div class="form-group" id="funcao_row">
                        <label for="funcao">Função:</label>
                        <input type="text" id="funcao" readonly>
                    </div>
                    <div class="form-group full-width">
                        <label for="observacao">Observação:</label>
                        <textarea id="observacao" readonly rows="3"></textarea>
                    </div>
                </div>
                
                <!-- Container de imagem para pedidos especiais -->
                <div id="imagem_container" class="imagem-container" style="display:none">
                    <h4>Imagem do Pedido</h4>
                    <img id="imagem_pedido" src="" alt="Imagem do pedido" style="max-width: 100%; max-height: 300px; border-radius: 8px;">
                </div>
                
                <!-- Divisão antes dos itens -->
                <div class="section-divider">
                    <div class="divider"></div>
                    <div class="divider-title">Itens da Solicitação</div>
                </div>
                
                <!-- Container para itens da solicitação -->
                <div id="itens_container" class="itens-container">
                    <div class="table-wrapper">
                        <table id="itensTable" class="modern-table">
                            <thead>
                                <tr></tr>
                            </thead>
                            <tbody id="itensTableBody">
                                <!-- Itens serão adicionados aqui via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    <button type="button" class="btn btn-primary modern-btn" id="btn-adicionar-item" style="display:none; padding: 8px 16px; font-size: 13px; margin-top: 12px;" onclick="abrirPopupProdutos()">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Adicionar Item
                    </button>
                </div>
            </form>
            
            <div class="btn-container modern-btn-container">
                <button type="button" class="btn btn-primary modern-btn" id="btn-salvar-alteracoes" style="display:none" onclick="salvarAlteracoes()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                        <polyline points="7,3 7,8 15,8"></polyline>
                    </svg>
                    Salvar Alterações
                </button>
                <button type="button" class="btn btn-success modern-btn" id="btn-concluir" onclick="confirmarConclusao()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20,6 9,17 4,12"></polyline>
                    </svg>
                    Marcar como Concluído
                </button>
                <button type="button" class="btn btn-danger modern-btn" id="btn-cancelar-solicitacao" style="display:none; margin-left: 8px;" onclick="abrirPopupCancelarSolicitacao()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                    Cancelar Solicitação
                </button>
                <button type="button" class="btn btn-danger modern-btn" id="btn-excluir-urgente" style="display:none; margin-left: 8px;" onclick="confirmarExclusaoUrgente()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="3,6 5,6 21,6"></polyline>
                        <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                    Excluir Pedido Urgente
                </button>
            </div>
        </div>
    </div>
    
    <!-- Pop-up de confirmação de conclusão -->
    <div id="confirmOverlay" class="confirm-overlay">
        <div class="confirm-popup">
            <button class="close-btn" onclick="document.getElementById('confirmOverlay').style.display='none'" title="Fechar">&times;</button>
            <h3>Confirmar Conclusão</h3>
            <p>Tem certeza que deseja marcar esta solicitação como concluída? Esta ação irá:</p>
            <ul style="text-align: left;">
                <li>Subtrair os itens do estoque</li>
                <li>Marcar a solicitação como concluída</li>
            </ul>
            <div class="confirm-buttons">
                <button id="confirmConcluir" class="btn btn-success">Sim, Concluir</button>
                <button id="cancelConcluir" class="btn btn-danger">Não, Voltar</button>
            </div>
        </div>
    </div>
    
    <!-- Pop-up de confirmação de exclusão -->
    <div id="confirmacaoExclusaoOverlay" class="popup-overlay" onclick="fecharConfirmacaoExclusao()"></div>
    <div id="confirmacaoExclusaoPopup" class="popup modern-popup" style="max-width: 500px;">
        <div class="popup-header modern-header">
            <div class="header-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <h2 style="margin: 0; color: #dc3545;">Confirmar Exclusão</h2>
            </div>
            <button class="close-btn" onclick="fecharConfirmacaoExclusao()" title="Fechar">&times;</button>
            <div class="header-separator"></div>
        </div>

        <div class="popup-content modern-content">
            <div style="text-align: center; padding: 20px 0;">
                <h3 style="color: #dc3545; margin-bottom: 15px;">ATENÇÃO!</h3>
                <p style="font-size: 16px; line-height: 1.5; margin-bottom: 20px; color: #333;">
                    Você está prestes a <strong>EXCLUIR permanentemente</strong> este pedido urgente.
                </p>
                <p style="font-size: 14px; color: #666; margin-bottom: 25px;">
                    Esta ação <strong>NÃO pode ser desfeita!</strong>
                </p>

                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button type="button" class="btn btn-secondary modern-btn" onclick="fecharConfirmacaoExclusao()" style="min-width: 120px;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-danger modern-btn" onclick="confirmarExclusaoDefinitiva()" style="min-width: 120px;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 8px;">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                        Sim, Excluir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pop-up para selecionar produtos -->
    <div id="produtoPopupOverlay" class="popup-overlay" onclick="fecharPopupProdutos()"></div>
    <div id="produtoPopup" class="popup">
        <div class="popup-header">
            <h2>Selecionar Produto</h2>
            <button class="close-btn" onclick="fecharPopupProdutos()">&times;</button>
        </div>
        
        <div class="search-container">
            <input type="text" id="pesquisaProduto" placeholder="Pesquisar produto..." onkeyup="filtrarProdutos()">
        </div>
        
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome</th>
                        <th>Estoque</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="produtosTableBody">
                    <!-- Produtos serão adicionados aqui via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pop-up de confirmação de cancelamento -->
    <div id="cancelarSolicitacaoOverlay" class="confirm-overlay" style="z-index:4000; display:none;">
        <div class="confirm-popup">
            <button class="close-btn" onclick="fecharPopupCancelarSolicitacao()" title="Fechar">&times;</button>
            <h3>Confirmar Cancelamento</h3>
            <p>Tem certeza que deseja cancelar esta solicitação? Esta ação irá desfazer a retirada do estoque e remover lançamentos de EPI (se houver).</p>
            <div class="confirm-buttons">
                <button id="confirmCancelarSolicitacao" class="btn btn-danger">Sim, Cancelar</button>
                <button id="cancelCancelarSolicitacao" class="btn btn-secondary">Não, Voltar</button>
            </div>
        </div>
    </div>

    <!-- Pop-up de erro de cancelamento de EPI assinado -->
    <div id="erroCancelarSolicitacaoOverlay" class="confirm-overlay" style="z-index:5000; display:none;">
        <div class="confirm-popup">
            <button class="close-btn" onclick="fecharPopupErroCancelarSolicitacao()" title="Fechar">&times;</button>
            <h3>Não é possível cancelar</h3>
            <p id="erroCancelarSolicitacaoMsg">Um ou mais itens já foram confirmados por assinatura na ficha de EPI.</p>
            <div class="confirm-buttons">
                <button class="btn btn-secondary" onclick="fecharPopupErroCancelarSolicitacao()">Fechar</button>
            </div>
        </div>
    </div>

    <script>
        // Variáveis globais
        let itemEditIndex = -1;
        let solicitacaoAtual = null;
        let itensSolicitacao = [];

        // Adicionar controle de edição para exibir apenas o botão de salvar
        let houveEdicao = false;
        let emEdicao = false;

        // Definir funções essenciais imediatamente para disponibilizar globalmente
        function mostrarDetalhesCompleta(tipo, codigo) {
            // Garantir que botões de edição estejam ocultos ao abrir detalhes
            emEdicao = false;
            document.getElementById('btn-adicionar-item').style.display = 'none';
            document.getElementById('btn-salvar-alteracoes').style.display = 'none';
            atualizarTabelaItens();

            // Definir o endpoint correto com base no tipo
            let endpoint = '';
            switch(tipo) {
                case 'requisicao':
                    endpoint = 'obter-detalhes-requisicao.php';
                    break;
                case 'pedido_mensal':
                    endpoint = 'obter-detalhes-pedido-mensal.php';
                    break;
                case 'pedido_especial':
                    endpoint = 'obter-detalhes-pedido-especial.php';
                    break;
            }

            // Esconder/mostrar itens da solicitação conforme o tipo
            if (tipo === 'pedido_especial') {
                if (document.querySelector('.section-divider')) document.querySelector('.section-divider').style.display = 'none';
                if (document.getElementById('itens_container')) document.getElementById('itens_container').style.display = 'none';
            } else {
                if (document.querySelector('.section-divider')) document.querySelector('.section-divider').style.display = '';
                if (document.getElementById('itens_container')) document.getElementById('itens_container').style.display = '';
            }

            // Fazer uma requisição AJAX para obter os detalhes da solicitação
            fetch(`${endpoint}?codigo=${codigo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Armazenar dados da solicitação atual
                        solicitacaoAtual = {
                            tipo: tipo,
                            codigo: codigo,
                            status: data.requisicao?.status || data.pedido?.status || 'pendente'
                        };

                        // Verificar se é pedido urgente e mostrar aviso
                        const detalhes = tipo === 'requisicao' ? data.requisicao : data.pedido;
                        const avisoUrgente = document.getElementById('aviso-pedido-urgente');
                        const btnExcluirUrgente = document.getElementById('btn-excluir-urgente');

                        // Para requisições, usar requisicao_urgente; para pedidos, usar pedido_urgente
                        const isUrgente = tipo === 'requisicao' ? (detalhes.requisicao_urgente == 1) : (detalhes.pedido_urgente == 1);

                        if (isUrgente) {
                            avisoUrgente.style.display = 'block';
                        } else {
                            avisoUrgente.style.display = 'none';
                        }

                        // Preencher o formulário com os dados
                        document.getElementById('tipo_solicitacao').value = tipo;
                        document.getElementById('codigo_solicitacao').value = tipo === 'requisicao' ? data.requisicao.codigo_solicitacao : data.pedido.codigo_pedido;

                        // Usar a variável detalhes já declarada acima

                        document.getElementById('solicitante').value = detalhes.solicitante;
                        document.getElementById('empresa').value = detalhes.empresa;
                        document.getElementById('contrato').value = detalhes.contrato;
                        document.getElementById('finalidade').value = detalhes.finalidade;
                        document.getElementById('setor').value = detalhes.setor || '';

                        // Esconder o container de imagem por padrão
                        if (document.getElementById('imagem_container')) {
                            document.getElementById('imagem_container').style.display = 'none';
                        }

                        // Configurar campos específicos por tipo
                        if (tipo === 'requisicao') {
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = '';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = '';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = '';
                            }
                            document.getElementById('funcionario').value = detalhes.nome_funcionario || '';
                            document.getElementById('setor').value = detalhes.setor_funcionario || '';
                            document.getElementById('funcao').value = detalhes.funcao || '';
                            document.getElementById('observacao').value = detalhes.observacao || '';
                        } else if (tipo === 'pedido_mensal') {
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = '';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = '';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = '';
                            }
                            document.getElementById('funcionario').value = detalhes.destinatario || '';
                            document.getElementById('setor').value = detalhes.setor_destinatario || '';
                            document.getElementById('funcao').value = detalhes.funcao || '';
                            document.getElementById('observacao').value = detalhes.observacao || '';
                        } else { // pedido_especial
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = 'none';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = 'none';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = 'none';
                            }
                            document.getElementById('observacao').value = detalhes.detalhes || '';

                            // Verificar se existe uma imagem
                            if (detalhes.imagem_path && detalhes.imagem_path !== 'null' && detalhes.imagem_path !== '') {
                                if (document.getElementById('imagem_container')) {
                                    document.getElementById('imagem_container').style.display = 'block';
                                    document.getElementById('imagem_pedido').src = detalhes.imagem_path;
                                }
                            }
                        }

                        // Buscar informações de estoque para cada item
                        if (data.itens && data.itens.length > 0) {
                            // Criar um array de promessas para buscar o estoque de cada produto
                            const estoquePromises = data.itens.map(item => {
                                return fetch(`obter-estoque.php?codigo=${item.produto}`)
                                    .then(response => response.json());
                            });

                            Promise.all(estoquePromises)
                                .then(estoqueResults => {
                                    // Combinar dados dos itens com informações de estoque
                                    itensSolicitacao = data.itens.map((item, index) => {
                                        const estoqueData = estoqueResults[index];
                                        return {
                                            produto: item.produto,
                                            nome_produto: item.nome_produto || 'Nome não disponível',
                                            quantidade: parseInt(item.quantidade),
                                            estoque: estoqueData.success ? estoqueData.estoque : 0
                                        };
                                    });

                                    // Atualizar a tabela de itens
                                    atualizarTabelaItens();
                                })
                                .catch(error => {
                                    console.error('Erro ao buscar estoque:', error);
                                    // Em caso de erro, continuar com os itens sem informação de estoque
                                    itensSolicitacao = data.itens.map(item => {
                                        return {
                                            produto: item.produto,
                                            nome_produto: item.nome_produto || 'Nome não disponível',
                                            quantidade: parseInt(item.quantidade),
                                            estoque: 0
                                        };
                                    });

                                    // Atualizar a tabela de itens
                                    atualizarTabelaItens();
                                });
                        } else {
                            itensSolicitacao = [];
                            atualizarTabelaItens();
                        }

                        // Exibir o popup
                        document.getElementById('popup').style.display = 'block';
                        document.getElementById('popupOverlay').style.display = 'block';
                        atualizarStatusConcluidoPopup(tipo, solicitacaoAtual.status);
                        atualizarBotaoCancelarSolicitacao();
                        atualizarBotaoExcluirUrgente();
                    } else {
                        mostrarFeedback('Erro ao carregar detalhes: ' + (data.message || 'Erro desconhecido'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    mostrarFeedback('Erro ao processar a solicitação', 'error');
                });
        }

        function mostrarAbaCompleta(qual) {
            document.getElementById('tab-pendentes').classList.remove('active');
            document.getElementById('tab-concluidas').classList.remove('active');
            document.getElementById('conteudo-pendentes').classList.remove('active');
            document.getElementById('conteudo-concluidas').classList.remove('active');
            if (qual === 'pendentes') {
                document.getElementById('tab-pendentes').classList.add('active');
                document.getElementById('conteudo-pendentes').classList.add('active');
            } else {
                document.getElementById('tab-concluidas').classList.add('active');
                document.getElementById('conteudo-concluidas').classList.add('active');
            }
        }

        // Tornar as funções disponíveis globalmente imediatamente
        window.mostrarDetalhesCompleta = mostrarDetalhesCompleta;
        window.mostrarAbaCompleta = mostrarAbaCompleta;

        function marcarComoEditado() {
            houveEdicao = true;
            // Esconder botão de concluir
            document.getElementById('btn-concluir').style.display = 'none';
            // Mostrar botão salvar
            let btnSalvar = document.querySelector('.btn-container .btn.btn-primary');
            if (btnSalvar) btnSalvar.style.display = 'inline-block';
        }
        
        // Função para alternar o dropdown de filtro
        function toggleFilterDropdown() {
            const dropdown = document.getElementById('filterDropdown');
            const isVisible = dropdown.style.display === 'block';
            
            if (isVisible) {
                dropdown.style.display = 'none';
            } else {
                dropdown.style.display = 'block';
            }
        }
        
        // Função para filtrar por tipo
        function filtrarPorTipo(tipo) {
            const cards = document.querySelectorAll('.solicitacao-card');
            
            cards.forEach(card => {
                if (tipo === 'todos' || card.getAttribute('data-tipo') === tipo) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Fechar o dropdown após selecionar
            document.getElementById('filterDropdown').style.display = 'none';
        }
        
        // Fechar dropdown quando clicar fora
        document.addEventListener('click', function(event) {
            const filterBtn = document.getElementById('filterBtn');
            const filterDropdown = document.getElementById('filterDropdown');
            
            if (!filterBtn.contains(event.target) && !filterDropdown.contains(event.target)) {
                filterDropdown.style.display = 'none';
            }
        });
        
        // Função completa para mostrar detalhes
        function mostrarDetalhesCompleta(tipo, codigo) {
            // Garantir que botões de edição estejam ocultos ao abrir detalhes
            emEdicao = false;
            document.getElementById('btn-adicionar-item').style.display = 'none';
            document.getElementById('btn-salvar-alteracoes').style.display = 'none';
            atualizarTabelaItens();
            
            // Definir o endpoint correto com base no tipo
            let endpoint = '';
            switch(tipo) {
                case 'requisicao':
                    endpoint = 'obter-detalhes-requisicao.php';
                    break;
                case 'pedido_mensal':
                    endpoint = 'obter-detalhes-pedido-mensal.php';
                    break;
                case 'pedido_especial':
                    endpoint = 'obter-detalhes-pedido-especial.php';
                    break;
            }
            
            // Esconder/mostrar itens da solicitação conforme o tipo
            if (tipo === 'pedido_especial') {
                if (document.querySelector('.section-divider')) document.querySelector('.section-divider').style.display = 'none';
                if (document.getElementById('itens_container')) document.getElementById('itens_container').style.display = 'none';
            } else {
                if (document.querySelector('.section-divider')) document.querySelector('.section-divider').style.display = '';
                if (document.getElementById('itens_container')) document.getElementById('itens_container').style.display = '';
            }
            
            // Fazer uma requisição AJAX para obter os detalhes da solicitação
            fetch(`${endpoint}?codigo=${codigo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Armazenar dados da solicitação atual
                        solicitacaoAtual = {
                            tipo: tipo,
                            codigo: codigo,
                            status: data.requisicao?.status || data.pedido?.status || 'pendente'
                        };

                        // Verificar se é pedido urgente e mostrar aviso
                        const detalhes = tipo === 'requisicao' ? data.requisicao : data.pedido;
                        const avisoUrgente = document.getElementById('aviso-pedido-urgente');
                        const btnExcluirUrgente = document.getElementById('btn-excluir-urgente');

                        // Para requisições, usar requisicao_urgente; para pedidos, usar pedido_urgente
                        const isUrgente = tipo === 'requisicao' ? (detalhes.requisicao_urgente == 1) : (detalhes.pedido_urgente == 1);

                        if (isUrgente) {
                            avisoUrgente.style.display = 'block';
                        } else {
                            avisoUrgente.style.display = 'none';
                        }
                        
                        // Preencher o formulário com os dados
                        document.getElementById('tipo_solicitacao').value = tipo;
                        document.getElementById('codigo_solicitacao').value = tipo === 'requisicao' ? data.requisicao.codigo_solicitacao : data.pedido.codigo_pedido;

                        // Usar a variável detalhes já declarada acima
                        
                        document.getElementById('solicitante').value = detalhes.solicitante;
                        document.getElementById('empresa').value = detalhes.empresa;
                        document.getElementById('contrato').value = detalhes.contrato;
                        document.getElementById('finalidade').value = detalhes.finalidade;
                        document.getElementById('setor').value = detalhes.setor || '';
                        
                        // Esconder o container de imagem por padrão
                        if (document.getElementById('imagem_container')) {
                            document.getElementById('imagem_container').style.display = 'none';
                        }
                        
                        // Configurar campos específicos por tipo
                        if (tipo === 'requisicao') {
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = '';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = '';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = '';
                            }
                            document.getElementById('funcionario').value = detalhes.nome_funcionario || '';
                            document.getElementById('setor').value = detalhes.setor_funcionario || '';
                            document.getElementById('funcao').value = detalhes.funcao || '';
                            document.getElementById('observacao').value = detalhes.observacao || '';
                        } else if (tipo === 'pedido_mensal') {
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = '';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = '';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = '';
                            }
                            document.getElementById('funcionario').value = detalhes.destinatario || '';
                            document.getElementById('setor').value = detalhes.setor_destinatario || '';
                            document.getElementById('funcao').value = detalhes.funcao || '';
                            document.getElementById('observacao').value = detalhes.observacao || '';
                        } else { // pedido_especial
                            if (document.getElementById('funcionario_row')) {
                                document.getElementById('funcionario_row').style.display = 'none';
                            }
                            if (document.getElementById('setor_row')) {
                                document.getElementById('setor_row').style.display = 'none';
                            }
                            if (document.getElementById('funcao_row')) {
                                document.getElementById('funcao_row').style.display = 'none';
                            }
                            document.getElementById('observacao').value = detalhes.detalhes || '';
                            
                            // Verificar se existe uma imagem
                            if (detalhes.imagem_path && detalhes.imagem_path !== 'null' && detalhes.imagem_path !== '') {
                                if (document.getElementById('imagem_container')) {
                                    document.getElementById('imagem_container').style.display = 'block';
                                    document.getElementById('imagem_pedido').src = detalhes.imagem_path;
                                }
                            }
                        }
                        
                        // Buscar informações de estoque para cada item
                        if (data.itens && data.itens.length > 0) {
                            // Criar um array de promessas para buscar o estoque de cada produto
                            const estoquePromises = data.itens.map(item => {
                                return fetch(`obter-estoque.php?codigo=${item.produto}`)
                                    .then(response => response.json());
                            });
                            
                            // Aguardar todas as promessas serem resolvidas
                            Promise.all(estoquePromises)
                                .then(resultados => {
                                    // Atualizar os itens com as informações de estoque
                                    itensSolicitacao = data.itens.map((item, index) => {
                                        return {
                                            produto: item.produto,
                                            nome_produto: item.nome_produto || 'Nome não disponível',
                                            quantidade: parseInt(item.quantidade),
                                            estoque: resultados[index].success ? parseInt(resultados[index].quantidade) : 0
                                        };
                                    });
                                    
                                    // Atualizar a tabela de itens
                                    atualizarTabelaItens();
                                })
                                .catch(error => {
                                    console.error('Erro ao buscar estoque:', error);
                                    // Em caso de erro, continuar com os itens sem informação de estoque
                                    itensSolicitacao = data.itens.map(item => {
                                        return {
                                            produto: item.produto,
                                            nome_produto: item.nome_produto || 'Nome não disponível',
                                            quantidade: parseInt(item.quantidade),
                                            estoque: 0
                                        };
                                    });
                                    
                                    // Atualizar a tabela de itens
                                    atualizarTabelaItens();
                                });
                        } else {
                            itensSolicitacao = [];
                            atualizarTabelaItens();
                        }
                        
                        // Exibir o popup
                        document.getElementById('popup').style.display = 'block';
                        document.getElementById('popupOverlay').style.display = 'block';
                        atualizarStatusConcluidoPopup(tipo, solicitacaoAtual.status);
                        atualizarBotaoCancelarSolicitacao();
                        atualizarBotaoExcluirUrgente();
                    } else {
                        mostrarFeedback('Erro ao carregar detalhes: ' + (data.message || 'Erro desconhecido'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    mostrarFeedback('Erro ao processar a solicitação', 'error');
                });
        }

        // Função duplicada removida - usando a definição do início

        // Função para atualizar a tabela de itens
        function atualizarTabelaItens() {
            const itensTableBody = document.getElementById('itensTableBody');
            const thead = document.querySelector('#itensTable thead');
            // Remove todos os <tr> do thead
            while (thead.firstChild) {
                thead.removeChild(thead.firstChild);
            }
            // Cria um novo <tr> para o cabeçalho
            const tr = document.createElement('tr');
            if (emEdicao) {
                tr.innerHTML = `
                    <th class="text-center">Código</th>
                    <th>Produto</th>
                    <th>Quantidade</th>
                    <th class="text-center">Estoque</th>
                    <th class="text-center">Ações</th>
                `;
            } else {
                tr.innerHTML = `
                    <th class="text-center">Código</th>
                    <th>Produto</th>
                    <th>Quantidade</th>
                    <th class="text-center">Estoque</th>
                `;
            }
            thead.appendChild(tr);
            itensTableBody.innerHTML = '';
            if (itensSolicitacao.length > 0) {
                itensSolicitacao.forEach((item, index) => {
                    const row = document.createElement('tr');
                    const estoqueClass = item.estoque >= item.quantidade ? 'estoque-ok' : 'estoque-baixo';
                    let acoesCol = '';
                    if (emEdicao) {
                        acoesCol = `<td class="text-center">
                            <button type="button" class="btn btn-danger btn-sm modern-btn" onclick="removerItem(${index})" title="Remover item">Excluir</button>
                        </td>`;
                    }
                    row.innerHTML = `
                        <td class="text-center">${item.produto}</td>
                        <td>${item.nome_produto || 'Nome não disponível'}</td>
                        <td>${emEdicao ? `<input type='number' min='1' value='${item.quantidade}' onchange='atualizarQuantidade(${index}, this.value); marcarComoEditado();'>` : item.quantidade}</td>
                        <td class='text-center ${estoqueClass}'>${item.estoque !== undefined ? item.estoque : 'N/A'}</td>
                        ${acoesCol}
                    `;
                    itensTableBody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = `<td colspan='${emEdicao ? 5 : 4}' class="text-center">Nenhum item encontrado</td>`;
                itensTableBody.appendChild(row);
            }
        }

        // Função para atualizar a quantidade de um item
        function atualizarQuantidade(index, novaQuantidade) {
            if (index >= 0 && index < itensSolicitacao.length) {
                itensSolicitacao[index].quantidade = parseInt(novaQuantidade);
            }
        }
        
        // Função para adicionar novo item
        function adicionarNovoItem() {
            // Verificar se a solicitação está concluída
            if (solicitacaoAtual && solicitacaoAtual.status === 'concluido') {
                mostrarFeedback('Não é possível adicionar itens a uma solicitação concluída', 'error');
                return;
            }
            
            // Abrir modal ou formulário para adicionar novo item
            // Aqui você pode implementar a lógica para abrir um modal ou formulário
            // para o usuário selecionar um produto e informar a quantidade
            
            // Exemplo simples (pode ser substituído por um modal mais elaborado):
            const codigo = prompt('Digite o código do produto:');
            if (!codigo) return;
            
            const nome = prompt('Digite o nome do produto:');
            if (!nome) return;
            
            const quantidade = parseInt(prompt('Digite a quantidade:'));
            if (isNaN(quantidade) || quantidade <= 0) {
                mostrarFeedback('Quantidade inválida', 'error');
                return;
            }
            
            // Verificar estoque antes de adicionar
            fetch(`obter-estoque.php?codigo=${codigo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Adicionar o item à lista
                        itensSolicitacao.push({
                            produto: codigo,
                            nome_produto: nome,
                            quantidade: quantidade,
                            estoque: parseInt(data.quantidade)
                        });
                        
                        // Atualizar a tabela
                        atualizarTabelaItens();
                        
                        mostrarFeedback('Item adicionado com sucesso', 'success');
                    } else {
                        mostrarFeedback('Produto não encontrado no estoque', 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    mostrarFeedback('Erro ao verificar estoque', 'error');
                });
        }
        
        // Função para editar um item
        function editarItem(index) {
            // Verificar se a solicitação está concluída
            if (solicitacaoAtual && solicitacaoAtual.status === 'concluido') {
                mostrarFeedback('Não é possível editar itens de uma solicitação concluída', 'error');
                return;
            }
            
            if (index >= 0 && index < itensSolicitacao.length) {
                const item = itensSolicitacao[index];
                
                // Exemplo simples (pode ser substituído por um modal mais elaborado):
                const novaQuantidade = parseInt(prompt(`Digite a nova quantidade para ${item.nome_produto}:`, item.quantidade));
                
                if (!isNaN(novaQuantidade) && novaQuantidade > 0) {
                    // Atualizar a quantidade
                    itensSolicitacao[index].quantidade = novaQuantidade;
                    
                    // Atualizar a tabela
                    atualizarTabelaItens();
                    
                    mostrarFeedback('Item atualizado com sucesso', 'success');
                } else {
                    mostrarFeedback('Quantidade inválida', 'error');
                }
            }
        }

        // Função para remover um item
        function removerItem(index) {
            // Verificar se a solicitação está concluída
            if (solicitacaoAtual && solicitacaoAtual.status === 'concluido') {
                mostrarFeedback('Não é possível remover itens de uma solicitação concluída', 'error');
                return;
            }
            
            if (index >= 0 && index < itensSolicitacao.length) {
                if (confirm(`Tem certeza que deseja remover o item ${itensSolicitacao[index].nome_produto}?`)) {
                    // Remover o item
                    itensSolicitacao.splice(index, 1);
                    
                    // Atualizar a tabela
                    atualizarTabelaItens();
                    
                    // Marcar como editado
                    marcarComoEditado();
                    
                    mostrarFeedback('Item removido com sucesso', 'success');
                }
            }
        }
        
        // Função para abrir popup de produtos
        function abrirPopupProdutos() {
            // Carregar produtos do banco de dados
            fetch('obter-produtos.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        preencherTabelaProdutos(data.produtos);
                        document.getElementById('produtoPopup').style.display = 'block';
                        document.getElementById('produtoPopupOverlay').style.display = 'block';
                    } else {
                        mostrarFeedback('Erro ao carregar produtos', 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    mostrarFeedback('Erro ao carregar produtos', 'error');
                });
        }
        
        // Função para preencher tabela de produtos
        function preencherTabelaProdutos(produtos) {
            const produtosTableBody = document.getElementById('produtosTableBody');
            produtosTableBody.innerHTML = '';
            
            produtos.forEach(produto => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${produto.codigo}</td>
                    <td>${produto.nome}</td>
                    <td>${produto.quantidade}</td>
                    <td>
                        <button type="button" class="btn btn-primary" onclick="selecionarProduto('${produto.codigo}', '${produto.nome}', ${produto.quantidade})">Selecionar</button>
                    </td>
                `;
                produtosTableBody.appendChild(row);
            });
        }
        
        // Função para filtrar produtos
        function filtrarProdutos() {
            const termo = document.getElementById('pesquisaProduto').value.toLowerCase();
            const linhas = document.getElementById('produtosTableBody').getElementsByTagName('tr');
            
            for (let i = 0; i < linhas.length; i++) {
                const codigo = linhas[i].getElementsByTagName('td')[0].textContent.toLowerCase();
                const nome = linhas[i].getElementsByTagName('td')[1].textContent.toLowerCase();
                
                if (codigo.includes(termo) || nome.includes(termo)) {
                    linhas[i].style.display = '';
                } else {
                    linhas[i].style.display = 'none';
                }
            }
        }
        
        // Função para selecionar produto
        function selecionarProduto(codigo, nome, estoque) {
            if (itemEditIndex >= 0) {
                // Editando item existente
                itensSolicitacao[itemEditIndex].produto = codigo;
                itensSolicitacao[itemEditIndex].nome_produto = nome;
                itensSolicitacao[itemEditIndex].estoque = estoque;
                // Manter a quantidade atual
            } else {
                // Adicionando novo item - quantidade padrão 1
                itensSolicitacao.push({
                    produto: codigo,
                    nome_produto: nome,
                    quantidade: 1,
                    estoque: estoque
                });
            }
            
            fecharPopupProdutos();
            atualizarTabelaItens();
            marcarComoEditado();
        }
        
        // Função para fechar popup de produtos
        function fecharPopupProdutos() {
            document.getElementById('produtoPopup').style.display = 'none';
            document.getElementById('produtoPopupOverlay').style.display = 'none';
        }
        
        // Função para fechar popup principal
        function fecharPopup() {
            // Verificar se está em modo de edição
            if (emEdicao) {
                if (confirm('Você tem alterações não salvas. Deseja realmente sair? As alterações serão perdidas.')) {
                    // Cancelar edição
                    emEdicao = false;
                    houveEdicao = false;
                    document.getElementById('btn-concluir').style.display = 'inline-block';
                    document.getElementById('btn-salvar-alteracoes').style.display = 'none';
                    document.getElementById('btn-adicionar-item').style.display = 'none';
                    document.getElementById('btn-editar-solicitacao').style.display = 'inline-block';
                    
                    // Fechar popup
                    document.getElementById('popup').style.display = 'none';
                    document.getElementById('popupOverlay').style.display = 'none';
                    
                    // Limpar dados
                    solicitacaoAtual = null;
                    itensSolicitacao = [];
                }
            } else {
                // Fechar normalmente se não estiver editando
                document.getElementById('popup').style.display = 'none';
                document.getElementById('popupOverlay').style.display = 'none';
                
                // Limpar dados
                solicitacaoAtual = null;
                itensSolicitacao = [];
            }
        }
        
        // Função para salvar alterações
        function salvarAlteracoes() {
            if (!solicitacaoAtual) return;
            if (solicitacaoAtual.status === 'concluido') {
                mostrarFeedback('Não é possível alterar uma solicitação concluída', 'error');
                return;
            }
            const formData = new FormData();
            formData.append('tipo', solicitacaoAtual.tipo);
            formData.append('codigo', solicitacaoAtual.codigo);
            if (solicitacaoAtual.tipo !== 'pedido_especial') {
                formData.append('itens', JSON.stringify(itensSolicitacao));
            }
            mostrarFeedback('Salvando alterações...', 'info');
            fetch('atualizar-solicitacao.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarFeedback('Alterações salvas com sucesso', 'success');
                    houveEdicao = false;
                    emEdicao = false;
                    document.getElementById('btn-concluir').style.display = 'inline-block';
                    document.getElementById('btn-salvar-alteracoes').style.display = 'none';
                    document.getElementById('btn-adicionar-item').style.display = 'none';
                    document.getElementById('btn-editar-solicitacao').style.display = 'inline-block';
                    atualizarTabelaItens();
                } else {
                    mostrarFeedback('Erro ao salvar alterações: ' + (data.message || 'Erro desconhecido'), 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                mostrarFeedback('Erro ao processar a solicitação', 'error');
            });
        }
        
        // Função para confirmar conclusão
        function confirmarConclusao() {
            document.getElementById('confirmOverlay').style.display = 'flex';
            // Configurar botões de confirmação
            var btnConfirm = document.getElementById('confirmConcluir');
            var btnCancel = document.getElementById('cancelConcluir');
            if (btnConfirm && btnCancel) {
                btnConfirm.onclick = concluirSolicitacao;
                btnCancel.onclick = function() {
                    document.getElementById('confirmOverlay').style.display = 'none';
                };
            } else {
                mostrarFeedback('Erro interno: botões de confirmação não encontrados no popup.', 'error');
            }
        }

        // Função para confirmar exclusão de pedido urgente
        function confirmarExclusaoUrgente() {
            if (!solicitacaoAtual) return;

            // Mostrar popup moderno de confirmação
            document.getElementById('confirmacaoExclusaoOverlay').style.display = 'flex';
            document.getElementById('confirmacaoExclusaoPopup').style.display = 'block';
        }

        // Função para fechar popup de confirmação
        function fecharConfirmacaoExclusao() {
            document.getElementById('confirmacaoExclusaoOverlay').style.display = 'none';
            document.getElementById('confirmacaoExclusaoPopup').style.display = 'none';
        }

        // Função para confirmar exclusão definitiva
        function confirmarExclusaoDefinitiva() {
            fecharConfirmacaoExclusao();
            excluirPedidoUrgente();
        }

        // Função para excluir pedido urgente
        function excluirPedidoUrgente() {
            if (!solicitacaoAtual) return;

            // Mostrar loading
            mostrarFeedback('Excluindo pedido urgente...', 'info');

            const formData = new FormData();
            formData.append('tipo', solicitacaoAtual.tipo);
            formData.append('codigo', solicitacaoAtual.codigo);

            fetch('excluir-pedido-urgente.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarFeedback('✅ Pedido urgente excluído com sucesso!', 'success');

                    // Remover o card da interface
                    const card = document.querySelector(`[data-tipo="${solicitacaoAtual.tipo}"][data-codigo="${solicitacaoAtual.codigo}"]`);
                    if (card) {
                        card.remove();
                    }

                    // Fechar popup
                    fecharPopup();
                } else {
                    mostrarFeedback('❌ Erro ao excluir pedido: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                mostrarFeedback('❌ Erro ao excluir pedido urgente', 'error');
            });
        }

        // Função para concluir solicitação
        function concluirSolicitacao() {
            if (!solicitacaoAtual) return;

            // Verificar se é pedido urgente e se usuário é admin
            const avisoUrgente = document.getElementById('aviso-pedido-urgente');
            const isPedidoUrgente = avisoUrgente && avisoUrgente.style.display !== 'none';

            console.log('🔍 Verificação de conclusão:', {
                avisoUrgente: !!avisoUrgente,
                display: avisoUrgente ? avisoUrgente.style.display : 'N/A',
                isPedidoUrgente: isPedidoUrgente,
                solicitacaoAtual: solicitacaoAtual
            });

            if (isPedidoUrgente) {
                console.log('🚨 Pedido urgente detectado! Verificando permissões...');

                // Verificar se usuário é administrador
                fetch('verificar-admin.php')
                    .then(response => response.json())
                    .then(data => {
                        console.log('📋 Resposta verificação admin:', data);

                        if (data.success && !data.is_admin) {
                            // Usuário não é admin, mostrar aviso
                            console.log('❌ Usuário não é admin - BLOQUEANDO conclusão');
                            document.getElementById('confirmOverlay').style.display = 'none';
                            mostrarFeedback('❌ Apenas administradores podem aceitar pedidos urgentes!', 'error');
                            return;
                        }

                        console.log('✅ Usuário autorizado - PERMITINDO conclusão');
                        // Se é admin ou não é pedido urgente, continuar
                        processarConclusao();
                    })
                    .catch(error => {
                        console.error('❌ Erro ao verificar permissões:', error);
                        // Em caso de erro, permitir continuar
                        console.log('⚠️ Erro na verificação - PERMITINDO por segurança');
                        processarConclusao();
                    });
            } else {
                console.log('📝 Pedido normal - PERMITINDO conclusão');
                // Não é pedido urgente, continuar normalmente
                processarConclusao();
            }
        }

        function processarConclusao() {
            // Mostrar indicador de carregamento apenas dentro do popup
            const confirmPopup = document.querySelector('#confirmOverlay .confirm-popup');
            if (confirmPopup) {
                confirmPopup.innerHTML = `
                    <h3>Processando...</h3>
                    <div class="loading-spinner"></div>
                `;
            }

            const formData = new FormData();
            formData.append('tipo', solicitacaoAtual.tipo);
            formData.append('codigo', solicitacaoAtual.codigo);

            // Apenas enviar itens se não for pedido especial
            if (solicitacaoAtual.tipo !== 'pedido_especial') {
                formData.append('itens', JSON.stringify(itensSolicitacao));
            } else {
                formData.append('itens', JSON.stringify([]));
            }

            fetch('concluir-solicitacao.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('confirmOverlay').style.display = 'none';

                if (data.success) {
                    // Atualizar o status da solicitação na interface
                    solicitacaoAtual.status = 'concluido';

                    // Atualizar a exibição
                    document.getElementById('btn-adicionar-item').style.display = 'none';
                    document.getElementById('btn-concluir').style.display = 'none';

                    // Atualizar a tabela de itens (para desabilitar inputs)
                    atualizarTabelaItens();

                    // Mostrar feedback de sucesso
                    mostrarFeedback('Solicitação concluída com sucesso!', 'success');

                    // Fechar o popup de detalhes
                    setTimeout(() => {
                        fecharPopup();
                        // Recarregar a página para atualizar a lista
                        window.location.reload();
                    }, 1500);
                } else {
                    mostrarFeedback('Erro ao concluir solicitação: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                document.getElementById('confirmOverlay').style.display = 'none';
                mostrarFeedback('Erro ao processar a solicitação', 'error');
            });
        }
        
        // Função para mostrar feedback
        function mostrarFeedback(mensagem, tipo) {
            const feedbackPopup = document.createElement('div');
            feedbackPopup.id = 'feedback-popup';
            feedbackPopup.className = tipo;
            feedbackPopup.textContent = mensagem;
            
            // Remover qualquer feedback existente
            const existingFeedback = document.getElementById('feedback-popup');
            if (existingFeedback) {
                existingFeedback.remove();
            }
            
            document.body.appendChild(feedbackPopup);
            
            // Mostrar o feedback
            setTimeout(() => {
                feedbackPopup.classList.add('show');
            }, 10);
            
            // Esconder o feedback após alguns segundos
            setTimeout(() => {
                feedbackPopup.classList.remove('show');
                setTimeout(() => {
                    feedbackPopup.remove();
                }, 300);
            }, 5000);
        }
        
        // Inicializar feedback se existir
        document.addEventListener('DOMContentLoaded', function() {
            const feedbackPopup = document.getElementById('feedback-popup');
            if (feedbackPopup) {
                feedbackPopup.classList.add('show');
                setTimeout(() => {
                    feedbackPopup.classList.remove('show');
                }, 5000);
            }
        });

        // Função para imprimir solicitação
        function imprimirSolicitacao() {
            // Obter dados da solicitação atual
            const tipo = document.getElementById('tipo_solicitacao').value;
            const codigo = document.getElementById('codigo_solicitacao').value;
            
            // Criar uma nova janela para impressão
            const printWindow = window.open('', '_blank');
            
            // Preparar o conteúdo HTML para impressão
            let html = `
                <!DOCTYPE html>
                <html lang="pt-BR">
                <head>
                    <meta charset="UTF-8">
                    <title>Solicitação #${codigo}</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            line-height: 1.6;
                            margin: 20px;
                        }
                        h1 {
                            text-align: center;
                            margin-bottom: 20px;
                            border-bottom: 2px solid #333;
                            padding-bottom: 10px;
                        }
                        .info-container {
                            margin-bottom: 20px;
                        }
                        .info-row {
                            display: flex;
                            margin-bottom: 10px;
                        }
                        .info-label {
                            font-weight: bold;
                            width: 150px;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        th, td {
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: left;
                        }
                        th {
                            background-color: #f2f2f2;
                        }
                        .assinatura {
                            margin-top: 50px;
                            text-align: center;
                        }
                        .assinatura-linha {
                            margin: 50px auto 10px;
                            width: 70%;
                            border-bottom: 1px solid #000;
                        }
                        .assinatura-texto {
                            font-size: 14px;
                        }
                        .tipo-badge {
                            display: inline-block;
                            padding: 5px 10px;
                            color: white;
                            border-radius: 4px;
                            font-weight: bold;
                            margin-left: 10px;
                        }
                        .tipo-requisicao { background-color: #4CAF50; }
                        .tipo-pedido_mensal { background-color: #2196F3; }
                        .tipo-pedido_especial { background-color: #FF9800; }
                    </style>
                </head>
                <body>
                    <h1>
                        Solicitação de Estoque #${codigo}
                        <span class="tipo-badge tipo-${tipo}">
                            ${getTipoTexto(tipo)}
                        </span>
                    </h1>
                    
                    <div class="info-container">
                        <div class="info-row">
                            <div class="info-label">Solicitante:</div>
                            <div>${document.getElementById('solicitante').value}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Empresa:</div>
                            <div>${document.getElementById('empresa').value}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Contrato:</div>
                            <div>${document.getElementById('contrato').value}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Finalidade:</div>
                            <div>${document.getElementById('finalidade').value}</div>
                        </div>`;
            
            // Adicionar campos específicos por tipo
            if (tipo !== 'pedido_especial') {
                html += `
                        <div class="info-row">
                            <div class="info-label">Funcionário:</div>
                            <div>${document.getElementById('funcionario').value}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Setor:</div>
                            <div>${document.getElementById('setor').value}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Função:</div>
                            <div>${document.getElementById('funcao').value}</div>
                        </div>`;
            }
            
            html += `
                        <div class="info-row">
                            <div class="info-label">Observação:</div>
                            <div>${document.getElementById('observacao').value}</div>
                        </div>
                    </div>`;
            
            // Adicionar imagem se for pedido especial e tiver imagem
            if (tipo === 'pedido_especial' && document.getElementById('imagem_container').style.display !== 'none') {
                const imagemSrc = document.getElementById('imagem_pedido').src;
                html += `
                    <div style="text-align: center; margin: 20px 0;">
                        <h3>Imagem do Pedido</h3>
                        <img src="${imagemSrc}" style="max-width: 80%; max-height: 300px;">
                    </div>`;
            }
            
            // Adicionar tabela de itens
            if (tipo !== 'pedido_especial') {
                html += `
                    <h3>Itens da Solicitação</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>`;
                
                // Adicionar linhas da tabela
                for (const item of itensSolicitacao) {
                    html += `
                            <tr>
                                <td>${item.produto}</td>
                                <td>${item.nome_produto}</td>
                                <td>${item.quantidade}</td>
                            </tr>`;
                }
                
                html += `
                        </tbody>
                    </table>`;
            }
            
            // Adicionar campo de assinatura
            html += `
                    <div class="assinatura">
                        <div class="assinatura-linha"></div>
                        <div class="assinatura-texto">Assinatura do Recebedor</div>
                    </div>
                    
                    <div class="assinatura" style="margin-top: 30px;">
                        <div class="assinatura-linha"></div>
                        <div class="assinatura-texto">Assinatura do Responsável</div>
                    </div>
                </body>
                </html>`;
            
            // Escrever o HTML na nova janela e imprimir
            printWindow.document.open();
            printWindow.document.write(html);
            printWindow.document.close();
            
            // Esperar o carregamento completo antes de imprimir
            printWindow.onload = function() {
                printWindow.print();
                // Não fechar a janela após imprimir para permitir múltiplas impressões
            };
        }

        // Função auxiliar para obter o texto do tipo de solicitação
        function getTipoTexto(tipo) {
            switch(tipo) {
                case 'requisicao':
                    return 'Requisição';
                case 'pedido_mensal':
                    return 'Pedido Mensal';
                case 'pedido_especial':
                    return 'Pedido Especial';
                default:
                    return 'Desconhecido';
            }
        }

        function habilitarEdicaoSolicitacao() {
            emEdicao = true;
            document.getElementById('btn-adicionar-item').style.display = 'inline-block';
            document.getElementById('btn-salvar-alteracoes').style.display = 'inline-block';
            document.getElementById('btn-concluir').style.display = 'none';
            document.getElementById('btn-editar-solicitacao').style.display = 'none';
            atualizarTabelaItens();
        }

        // Função duplicada removida - usando a definição do início

        // Badge de status concluído e esconder/centralizar botões se necessário
        function atualizarStatusConcluidoPopup(tipo, status) {
            const statusRow = document.getElementById('status-concluido-row');
            const statusLabel = document.getElementById('status-concluido-label');
            const btnImprimirAzul = document.getElementById('btn-imprimir-azul');
            const btnEditar = document.getElementById('btn-editar-solicitacao');
            const btnConcluir = document.getElementById('btn-concluir');
            if (status === 'concluido') {
                let texto = '';
                if (tipo === 'requisicao') texto = 'SOLICITAÇÃO CONCLUÍDA';
                else if (tipo === 'pedido_mensal') texto = 'PEDIDO MENSAL CONCLUÍDO';
                else if (tipo === 'pedido_especial') texto = 'PEDIDO ESPECIAL CONCLUÍDO';
                statusLabel.textContent = texto;
                statusRow.style.display = 'flex';
                if (btnImprimirAzul) btnImprimirAzul.style.display = 'flex';
                if (btnEditar) btnEditar.style.display = 'none';
                if (btnConcluir) btnConcluir.style.display = 'none';
            } else {
                statusLabel.textContent = '';
                statusRow.style.display = 'none';
                if (btnImprimirAzul) btnImprimirAzul.style.display = 'flex';
                if (btnEditar) btnEditar.style.display = 'flex';
                if (btnConcluir) btnConcluir.style.display = 'inline-block';
            }
        }

        // --- Atualização automática de novas solicitações e som ---
        function getMaioresCodigosPorTipo() {
            let maiorRequisicao = 0;
            let maiorPedidoMensal = 0;
            let maiorPedidoEspecial = 0;
            document.querySelectorAll('.solicitacao-card').forEach(card => {
                const tipo = card.getAttribute('data-tipo');
                const codigo = parseInt(card.getAttribute('data-codigo'));
                if (tipo === 'requisicao' && codigo > maiorRequisicao) maiorRequisicao = codigo;
                if (tipo === 'pedido_mensal' && codigo > maiorPedidoMensal) maiorPedidoMensal = codigo;
                if (tipo === 'pedido_especial' && codigo > maiorPedidoEspecial) maiorPedidoEspecial = codigo;
            });
            return {
                requisicao: maiorRequisicao,
                pedido_mensal: maiorPedidoMensal,
                pedido_especial: maiorPedidoEspecial
            };
        }

        function buscarNovasSolicitacoes() {
            const maiores = getMaioresCodigosPorTipo();
            const url = `buscar-novas-solicitacoes.php?maior_codigo_requisicao=${maiores.requisicao}&maior_codigo_pedido_mensal=${maiores.pedido_mensal}&maior_codigo_pedido_especial=${maiores.pedido_especial}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.novas && data.novas.length > 0) {
                        // Adicionar novas solicitações ao grid de pendentes
                        const grid = document.getElementById('solicitacoes-pendentes');
                        data.novas.forEach(solicitacao => {
                            const tipoClass = 'tipo-' + solicitacao.tipo;
                            let tipoTexto = '';
                            switch(solicitacao.tipo) {
                                case 'requisicao': tipoTexto = 'Requisição'; break;
                                case 'pedido_mensal': tipoTexto = 'Pedido Mensal'; break;
                                case 'pedido_especial': tipoTexto = 'Pedido Especial'; break;
                            }
                            const dataFormatada = solicitacao.data_solicitacao
                                ? new Date(solicitacao.data_solicitacao).toLocaleDateString('pt-BR')
                                : 'Data não disponível';

                            // Verificar se é pedido urgente
                            const isUrgente = solicitacao.pedido_urgente == 1;
                            const classeUrgente = isUrgente ? ' solicitacao-urgente' : '';

                            const card = document.createElement('div');
                            card.className = 'solicitacao-card' + classeUrgente;
                            card.setAttribute('data-tipo', solicitacao.tipo);
                            card.setAttribute('data-codigo', solicitacao.codigo);
                            card.onclick = function() {
                                mostrarDetalhes(solicitacao.tipo, solicitacao.codigo);
                            };
                            card.innerHTML = `
                                <h3>
                                    #${solicitacao.codigo}
                                    <span class="tipo-badge ${tipoClass}">${tipoTexto}</span>
                                </h3>
                                <p><strong>Solicitante:</strong> ${solicitacao.solicitante}</p>
                                <p><strong>Empresa:</strong> ${solicitacao.empresa}</p>
                                <p><strong>Finalidade:</strong> ${solicitacao.finalidade}</p>
                                <p class="data">${dataFormatada}</p>
                            `;
                            // Adiciona no início do grid
                            grid.prepend(card);
                        });
                        // Toca o som imediatamente
                        document.getElementById('notificacao-audio').play();
                    }
                });
        }
        setInterval(buscarNovasSolicitacoes, 3000); // a cada 3 segundos

        // --- Notificação de cancelamento de solicitação ---
        window.addEventListener('storage', function(e) {
            if (e.key === 'sol-cancelada') {
                // Sinaliza para tocar o som após o reload
                localStorage.setItem('tocar-notificacao', '1');
                window.location.reload();
            }
        });

        window.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('tocar-notificacao') === '1') {
                var audio = document.getElementById('notificacao-audio');
                if (audio) {
                    audio.currentTime = 0;
                    audio.play();
                }
                localStorage.removeItem('tocar-notificacao');
            }
        });

        // Função para exibir o botão de cancelar se a solicitação estiver concluída
        function atualizarBotaoCancelarSolicitacao() {
            const btnCancelar = document.getElementById('btn-cancelar-solicitacao');
            if (solicitacaoAtual && solicitacaoAtual.status === 'concluido') {
                btnCancelar.style.display = 'inline-block';
            } else {
                btnCancelar.style.display = 'none';
            }
        }

        // Função para atualizar a exibição do botão "Excluir Pedido Urgente"
        function atualizarBotaoExcluirUrgente() {
            const btnExcluirUrgente = document.getElementById('btn-excluir-urgente');
            const avisoUrgente = document.getElementById('aviso-pedido-urgente');

            // Só mostrar o botão se:
            // 1. É pedido urgente (aviso está visível)
            // 2. Não está concluído
            if (avisoUrgente && avisoUrgente.style.display !== 'none' &&
                solicitacaoAtual && solicitacaoAtual.status !== 'concluido') {
                btnExcluirUrgente.style.display = 'inline-block';
            } else {
                btnExcluirUrgente.style.display = 'none';
            }
        }

        // Chamar ao mostrar detalhes
        // ... já existe chamada para atualizarStatusConcluidoPopup, adicionar:
        // atualizarBotaoCancelarSolicitacao();
        // (garantir que seja chamada após mostrar detalhes)

        // Função para abrir popup de confirmação de cancelamento
        function abrirPopupCancelarSolicitacao() {
            document.getElementById('cancelarSolicitacaoOverlay').style.display = 'flex';
        }
        function fecharPopupCancelarSolicitacao() {
            document.getElementById('cancelarSolicitacaoOverlay').style.display = 'none';
        }
        // Lógica do botão de confirmação
        document.getElementById('confirmCancelarSolicitacao').onclick = function() {
            cancelarSolicitacao();
        };
        document.getElementById('cancelCancelarSolicitacao').onclick = function() {
            fecharPopupCancelarSolicitacao();
        };

        // Função AJAX para cancelar a solicitação
        function cancelarSolicitacao() {
            if (!solicitacaoAtual) return;
            const formData = new FormData();
            formData.append('delete_id', solicitacaoAtual.codigo);
            formData.append('delete_tipo', solicitacaoAtual.tipo);
            fetch('cancelar-solicitacao.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                fecharPopupCancelarSolicitacao();
                if (data.success) {
                    mostrarFeedback('Solicitação cancelada com sucesso!', 'success');
                    setTimeout(() => { window.location.reload(); }, 1500);
                } else {
                    if (data.message && data.message.includes('assinatura')) {
                        mostrarPopupErroCancelarSolicitacao(data.message);
                    } else {
                        mostrarFeedback('Erro ao cancelar: ' + (data.message || 'Erro desconhecido'), 'error');
                    }
                }
            })
            .catch(error => {
                fecharPopupCancelarSolicitacao();
                mostrarFeedback('Erro ao processar o cancelamento', 'error');
            });
        }

        // Função para exibir popup de erro de cancelamento
        function mostrarPopupErroCancelarSolicitacao(msg) {
            document.getElementById('erroCancelarSolicitacaoMsg').textContent = msg || 'Não é possível cancelar: um ou mais itens já foram confirmados por assinatura na ficha de EPI.';
            document.getElementById('erroCancelarSolicitacaoOverlay').style.display = 'flex';
        }
        function fecharPopupErroCancelarSolicitacao() {
            document.getElementById('erroCancelarSolicitacaoOverlay').style.display = 'none';
        }
    </script>
</body>
</html>


















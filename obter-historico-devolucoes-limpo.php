<?php
// Garantir JSON puro - sem qualquer saída HTML
ob_start();
error_reporting(0);
ini_set('display_errors', 0);

session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
}

try {
    require_once 'conexao.php';
    
    $historico = [];
    
    // 1. Buscar devoluções por funcionário
    $sql_check = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'";
    $result = $conn->query($sql_check);
    
    if ($result && $result->fetch_assoc()['count'] > 0) {
        // Verificar se tabela usuarios existe
        $sql_usuarios = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'";
        $result_usuarios = $conn->query($sql_usuarios);
        $tem_usuarios = $result_usuarios && $result_usuarios->fetch_assoc()['count'] > 0;
        
        if ($tem_usuarios) {
            $sql = "
                SELECT 
                    d.id,
                    d.pessoa_id,
                    d.pessoa_nome,
                    d.produto_id,
                    d.produto_nome,
                    d.quantidade,
                    d.estado,
                    d.data_devolucao,
                    d.usuario_id,
                    COALESCE(u.nome, CONCAT('Usuário ', d.usuario_id)) as usuario_nome,
                    'funcionario' as tipo
                FROM devolucoes_epi d
                LEFT JOIN usuarios u ON d.usuario_id = u.id
                ORDER BY d.data_devolucao DESC
            ";
        } else {
            $sql = "
                SELECT 
                    id,
                    pessoa_id,
                    pessoa_nome,
                    produto_id,
                    produto_nome,
                    quantidade,
                    estado,
                    data_devolucao,
                    usuario_id,
                    CONCAT('Usuário ', usuario_id) as usuario_nome,
                    'funcionario' as tipo
                FROM devolucoes_epi
                ORDER BY data_devolucao DESC
            ";
        }
        
        $result = $conn->query($sql);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $row['assinatura'] = '';
                $historico[] = $row;
            }
        }
    }
    
    // 2. Buscar devoluções rápidas
    $sql_check = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'";
    $result = $conn->query($sql_check);
    
    if ($result && $result->fetch_assoc()['count'] > 0) {
        if (isset($tem_usuarios) && $tem_usuarios) {
            $sql = "
                SELECT 
                    d.id,
                    NULL as pessoa_id,
                    NULL as pessoa_nome,
                    d.produto_id,
                    d.produto_nome,
                    d.quantidade,
                    d.estado,
                    d.data_devolucao,
                    d.usuario_id,
                    COALESCE(u.nome, CONCAT('Usuário ', d.usuario_id)) as usuario_nome,
                    'rapida' as tipo
                FROM devolucoes_rapidas d
                LEFT JOIN usuarios u ON d.usuario_id = u.id
                ORDER BY d.data_devolucao DESC
            ";
        } else {
            $sql = "
                SELECT 
                    id,
                    NULL as pessoa_id,
                    NULL as pessoa_nome,
                    produto_id,
                    produto_nome,
                    quantidade,
                    estado,
                    data_devolucao,
                    usuario_id,
                    CONCAT('Usuário ', usuario_id) as usuario_nome,
                    'rapida' as tipo
                FROM devolucoes_rapidas
                ORDER BY data_devolucao DESC
            ";
        }
        
        $result = $conn->query($sql);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $row['assinatura'] = '';
                $historico[] = $row;
            }
        }
    }
    
    // 3. Ordenar por data
    if (count($historico) > 0) {
        usort($historico, function($a, $b) {
            return strtotime($b['data_devolucao']) - strtotime($a['data_devolucao']);
        });
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $historico = [];
}

// Limpar qualquer saída anterior
ob_clean();

// Enviar apenas JSON
header('Content-Type: application/json');
echo json_encode($historico);
exit();
?>

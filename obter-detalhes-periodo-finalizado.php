<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';
include_once 'periodos-limite-helper.php';

$id_periodo = isset($_GET['id_periodo']) ? intval($_GET['id_periodo']) : (isset($_POST['id_periodo']) ? intval($_POST['id_periodo']) : 0);
if (!$id_periodo) {
    echo json_encode(['success' => false, 'message' => 'id_periodo não informado']);
    exit();
}

$periodo = detalhesPeriodoFinalizado($id_periodo);
if (!$periodo) {
    echo json_encode(['success' => false, 'message' => 'Período não encontrado']);
    exit();
}

$dados_gastos = json_decode($periodo['dados_gastos'], true);

echo json_encode([
    'success' => true,
    'data_inicio' => $periodo['data_inicio'],
    'data_fim' => $periodo['data_fim'],
    'dados_gastos' => $dados_gastos
]); 
<?php
include 'conexao.php';

echo "<h2>Verificando estrutura das tabelas de itens</h2>";

// Verificar tabelas existentes relacionadas a itens
$tables = ['itens_solicitacao', 'itens_requisicao', 'itens_pedido_mensal'];

foreach ($tables as $table) {
    echo "<h3>Tabela: $table</h3>";
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Tabela existe</p>";
        
        // Mostrar estrutura
        $desc = $conn->query("DESCRIBE $table");
        if ($desc) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $desc->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Contar registros
        $count = $conn->query("SELECT COUNT(*) as total FROM $table");
        if ($count) {
            $row = $count->fetch_assoc();
            echo "<p>Total de registros: " . $row['total'] . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Tabela não existe</p>";
    }
    echo "<hr>";
}

// Verificar restrições de chave estrangeira
echo "<h3>Verificando restrições de chave estrangeira</h3>";
$result = $conn->query("
    SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        CONSTRAINT_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
    FROM 
        INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE 
        REFERENCED_TABLE_SCHEMA = 'sistema' 
        AND (REFERENCED_TABLE_NAME = 'requisicoes' OR REFERENCED_TABLE_NAME = 'pedidos_mensais')
");

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Tabela</th><th>Coluna</th><th>Constraint</th><th>Referencia Tabela</th><th>Referencia Coluna</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['CONSTRAINT_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Nenhuma restrição de chave estrangeira encontrada.</p>";
}

echo "<h3>Teste de exclusão simulada</h3>";
echo "<p>Para testar a exclusão, primeiro precisamos de uma requisição urgente com itens.</p>";

$conn->close();
?>

<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
}

require_once 'conexao.php';

echo "<h2>Teste Final - Correções Aplicadas</h2>";

// 1. Verificar estados corrigidos
echo "<h3>1. ✅ Estados de Devolução Corrigidos:</h3>";

$result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'");
if ($result && $result->fetch_assoc()['count'] > 0) {
    echo "<h4>Distribuição de Estados (devolucoes_epi):</h4>";
    $sql = "SELECT estado, COUNT(*) as total FROM devolucoes_epi GROUP BY estado ORDER BY total DESC";
    $result_estados = $conn->query($sql);
    
    if ($result_estados) {
        echo "<table border='1' style='border-collapse: collapse; background: #f8f9fa;'>";
        echo "<tr><th>Estado</th><th>Quantidade</th><th>Status</th></tr>";
        
        $tem_problema = false;
        while ($row = $result_estados->fetch_assoc()) {
            $status = "✅ OK";
            $cor = "green";
            
            if ($row['estado'] == '0' || $row['estado'] == '' || $row['estado'] == null) {
                $status = "❌ Problemático";
                $cor = "red";
                $tem_problema = true;
            }
            
            echo "<tr>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>{$row['total']}</td>";
            echo "<td style='color: $cor;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (!$tem_problema) {
            echo "<p style='color: green;'><strong>✅ Todos os estados estão corretos!</strong></p>";
        }
    }
}

$result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'");
if ($result && $result->fetch_assoc()['count'] > 0) {
    echo "<h4>Distribuição de Estados (devolucoes_rapidas):</h4>";
    $sql = "SELECT estado, COUNT(*) as total FROM devolucoes_rapidas GROUP BY estado ORDER BY total DESC";
    $result_estados = $conn->query($sql);
    
    if ($result_estados) {
        echo "<table border='1' style='border-collapse: collapse; background: #f8f9fa;'>";
        echo "<tr><th>Estado</th><th>Quantidade</th><th>Status</th></tr>";
        
        while ($row = $result_estados->fetch_assoc()) {
            $status = "✅ OK";
            $cor = "green";
            
            if ($row['estado'] == '0' || $row['estado'] == '' || $row['estado'] == null) {
                $status = "❌ Problemático";
                $cor = "red";
            }
            
            echo "<tr>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>{$row['total']}</td>";
            echo "<td style='color: $cor;'>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// 2. Verificar nomes de usuários
echo "<h3>2. ✅ Nomes de Usuários Corrigidos:</h3>";

$result_usuarios = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'");
if ($result_usuarios && $result_usuarios->fetch_assoc()['count'] > 0) {
    echo "<p>✅ Tabela 'usuarios' existe</p>";
    
    // Testar consulta com JOIN
    $sql_teste = "
        SELECT 
            d.id,
            d.produto_nome,
            d.usuario_id,
            COALESCE(u.nome, CONCAT('Usuário ID: ', d.usuario_id)) as usuario_nome
        FROM devolucoes_epi d
        LEFT JOIN usuarios u ON d.usuario_id = u.id
        LIMIT 5
    ";
    
    $result_teste = $conn->query($sql_teste);
    if ($result_teste) {
        echo "<h4>Teste de Nomes de Usuários:</h4>";
        echo "<table border='1' style='border-collapse: collapse; background: #e8f5e8;'>";
        echo "<tr><th>ID Devolução</th><th>Produto</th><th>ID Usuário</th><th>Nome Usuário</th></tr>";
        
        while ($row = $result_teste->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['produto_nome']}</td>";
            echo "<td>{$row['usuario_id']}</td>";
            echo "<td>{$row['usuario_nome']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p style='color: green;'><strong>✅ Nomes de usuários sendo exibidos corretamente!</strong></p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Tabela 'usuarios' não existe - usando IDs</p>";
}

// 3. Testar endpoint do histórico
echo "<h3>3. 🌐 Teste do Endpoint do Histórico:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/sistema/obter-historico-devolucoes.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, false);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    echo "<p style='color: green;'>✅ Endpoint respondeu com sucesso (HTTP 200)</p>";
    
    $data = json_decode($response, true);
    if (is_array($data)) {
        echo "<p>✅ JSON válido retornado</p>";
        echo "<p>Total de registros: " . count($data) . "</p>";
        
        if (count($data) > 0) {
            echo "<h4>Primeiro registro do histórico:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Campo</th><th>Valor</th></tr>";
            
            foreach ($data[0] as $campo => $valor) {
                $cor = "black";
                if ($campo == 'estado' && ($valor == '0' || $valor == '')) {
                    $cor = "red";
                }
                if ($campo == 'usuario_nome' && $valor == 'Sistema') {
                    $cor = "orange";
                }
                
                echo "<tr>";
                echo "<td><strong>$campo</strong></td>";
                echo "<td style='color: $cor;'>$valor</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ Resposta não é um JSON válido</p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ Endpoint retornou erro HTTP $http_code</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
}

// 4. Resumo das correções
echo "<h3>4. 📋 Resumo das Correções Aplicadas:</h3>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Estados Corrigidos</h4>";
echo "<ul>";
echo "<li>Estados '0' → 'descarte'</li>";
echo "<li>Estados vazios → 'descarte'</li>";
echo "<li>Estados NULL → 'descarte'</li>";
echo "<li>Validação nos formulários</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Usuários Corrigidos</h4>";
echo "<ul>";
echo "<li>JOIN com tabela usuarios</li>";
echo "<li>Fallback para 'Usuário ID: X'</li>";
echo "<li>Sessão de usuário configurada</li>";
echo "<li>Nomes reais exibidos</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h3>5. 🧪 Links para Teste:</h3>";
echo "<ul>";
echo "<li><a href='obter-historico-devolucoes.php' target='_blank'>📊 Testar endpoint do histórico</a></li>";
echo "<li><a href='devolucao.php' target='_blank'>📋 Testar página de devolução</a></li>";
echo "<li><a href='verificar-sessao.php' target='_blank'>👤 Verificar sessão do usuário</a></li>";
echo "</ul>";

echo "<p style='margin-top: 30px; text-align: center;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;'>🚀 Testar Sistema Corrigido</a>";
echo "</p>";

$conn->close();
?>

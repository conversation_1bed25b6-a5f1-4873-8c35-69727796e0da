<?php
require_once 'conexao.php';
header('Content-Type: application/json');

$id_pessoa = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id_pessoa <= 0) {
    echo json_encode([]);
    exit;
}

// Consulta para pegar apenas a entrega mais recente de cada produto para a pessoa
$sql = "
    SELECT 
        p.nome, 
        ppe.data_entrega, 
        p.validade_uso
    FROM 
        pessoa_epi ppe
    INNER JOIN (
        SELECT 
            produto_id, 
            MAX(data_entrega) as max_data_entrega
        FROM 
            pessoa_epi
        WHERE 
            pessoa_id = ?
        GROUP BY 
            produto_id
    ) as pe_recente 
        ON ppe.produto_id = pe_recente.produto_id 
        AND ppe.data_entrega = pe_recente.max_data_entrega
    JOIN 
        produtos p ON ppe.produto_id = p.codigo 
    WHERE 
        ppe.pessoa_id = ? 
        AND p.validade_uso IS NOT NULL 
        AND p.validade_uso > 0
";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $id_pessoa, $id_pessoa);
$stmt->execute();
$result = $stmt->get_result();

$hoje = new DateTime();
$alertas = [];
while ($row = $result->fetch_assoc()) {
    $data_entrega = new DateTime($row['data_entrega']);
    $data_vencimento = clone $data_entrega;
    $data_vencimento->modify('+' . $row['validade_uso'] . ' months');
    
    $dias_restantes = (int)$hoje->diff($data_vencimento)->format('%r%a');
    
    if ($dias_restantes <= 30) {
        $alertas[] = [
            'nome' => $row['nome'],
            'dias_restantes' => $dias_restantes
        ];
    }
}

// Ordenar para mostrar os mais críticos primeiro
usort($alertas, function($a, $b) {
    return $a['dias_restantes'] <=> $b['dias_restantes'];
});

echo json_encode($alertas); 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['tipo_usuario'] = 'administrador';
}

require_once 'conexao.php';

echo "<h2>Verificar Tabela de Acessos</h2>";

// Verificar se a tabela usuarios_acessos existe
$sql_check = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios_acessos'";
$result = $conn->query($sql_check);

if ($result && $result->fetch_assoc()['count'] > 0) {
    echo "<p>✅ Tabela 'usuarios_acessos' existe</p>";
    
    // Verificar estrutura
    echo "<h3>Estrutura da tabela:</h3>";
    $result_struct = $conn->query("DESCRIBE usuarios_acessos");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result_struct->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar dados existentes
    $count_acessos = $conn->query("SELECT COUNT(*) as total FROM usuarios_acessos")->fetch_assoc()['total'];
    echo "<p>Total de registros de acesso: $count_acessos</p>";
    
    if ($count_acessos > 0) {
        echo "<h4>Acessos existentes (últimos 10):</h4>";
        $result_acessos = $conn->query("SELECT * FROM usuarios_acessos ORDER BY id DESC LIMIT 10");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Usuário ID</th><th>Menu</th><th>Item</th><th>Permitido</th></tr>";
        while ($row = $result_acessos->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['usuario_id']}</td>";
            echo "<td>{$row['menu']}</td>";
            echo "<td>{$row['item']}</td>";
            echo "<td>" . ($row['permitido'] ? 'Sim' : 'Não') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} else {
    echo "<p>❌ Tabela 'usuarios_acessos' não existe</p>";
    echo "<p>Criando tabela...</p>";
    
    $sql_create = "
        CREATE TABLE usuarios_acessos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            menu VARCHAR(100) NOT NULL,
            item VARCHAR(100) DEFAULT NULL,
            permitido TINYINT(1) DEFAULT 0,
            data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_acesso (usuario_id, menu, item),
            FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
        )
    ";
    
    if ($conn->query($sql_create)) {
        echo "<p>✅ Tabela 'usuarios_acessos' criada com sucesso</p>";
        
        // Inserir acessos padrão para o administrador
        echo "<p>Inserindo acessos padrão para administrador...</p>";
        
        $menus_padrao = [
            'cadastros' => ['cadastro-produto', 'cadastro-pessoa', 'cadastro-empresa'],
            'tabelas' => ['tabela-produtos', 'tabela-pessoas', 'tabela-empresas'],
            'entradas' => ['entrada-estoque', 'registros-entrada'],
            'saida' => ['saida-estoque', 'registro-saidas'],
            'solicitacoes' => ['todas-solicitacoes'],
            'epi' => ['fichas-epi'],
            'limites-gastos' => ['configurar-limites', 'relatorio-gastos', 'alertas-limites'],
            'devolucao' => ['devolucao', 'historico-devolucoes', 'relatorio-devolucoes'],
            'pedidos' => ['requisicoes', 'pedidos-mensais', 'produtos-pedido-mensal', 'pedidos-especiais'],
            'meus-pedidos' => ['pedidos'],
            'usuarios' => ['cadastro-usuario', 'gerenciar-usuarios']
        ];
        
        $usuario_admin_id = 1; // Assumindo que o admin tem ID 1
        
        foreach ($menus_padrao as $menu => $itens) {
            // Inserir permissão para o menu principal
            $sql_menu = "INSERT IGNORE INTO usuarios_acessos (usuario_id, menu, permitido) VALUES (?, ?, 1)";
            $stmt_menu = $conn->prepare($sql_menu);
            $stmt_menu->bind_param("is", $usuario_admin_id, $menu);
            $stmt_menu->execute();
            
            // Inserir permissões para os itens do menu
            foreach ($itens as $item) {
                $sql_item = "INSERT IGNORE INTO usuarios_acessos (usuario_id, menu, item, permitido) VALUES (?, ?, ?, 1)";
                $stmt_item = $conn->prepare($sql_item);
                $stmt_item->bind_param("iss", $usuario_admin_id, $menu, $item);
                $stmt_item->execute();
            }
        }
        
        echo "<p>✅ Acessos padrão inseridos para administrador</p>";
        
    } else {
        echo "<p>❌ Erro ao criar tabela: " . $conn->error . "</p>";
    }
}

// Testar endpoint
echo "<h3>Teste do Endpoint:</h3>";
echo "<p><a href='obter-acessos-usuario.php?usuario_id=1' target='_blank'>Testar obter-acessos-usuario.php</a></p>";

// Verificar se existem as novas abas
echo "<h3>Verificar Novas Abas:</h3>";
$novas_abas = ['limites-gastos', 'devolucao'];

foreach ($novas_abas as $aba) {
    $sql_check_aba = "SELECT COUNT(*) as count FROM usuarios_acessos WHERE menu = ? AND usuario_id = 1";
    $stmt_check = $conn->prepare($sql_check_aba);
    $stmt_check->bind_param("s", $aba);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    $count = $result_check->fetch_assoc()['count'];
    
    if ($count > 0) {
        echo "<p>✅ Aba '$aba' configurada ($count registros)</p>";
    } else {
        echo "<p>⚠️ Aba '$aba' não configurada ainda</p>";
    }
}

echo "<h3>Links para Teste:</h3>";
echo "<ul>";
echo "<li><a href='gerenciar-usuarios.php' target='_blank'>Gerenciar Usuários</a></li>";
echo "<li><a href='obter-acessos-usuario.php?usuario_id=1' target='_blank'>API de Acessos</a></li>";
echo "</ul>";

$conn->close();
?>

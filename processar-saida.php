<?php
// Iniciar sessão apenas se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definir sessão se não existir (para teste)
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['nome_usuario'] = 'admin';
}

include 'conexao.php';

// Verificar se a requisição é do tipo POST e se é uma requisição AJAX
header('Content-Type: application/json');

try {
    // Verificar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Obter dados do corpo da requisição
    $json = file_get_contents('php://input');
    if (empty($json)) {
        throw new Exception('Nenhum dado JSON recebido');
    }

    // Decodificar JSON
    $data = json_decode($json, true);
    if (!$data) {
        throw new Exception('JSON inválido: ' . json_last_error_msg());
    }

    // Extrair dados
    $codigo_saida = $data['codigo_saida'] ?? '';
    $responsavel = trim($data['responsavel'] ?? '');
    $data_saida = $data['data_saida'] ?? date('Y-m-d');
    $empresa_destino = trim($data['empresa_destino'] ?? '');
    $destinatario = trim($data['destinatario'] ?? '');
    $setor_destinatario = trim($data['setor_destinatario'] ?? '');
    $motivo = trim($data['motivo'] ?? '');
    $observacoes = trim($data['observacoes'] ?? '');
    $produtos = $data['produtos'] ?? [];

    // Validar dados obrigatórios
    if (empty($responsavel)) {
        throw new Exception('Campo responsável é obrigatório');
    }
    if (empty($empresa_destino)) {
        throw new Exception('Campo empresa_destino é obrigatório');
    }
    if (empty($motivo)) {
        throw new Exception('Campo motivo é obrigatório');
    }
    if (empty($produtos) || !is_array($produtos)) {
        throw new Exception('Pelo menos um produto é obrigatório');
    }

    // Verificar se empresa existe
    $stmt_empresa = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
    $stmt_empresa->bind_param("s", $empresa_destino);
    $stmt_empresa->execute();
    $result_empresa = $stmt_empresa->get_result();

    if ($result_empresa->num_rows === 0) {
        throw new Exception("Empresa com código '$empresa_destino' não encontrada");
    }

    // Iniciar transação
    $conn->begin_transaction();

    // Inserir saída no banco de dados
    $stmt = $conn->prepare("
        INSERT INTO saidas_estoque (responsavel, data_saida, empresa_destino, destinatario, setor_destinatario, motivo, observacoes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");

    if (!$stmt) {
        throw new Exception("Erro ao preparar statement: " . $conn->error);
    }

    $stmt->bind_param("sssssss", $responsavel, $data_saida, $empresa_destino, $destinatario, $setor_destinatario, $motivo, $observacoes);

    if (!$stmt->execute()) {
        throw new Exception("Erro ao executar inserção: " . $stmt->error);
    }

    // Obter o ID da saída inserida
    $saida_id = $conn->insert_id;

    // Inserir produtos da saída
    $stmt_produto = $conn->prepare("
        INSERT INTO produtos_saida (saida_id, codigo, nome, quantidade)
        VALUES (?, ?, ?, ?)
    ");

    foreach ($produtos as $produto) {
        $codigo = trim($produto['codigo'] ?? '');
        $nome = trim($produto['nome'] ?? '');
        $quantidade = intval($produto['quantidade'] ?? 0);

        if (empty($codigo) || empty($nome) || $quantidade <= 0) {
            throw new Exception("Dados inválidos do produto: código='$codigo', nome='$nome', quantidade=$quantidade");
        }

        $stmt_produto->bind_param("issi", $saida_id, $codigo, $nome, $quantidade);
        if (!$stmt_produto->execute()) {
            throw new Exception('Erro ao inserir produto: ' . $stmt_produto->error);
        }

        // Atualizar estoque
        $stmt_update = $conn->prepare("
            UPDATE produtos
            SET quantidade = quantidade - ?
            WHERE codigo = ?
        ");
        $stmt_update->bind_param("is", $quantidade, $codigo);
        $stmt_update->execute();

        // Verificar se a atualização foi bem-sucedida (opcional - produto pode não existir no estoque)
        if ($stmt_update->affected_rows === 0) {
            // Log de aviso mas não interrompe o processo
            error_log("AVISO: Produto $codigo não encontrado no estoque ou quantidade não alterada");
        }
    }
    
    // Calcular valor total da saída para registro de gastos
    $valor_total = 0;
    foreach ($produtos as $produto) {
        // Buscar preço do produto no banco de dados
        $stmt_valor = $conn->prepare("SELECT valor FROM produtos WHERE codigo = ?");
        $stmt_valor->bind_param("s", $produto['codigo']);
        $stmt_valor->execute();
        $result_valor = $stmt_valor->get_result();
        if ($row_valor = $result_valor->fetch_assoc()) {
            $valor_total += $row_valor['valor'] * $produto['quantidade'];
        } else {
            // Se não encontrar valor, usar valor estimado
            $valor_total += $produto['quantidade'] * 10; // Valor estimado por unidade
        }
    }
    
    // Registrar gasto se houver valor total
    if ($valor_total > 0) {
        // Incluir apenas o arquivo de funções (não o endpoint)
        include_once 'funcoes-gastos.php';

        // Buscar nome da empresa pelo código
        $nome_empresa = $empresa_destino; // fallback
        $stmt_empresa = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
        $stmt_empresa->bind_param("s", $empresa_destino);
        $stmt_empresa->execute();
        $result_empresa = $stmt_empresa->get_result();
        if ($result_empresa && $result_empresa->num_rows > 0) {
            $row_empresa = $result_empresa->fetch_assoc();
            $nome_empresa = $row_empresa['nome_empresa'];
        }

        // Chamar função para registrar gasto se existir
        if (function_exists('registrarGasto')) {
            $gasto_registrado = registrarGasto($conn, $nome_empresa, $setor_destinatario, $valor_total, 'saida_estoque', $saida_id);
            // Log opcional para debug
            error_log("Gasto registrado para empresa '$nome_empresa': " . ($gasto_registrado ? 'SIM' : 'NÃO'));
        }
    }
    
    // Commit da transação
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Saída registrada com sucesso',
        'saida_id' => $saida_id
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if (isset($conn)) {
        $conn->rollback();
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>






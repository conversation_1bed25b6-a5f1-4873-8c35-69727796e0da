<?php
include 'conexao.php';

// Verificar se os dados foram enviados
if (!isset($_POST['tipo']) || !isset($_POST['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$tipo = $_POST['tipo'];
$codigo = $_POST['codigo'];
$itens = isset($_POST['itens']) ? json_decode($_POST['itens'], true) : [];

// Iniciar transação
$conn->begin_transaction();

try {
    // Determinar a tabela e o campo de código corretos com base no tipo
    $tabela_principal = '';
    $campo_codigo = '';
    $tabela_itens = '';
    
    switch ($tipo) {
        case 'requisicao':
            $tabela_principal = 'requisicoes';
            $campo_codigo = 'codigo_solicitacao';
            $tabela_itens = 'itens_solicitacao';
            break;
        case 'pedido_mensal':
            $tabela_principal = 'pedidos_mensais';
            $campo_codigo = 'codigo_pedido';
            $tabela_itens = 'itens_pedido_mensal';
            break;
        case 'pedido_especial':
            $tabela_principal = 'pedidos_especiais';
            $campo_codigo = 'codigo_pedido';
            $tabela_itens = ''; // Pedidos especiais não têm itens
            break;
        default:
            throw new Exception('Tipo de solicitação inválido');
    }
    
    // Verificar estoque antes de prosseguir (apenas para requisições e pedidos mensais)
    if ($tipo !== 'pedido_especial' && !empty($itens)) {
        foreach ($itens as $item) {
            // Verificar se há estoque suficiente
            $stmt = $conn->prepare("SELECT quantidade FROM produtos WHERE codigo = ?");
            $stmt->bind_param("s", $item['produto']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                throw new Exception("Produto {$item['produto']} não encontrado");
            }
            
            $produto = $result->fetch_assoc();
            if ($produto['quantidade'] < $item['quantidade']) {
                throw new Exception("Estoque insuficiente para o produto {$item['produto']}");
            }
        }
    }
    
    // Atualizar o estoque (apenas para requisições e pedidos mensais)
    if ($tipo !== 'pedido_especial' && !empty($itens)) {
        $stmt = $conn->prepare("UPDATE produtos SET quantidade = quantidade - ? WHERE codigo = ?");
        
        foreach ($itens as $item) {
            $stmt->bind_param("is", $item['quantidade'], $item['produto']);
            $stmt->execute();
            
            if ($stmt->affected_rows === 0) {
                throw new Exception("Erro ao atualizar estoque do produto {$item['produto']}");
            }
        }
    }
    
    // Verificar se a tabela tem a coluna 'status'
    $check_status = $conn->query("SHOW COLUMNS FROM $tabela_principal LIKE 'status'");
    
    if ($check_status->num_rows > 0) {
        // A coluna 'status' existe, podemos atualizar diretamente
        $stmt = $conn->prepare("UPDATE $tabela_principal SET status = 'concluido' WHERE $campo_codigo = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("Erro ao atualizar status da solicitação");
        }
    } else {
        // A coluna 'status' não existe, precisamos adicioná-la primeiro
        $conn->query("ALTER TABLE $tabela_principal ADD COLUMN status VARCHAR(20) DEFAULT 'pendente'");
        
        // Agora podemos atualizar o status
        $stmt = $conn->prepare("UPDATE $tabela_principal SET status = 'concluido' WHERE $campo_codigo = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("Erro ao atualizar status da solicitação");
        }
    }
    
    // Se for uma requisição, adicionar os produtos à ficha EPI do funcionário
    if ($tipo === 'requisicao') {
        // Obter o ID do funcionário diretamente da requisição
        $stmt = $conn->prepare("SELECT funcionario FROM requisicoes WHERE codigo_solicitacao = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $pessoa_id = $row['funcionario'];
            if (!empty($pessoa_id) && is_numeric($pessoa_id)) {
                // Adicionar cada item à ficha EPI do funcionário
                foreach ($itens as $item) {
                    // Buscar validade_uso do produto
                    $stmt_validade = $conn->prepare("SELECT validade_uso FROM produtos WHERE codigo = ?");
                    $stmt_validade->bind_param("s", $item['produto']);
                    $stmt_validade->execute();
                    $result_validade = $stmt_validade->get_result();
                    $validade_uso = null;
                    if ($result_validade->num_rows > 0) {
                        $row_validade = $result_validade->fetch_assoc();
                        $validade_uso = $row_validade['validade_uso'];
                    }
                    $stmt_epi = $conn->prepare("INSERT INTO pessoa_epi (pessoa_id, produto_id, quantidade, data_entrega, validade_uso) VALUES (?, ?, ?, NOW(), ?)");
                    $stmt_epi->bind_param("isii", $pessoa_id, $item['produto'], $item['quantidade'], $validade_uso);
                    $stmt_epi->execute();
                }
            }
        }
    }
    
    // Commit da transação
    $conn->commit();

    // Registrar gasto após conclusão
    include_once 'funcoes-gastos.php';
    $empresa = '';
    $setor = '';
    $valor_total = 0;
    $tipo_operacao = '';
    $referencia_id = $codigo;



    if ($tipo === 'requisicao') {
        // Buscar empresa (nome) e setor da requisição
        $stmt = $conn->prepare("SELECT empresa, setor FROM requisicoes WHERE codigo_solicitacao = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $empresa_codigo = $row['empresa'];
            $setor = $row['setor'];

            // Se setor estiver vazio, buscar da pessoa
            if (empty($setor)) {
                $stmt_pessoa = $conn->prepare("SELECT p.setor FROM pessoas p JOIN requisicoes r ON p.id = r.funcionario WHERE r.codigo_solicitacao = ?");
                $stmt_pessoa->bind_param("i", $codigo);
                $stmt_pessoa->execute();
                $result_pessoa = $stmt_pessoa->get_result();
                if ($result_pessoa->num_rows > 0) {
                    $row_pessoa = $result_pessoa->fetch_assoc();
                    $setor = $row_pessoa['setor'];
                }
            }

            // Buscar o nome da empresa
            $stmt_nome = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
            $stmt_nome->bind_param("i", $empresa_codigo);
            $stmt_nome->execute();
            $result_nome = $stmt_nome->get_result();
            if ($result_nome->num_rows > 0) {
                $row_nome = $result_nome->fetch_assoc();
                $empresa = $row_nome['nome_empresa'];

            }
        }
        // Calcular valor total dos itens
        foreach ($itens as $item) {
            $stmt_valor = $conn->prepare("SELECT valor FROM produtos WHERE codigo = ?");
            $stmt_valor->bind_param("s", $item['produto']);
            $stmt_valor->execute();
            $result_valor = $stmt_valor->get_result();
            if ($row_valor = $result_valor->fetch_assoc()) {
                $valor_total += $row_valor['valor'] * $item['quantidade'];
            }
        }
        $tipo_operacao = 'requisicao';
    } elseif ($tipo === 'pedido_mensal') {
        // Buscar empresa (nome) e setor do pedido mensal
        $stmt = $conn->prepare("SELECT empresa, setor, funcao FROM pedidos_mensais WHERE codigo_pedido = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $empresa_codigo = $row['empresa'];
            // Usar campo setor se existir, senão usar funcao como fallback
            $setor = !empty($row['setor']) ? $row['setor'] : $row['funcao'];
            // Buscar o nome da empresa
            $stmt_nome = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
            $stmt_nome->bind_param("i", $empresa_codigo);
            $stmt_nome->execute();
            $result_nome = $stmt_nome->get_result();
            if ($result_nome->num_rows > 0) {
                $row_nome = $result_nome->fetch_assoc();
                $empresa = $row_nome['nome_empresa'];
            }
        }
        // Calcular valor total dos itens
        foreach ($itens as $item) {
            $stmt_valor = $conn->prepare("SELECT valor FROM produtos WHERE codigo = ?");
            $stmt_valor->bind_param("s", $item['produto']);
            $stmt_valor->execute();
            $result_valor = $stmt_valor->get_result();
            if ($row_valor = $result_valor->fetch_assoc()) {
                $valor_total += $row_valor['valor'] * $item['quantidade'];
            }
        }
        $tipo_operacao = 'pedido_mensal';
    }
    if (($tipo === 'requisicao' || $tipo === 'pedido_mensal')) {
        if (empty($empresa)) {
            echo json_encode(['success' => false, 'message' => 'Empresa não encontrada para registro de gasto']);
            exit;
        }
        if ($valor_total <= 0) {
            echo json_encode(['success' => false, 'message' => 'Valor total dos itens é zero ou inválido']);
            exit;
        }
        if (empty($tipo_operacao)) {
            echo json_encode(['success' => false, 'message' => 'Tipo de operação não definido']);
            exit;
        }

        registrarGasto($conn, $empresa, $setor, $valor_total, $tipo_operacao, $referencia_id);
    }

    // Retornar sucesso em formato JSON
    echo json_encode(['success' => true]);
    exit;
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    exit;
}
?>








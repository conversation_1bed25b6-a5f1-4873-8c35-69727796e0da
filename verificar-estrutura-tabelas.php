<?php
include 'conexao.php';

echo "<h2>Verificação da Estrutura das Tabelas</h2>";

// Verificar tabela requisicoes
echo "<h3>Tabela: requisicoes</h3>";
$result = $conn->query("DESCRIBE requisicoes");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th><PERSON><PERSON><PERSON></th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Erro ao verificar tabela requisicoes: " . $conn->error;
}

// Verificar tabela pedidos_mensais
echo "<h3>Tabela: pedidos_mensais</h3>";
$result = $conn->query("DESCRIBE pedidos_mensais");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Erro ao verificar tabela pedidos_mensais: " . $conn->error;
}

// Verificar tabela pedidos_especiais
echo "<h3>Tabela: pedidos_especiais</h3>";
$result = $conn->query("DESCRIBE pedidos_especiais");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Erro ao verificar tabela pedidos_especiais: " . $conn->error;
}

echo "<h3>Verificar colunas de data</h3>";

// Verificar colunas de data em requisicoes
$result = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
echo "<p>Colunas de data em requisicoes:</p>";
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
} else {
    echo "Nenhuma coluna de data encontrada em requisicoes<br>";
}

// Verificar colunas de data em pedidos_mensais
$result = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
echo "<p>Colunas de data em pedidos_mensais:</p>";
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
} else {
    echo "Nenhuma coluna de data encontrada em pedidos_mensais<br>";
}

// Verificar colunas de data em pedidos_especiais
$result = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE '%data%'");
echo "<p>Colunas de data em pedidos_especiais:</p>";
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
} else {
    echo "Nenhuma coluna de data encontrada em pedidos_especiais<br>";
}

echo "<h3>Adicionar colunas de data se necessário</h3>";

// Adicionar coluna de data em requisicoes se não existir
$result = $conn->query("SHOW COLUMNS FROM requisicoes LIKE 'data_solicitacao'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE requisicoes ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP";
    if ($conn->query($sql)) {
        echo "✓ Coluna data_solicitacao adicionada à tabela requisicoes<br>";
    } else {
        echo "✗ Erro ao adicionar coluna data_solicitacao à tabela requisicoes: " . $conn->error . "<br>";
    }
} else {
    echo "✓ Coluna data_solicitacao já existe na tabela requisicoes<br>";
}

// Adicionar coluna de data em pedidos_mensais se não existir
$result = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE 'data_solicitacao'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE pedidos_mensais ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP";
    if ($conn->query($sql)) {
        echo "✓ Coluna data_solicitacao adicionada à tabela pedidos_mensais<br>";
    } else {
        echo "✗ Erro ao adicionar coluna data_solicitacao à tabela pedidos_mensais: " . $conn->error . "<br>";
    }
} else {
    echo "✓ Coluna data_solicitacao já existe na tabela pedidos_mensais<br>";
}

// Adicionar coluna de data em pedidos_especiais se não existir
$result = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE 'data_solicitacao'");
if ($result->num_rows === 0) {
    $sql = "ALTER TABLE pedidos_especiais ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP";
    if ($conn->query($sql)) {
        echo "✓ Coluna data_solicitacao adicionada à tabela pedidos_especiais<br>";
    } else {
        echo "✗ Erro ao adicionar coluna data_solicitacao à tabela pedidos_especiais: " . $conn->error . "<br>";
    }
} else {
    echo "✓ Coluna data_solicitacao já existe na tabela pedidos_especiais<br>";
}

echo "<h3>Atualizar registros existentes</h3>";

// Atualizar registros existentes que não têm data
$sql = "UPDATE requisicoes SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL";
if ($conn->query($sql)) {
    echo "✓ Registros atualizados na tabela requisicoes<br>";
} else {
    echo "✗ Erro ao atualizar registros na tabela requisicoes: " . $conn->error . "<br>";
}

$sql = "UPDATE pedidos_mensais SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL";
if ($conn->query($sql)) {
    echo "✓ Registros atualizados na tabela pedidos_mensais<br>";
} else {
    echo "✗ Erro ao atualizar registros na tabela pedidos_mensais: " . $conn->error . "<br>";
}

$sql = "UPDATE pedidos_especiais SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL";
if ($conn->query($sql)) {
    echo "✓ Registros atualizados na tabela pedidos_especiais<br>";
} else {
    echo "✗ Erro ao atualizar registros na tabela pedidos_especiais: " . $conn->error . "<br>";
}

echo "<h3>Verificação concluída!</h3>";
echo "<p><a href='requisicoes-feitas.php'>Voltar para Solicitações Realizadas</a></p>";
?> 
<?php
include 'conexao.php';
$grupos = [];
$status = isset($_GET['status']) ? $_GET['status'] : 'ativo';
$where = '';
if ($status === 'ativo') $where = "WHERE status = 'ativo' OR status IS NULL";
if ($status === 'inativo') $where = "WHERE status = 'inativo'";
$res = $conn->query("SELECT * FROM grupos_pedidos_mensais $where ORDER BY id");
while($g = $res->fetch_assoc()) {
    $g['produtos'] = [];
    $res2 = $conn->query("SELECT gppm.*, p.nome FROM grupo_produtos_pedidos_mensais gppm JOIN produtos p ON gppm.produto_id = p.codigo WHERE grupo_id = " . $g['id']);
    while($p = $res2->fetch_assoc()) {
        $g['produtos'][] = ['codigo'=>$p['produto_id'], 'nome'=>$p['nome'], 'quantidade'=>$p['quantidade']];
    }
    $grupos[] = $g;
}
echo json_encode($grupos); 
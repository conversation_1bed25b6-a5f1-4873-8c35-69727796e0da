<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

try {
    // Buscar todos os produtos ativos
    $sql = "SELECT codigo, nome, categoria, quantidade FROM produtos WHERE status = 'ativo' OR status IS NULL ORDER BY nome ASC";
    $result = $conn->query($sql);
    
    $produtos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $produtos[] = [
                'codigo' => $row['codigo'],
                'nome' => $row['nome'],
                'categoria' => $row['categoria'],
                'quantidade' => $row['quantidade']
            ];
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode($produtos);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro interno do servidor']);
}

$conn->close();
?>

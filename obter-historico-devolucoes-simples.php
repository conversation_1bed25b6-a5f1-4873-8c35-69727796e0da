<?php
// Versão ultra-simples para garantir funcionamento
header('Content-Type: application/json');

session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
}

try {
    require_once 'conexao.php';
    
    $historico = [];
    
    // Verificar e buscar devolucoes_epi
    $result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'");
    if ($result && $result->fetch_assoc()['count'] > 0) {
        $sql = "SELECT id, pessoa_nome, produto_nome, quantidade, estado, data_devolucao FROM devolucoes_epi ORDER BY data_devolucao DESC LIMIT 50";
        $result = $conn->query($sql);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $historico[] = [
                    'id' => $row['id'],
                    'pessoa_id' => null,
                    'pessoa_nome' => $row['pessoa_nome'],
                    'produto_id' => '',
                    'produto_nome' => $row['produto_nome'],
                    'quantidade' => $row['quantidade'],
                    'estado' => $row['estado'],
                    'data_devolucao' => $row['data_devolucao'],
                    'usuario_id' => 1,
                    'usuario_nome' => 'Sistema',
                    'tipo' => 'funcionario',
                    'assinatura' => ''
                ];
            }
        }
    }
    
    // Verificar e buscar devolucoes_rapidas
    $result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'");
    if ($result && $result->fetch_assoc()['count'] > 0) {
        $sql = "SELECT id, produto_nome, quantidade, estado, data_devolucao FROM devolucoes_rapidas ORDER BY data_devolucao DESC LIMIT 50";
        $result = $conn->query($sql);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $historico[] = [
                    'id' => $row['id'],
                    'pessoa_id' => null,
                    'pessoa_nome' => null,
                    'produto_id' => '',
                    'produto_nome' => $row['produto_nome'],
                    'quantidade' => $row['quantidade'],
                    'estado' => $row['estado'],
                    'data_devolucao' => $row['data_devolucao'],
                    'usuario_id' => 1,
                    'usuario_nome' => 'Sistema',
                    'tipo' => 'rapida',
                    'assinatura' => ''
                ];
            }
        }
    }
    
    echo json_encode($historico);
    
} catch (Exception $e) {
    echo json_encode([]);
}
?>

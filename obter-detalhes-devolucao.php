<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

if (!isset($_GET['id']) || !isset($_GET['tipo'])) {
    echo json_encode(['success' => false, 'message' => 'Parâmetros inválidos']);
    exit();
}

$id = intval($_GET['id']);
$tipo = $_GET['tipo'];

try {
    $devolucao = null;
    
    if ($tipo === 'funcionario') {
        // Buscar devolução por funcionário
        $sql = "
            SELECT 
                d.id,
                d.pessoa_id,
                d.pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                u.nome as usuario_nome,
                'funcionario' as tipo,
                d.assinatura,
                p.posto,
                p.setor,
                p.funcao
            FROM devolucoes_epi d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            LEFT JOIN pessoas p ON d.pessoa_id = p.id
            WHERE d.id = ?
        ";
    } else if ($tipo === 'rapida') {
        // Buscar devolução rápida
        $sql = "
            SELECT 
                d.id,
                NULL as pessoa_id,
                NULL as pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                u.nome as usuario_nome,
                'rapida' as tipo,
                d.assinatura,
                NULL as posto,
                NULL as setor,
                NULL as funcao
            FROM devolucoes_rapidas d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            WHERE d.id = ?
        ";
    } else {
        echo json_encode(['success' => false, 'message' => 'Tipo de devolução inválido']);
        exit();
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $devolucao = $result->fetch_assoc();
        
        // Buscar informações adicionais do produto
        $sql_produto = "SELECT categoria, ca, valor FROM produtos WHERE codigo = ?";
        $stmt_produto = $conn->prepare($sql_produto);
        $stmt_produto->bind_param("s", $devolucao['produto_id']);
        $stmt_produto->execute();
        $result_produto = $stmt_produto->get_result();
        
        if ($result_produto && $result_produto->num_rows > 0) {
            $produto_info = $result_produto->fetch_assoc();
            $devolucao['produto_categoria'] = $produto_info['categoria'];
            $devolucao['produto_ca'] = $produto_info['ca'];
            $devolucao['produto_valor'] = $produto_info['valor'];
        }
        
        echo json_encode(['success' => true, 'devolucao' => $devolucao]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Devolução não encontrada']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}

$conn->close();
?>

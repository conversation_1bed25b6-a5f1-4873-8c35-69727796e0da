<?php
include 'conexao.php';

echo "<h2>Teste do Popup Moderno de Confirmação</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Popup Moderno', '001', 'POPUP-001', 'Teste do popup moderno', '1', 'Teste', 'TI', 'Requisição para testar popup moderno', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'POPUP-TEST', 2)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎨 Popup Moderno Implementado!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Melhorias implementadas:</strong></p>";
    echo "<ul>";
    echo "<li>✨ Design moderno com fundo branco</li>";
    echo "<li>🎯 Aparece na frente de todos os outros elementos</li>";
    echo "<li>🎭 Animação suave de entrada</li>";
    echo "<li>🎨 Ícones e cores consistentes com o sistema</li>";
    echo "<li>📱 Responsivo e bem estruturado</li>";
    echo "<li>⚡ Substitui o alert() padrão do navegador</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007cba;'>";
    echo "<h4>📋 Como testar:</h4>";
    echo "<ol>";
    echo "<li>Ir para 'Todas as Solicitações'</li>";
    echo "<li>Localizar a requisição com fundo vermelho claro</li>";
    echo "<li>Clicar na requisição para abrir o popup de detalhes</li>";
    echo "<li>Clicar no botão 'Excluir Pedido Urgente'</li>";
    echo "<li>Verificar o novo popup moderno de confirmação</li>";
    echo "<li>Testar os botões 'Cancelar' e 'Sim, Excluir'</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='todas-solitacoes-estoque.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🚀 Testar Popup Moderno</a></p>";

$conn->close();
?>

<?php
include 'conexao.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido.']);
    exit;
}

$saida_id = isset($_POST['saida_id']) ? intval($_POST['saida_id']) : 0;
if (!$saida_id || !isset($_FILES['assinatura_arquivo'])) {
    echo json_encode(['success' => false, 'message' => 'Dados inválidos.']);
    exit;
}

$file = $_FILES['assinatura_arquivo'];
$allowed = ['image/png', 'image/jpeg', 'application/pdf'];
if (!in_array($file['type'], $allowed)) {
    echo json_encode(['success' => false, 'message' => 'Formato não suportado. Envie PNG, JPG ou PDF.']);
    exit;
}
if ($file['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'Erro no upload do arquivo.']);
    exit;
}

$conteudo = file_get_contents($file['tmp_name']);

$stmt = $conn->prepare('UPDATE saidas_estoque SET assinatura_eletronica = ?, data_assinatura_eletronica = NOW() WHERE id = ?');
$stmt->bind_param('si', $conteudo, $saida_id);
$ok = $stmt->execute();

if ($ok) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Erro ao salvar assinatura.']);
} 
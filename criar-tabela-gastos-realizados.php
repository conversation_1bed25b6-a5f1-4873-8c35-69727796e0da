<?php
include 'conexao.php';

// Criar tabela para registrar gastos realizados
$sql = "CREATE TABLE IF NOT EXISTS gastos_realizados (
    id INT AUTO_INCREMENT PRIMARY KEY,
    limite_id INT NOT NULL,
    tipo_operacao ENUM('saida_estoque', 'requisicao', 'pedido_mensal') NOT NULL,
    empresa VARCHAR(255) NOT NULL,
    setor VARCHAR(255),
    valor_total DECIMAL(15,2) NOT NULL,
    data_operacao DATE NOT NULL,
    referencia_id INT NOT NULL,
    data_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (limite_id) REFERENCES limites_gastos(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Tabela gastos_realizados criada com sucesso!<br>";
} else {
    echo "Erro ao criar tabela: " . $conn->error . "<br>";
}

$conn->close();
echo "Script executado com sucesso!";
?> 
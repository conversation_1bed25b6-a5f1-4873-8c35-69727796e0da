<?php
include 'conexao.php';

if (!isset($_GET['id'])) {
    die("ID inválido!");
}

$entrada_id = $_GET['id'];

// Buscar informações da entrada
$stmt = $conn->prepare("SELECT * FROM entradas_estoque WHERE id = ?");
$stmt->bind_param("i", $entrada_id);
$stmt->execute();
$entrada = $stmt->get_result()->fetch_assoc();

// Buscar produtos da entrada
$stmt = $conn->prepare("SELECT * FROM produtos_entrada WHERE entrada_id = ?");
$stmt->bind_param("i", $entrada_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Entrada</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Estilo para o pop-up */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .popup {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 600px;
        }

        .popup h3 {
            margin-top: 0;
            color: #333;
            margin-bottom: 20px;
        }

        .popup table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .popup th, .popup td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .popup th {
            background-color: #4a89dc;
            color: white;
        }

        .popup tr:hover {
            background-color: #f9f9f9;
        }

        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #4a89dc;
        }

        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-confirm {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-cancel {
            background-color: #95a5a6;
            color: white;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<h1>Detalhes da Entrada #<?= $entrada_id ?></h1>

<button class="btn btn-confirm" onclick="abrirDetalhes()">Ver Detalhes</button>
<a href="registro-entradas.php" class="btn btn-cancel">Voltar</a>

<!-- Pop-up de detalhes -->
<div id="detalhesPopup" class="popup-overlay">
    <div class="popup">
        <h3>Detalhes da Entrada #<?= $entrada_id ?></h3>
        
        <div class="info-section">
            <p><strong>Data e Hora:</strong> <?= date('d/m/Y H:i:s', strtotime($entrada['data_hora'])) ?></p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Quantidade</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['codigo'] ?></td>
                        <td><?= $row['nome'] ?></td>
                        <td><?= $row['quantidade'] ?></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
        
        <div class="form-buttons">
            <button class="btn btn-danger" onclick="confirmarCancelamento()">Cancelar Entrada</button>
            <button class="btn btn-cancel" onclick="fecharDetalhes()">Fechar</button>
        </div>
    </div>
</div>

<!-- Pop-up de confirmação de cancelamento -->
<div id="cancelarPopup" class="popup-overlay">
    <div class="popup">
        <h3>Confirmar Cancelamento</h3>
        <p>Tem certeza que deseja cancelar esta entrada? Esta ação irá:</p>
        <ul>
            <li>Remover as quantidades adicionadas ao estoque</li>
            <li>Marcar esta entrada como cancelada</li>
        </ul>
        <p><strong>Esta ação não pode ser desfeita!</strong></p>
        
        <div class="form-buttons">
            <button class="btn btn-cancel" onclick="fecharCancelamento()">Não, Voltar</button>
            <button class="btn btn-danger" onclick="cancelarEntrada()">Sim, Cancelar Entrada</button>
        </div>
    </div>
</div>

<script>
    function abrirDetalhes() {
        document.getElementById('detalhesPopup').style.display = 'flex';
    }
    
    function fecharDetalhes() {
        document.getElementById('detalhesPopup').style.display = 'none';
    }
    
    function confirmarCancelamento() {
        document.getElementById('detalhesPopup').style.display = 'none';
        document.getElementById('cancelarPopup').style.display = 'flex';
    }
    
    function fecharCancelamento() {
        document.getElementById('cancelarPopup').style.display = 'none';
        document.getElementById('detalhesPopup').style.display = 'flex';
    }
    
    function cancelarEntrada() {
        // Enviar solicitação para cancelar a entrada
        fetch('cancelar-entrada.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                entrada_id: <?= $entrada_id ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Entrada cancelada com sucesso!');
                window.location.href = 'registro-entradas.php';
            } else {
                alert('Erro ao cancelar entrada: ' + data.message);
                fecharCancelamento();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao processar a solicitação.');
            fecharCancelamento();
        });
    }
    
    // Abrir automaticamente o popup de detalhes ao carregar a página
    window.onload = function() {
        abrirDetalhes();
    };
</script>

</body>
</html>

<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $codigo = $_POST['codigo'] ?? '';
    $tipo = $_POST['tipo'] ?? '';
    
    if (empty($codigo) || empty($tipo)) {
        echo json_encode(['success' => false, 'message' => 'Código e tipo são obrigatórios']);
        exit();
    }
    
    $nome = '';
    
    if ($tipo === 'empresa') {
        $sql = "SELECT nome FROM empresas WHERE codigo = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $codigo);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $nome = $row['nome'];
        }
        $stmt->close();
    } elseif ($tipo === 'setor') {
        $sql = "SELECT nome FROM setores WHERE codigo = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $codigo);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $nome = $row['nome'];
        }
        $stmt->close();
    }
    
    if ($nome) {
        echo json_encode(['success' => true, 'nome' => $nome]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Código não encontrado']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
}

$conn->close();
?> 
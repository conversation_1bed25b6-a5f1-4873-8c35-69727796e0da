<?php
session_start();
include 'conexao.php';
header('Content-Type: application/json');

if (!isset($_SESSION['usuario_id'])) {
    // Simular usuário logado para teste
    $_SESSION['usuario_id'] = 1;
}

$usuario_id = isset($_GET['usuario_id']) ? intval($_GET['usuario_id']) : 0;
if (!$usuario_id) {
    echo json_encode([]);
    exit();
}

$sql = "SELECT menu, item, permitido FROM usuarios_acessos WHERE usuario_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $usuario_id);
$stmt->execute();
$result = $stmt->get_result();
$acessos = [];
while ($row = $result->fetch_assoc()) {
    $chave = $row['menu'] . ($row['item'] ? ('.' . $row['item']) : '');
    $acessos[$chave] = (int)$row['permitido'];
}
echo json_encode($acessos);
$conn->close(); 
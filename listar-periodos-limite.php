<?php
include 'conexao.php';
header('Content-Type: text/html; charset=utf-8');

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if (!$id) {
    echo '<div style="color:#dc2626;">ID do limite não informado.</div>';
    exit;
}
// Buscar dados do limite
$limite = $conn->query("SELECT * FROM limites_gastos WHERE id = $id")->fetch_assoc();
if (!$limite) {
    echo '<div style="color:#dc2626;">Limite não encontrado.</div>';
    exit;
}
$criado_em = $limite['criado_em'];
$periodicidade = intval($limite['periodicidade']);
$tipo = $limite['tipo'];
$codigo = $tipo === 'empresa' ? $limite['id_empresa'] : $limite['id_setor'];
if (!$criado_em || !$periodicidade || !$codigo) {
    echo '<div style="color:#dc2626;">Dados do limite incompletos.</div>';
    exit;
}
// Fechar períodos antigos se necessário
$now = new DateTime();
$inicio = new DateTime($criado_em);
$periodos = [];
while ($inicio < $now) {
    $fim = clone $inicio;
    $fim->modify("+{$periodicidade} month");
    // Verifica se já existe registro deste período
    $sqlCheck = "SELECT id FROM registros_periodos_limite WHERE limite_id = ? AND periodo_inicio = ? AND periodo_fim = ?";
    $stmt = $conn->prepare($sqlCheck);
    $inicioStr = $inicio->format('Y-m-d 00:00:00');
    $fimStr = $fim->format('Y-m-d 00:00:00');
    $stmt->bind_param('iss', $id, $inicioStr, $fimStr);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows == 0 && $fim < $now) {
        // Buscar gastos do período
        $params = [
            'tipo' => $tipo,
            'codigo' => $codigo,
            'data_inicio' => $inicioStr,
            'data_fim' => $fimStr
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/gastos-por-limite.php');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);
        $data = json_decode($result, true);
        $dados_json = json_encode($data['gastos'] ?? []);
        $totalQtd = $data['totalQtd'] ?? 0;
        $totalValor = $data['totalValor'] ?? 0;
        $totalValorBruto = $data['totalValorBruto'] ?? 0;
        // Salvar no banco
        $sqlIns = "INSERT INTO registros_periodos_limite (limite_id, periodo_inicio, periodo_fim, total_quantidade, total_valor, total_valor_bruto, dados_json) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt2 = $conn->prepare($sqlIns);
        $stmt2->bind_param('issidds', $id, $inicioStr, $fimStr, $totalQtd, $totalValor, $totalValorBruto, $dados_json);
        $stmt2->execute();
        $stmt2->close();
    }
    $stmt->close();
    $periodos[] = ['inicio' => $inicioStr, 'fim' => $fimStr];
    $inicio = $fim;
}
// Calcular período vigente corretamente
$vigente_inicio = null;
$vigente_fim = null;
$ultimo_fechado = $conn->query("SELECT MAX(periodo_fim) as max_fim FROM registros_periodos_limite WHERE limite_id = $id")->fetch_assoc();
if ($ultimo_fechado && $ultimo_fechado['max_fim']) {
    // O início do vigente é o dia seguinte ao último fim fechado
    $vigente_inicio = (new DateTime($ultimo_fechado['max_fim']))->modify('+1 day')->format('Y-m-d');
} else {
    $vigente_inicio = (new DateTime($criado_em))->format('Y-m-d');
}
$vigente_fim = (new DateTime($vigente_inicio))->modify("+{$periodicidade} month -1 day")->format('Y-m-d');
// Buscar gastos do período vigente
$params = [
    'tipo' => $tipo,
    'codigo' => $codigo,
    'data_inicio' => $vigente_inicio . ' 00:00:00',
    'data_fim' => $vigente_fim . ' 23:59:59'
];
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/gastos-por-limite.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$result = curl_exec($ch);
curl_close($ch);
$data = json_decode($result, true);
$dados_json = json_encode($data['gastos'] ?? []);
$totalQtd = $data['totalQtd'] ?? 0;
$totalValor = $data['totalValor'] ?? 0;
$totalValorBruto = $data['totalValorBruto'] ?? 0;
// Exibir período vigente SEMPRE
$inicio_fmt = (new DateTime($vigente_inicio))->format('d/m/Y');
$fim_fmt = (new DateTime($vigente_fim))->format('d/m/Y');
echo '<div style="margin-bottom:18px;">';
echo '<div style="font-weight:bold;color:#2563eb;">Período Vigente (em andamento):</div>';
echo '<input type="hidden" id="periodoVigenteInicio" value="' . $vigente_inicio . '">';
echo '<input type="hidden" id="periodoVigenteFim" value="' . $vigente_fim . '">';
echo '<table class="modern-table" style="min-width:700px;margin-bottom:0;">';
echo '<thead><tr><th>Período</th><th>Qtd Total</th><th>Valor Total</th><th>Valor Bruto Total</th><th>Detalhes</th></tr></thead><tbody>';
echo '<tr>';
echo '<td>' . $inicio_fmt . ' a ' . $fim_fmt . '</td>';
echo '<td>' . intval($totalQtd) . '</td>';
echo '<td>R$ ' . number_format($totalValor, 2, ',', '.') . '</td>';
echo '<td>R$ ' . number_format($totalValorBruto, 2, ',', '.') . '</td>';
echo '<td><button type="button" class="btn-modern btn-confirm" onclick="mostrarDetalhesPeriodoLimite(this)" data-detalhes="' . htmlspecialchars($dados_json, ENT_QUOTES) . '" data-inicio="' . $vigente_inicio . '" data-fim="' . $vigente_fim . '">Ver Itens</button></td>';
echo '</tr>';
echo '</tbody></table>';
echo '</div>';
// Buscar todos os períodos salvos
$sql = "SELECT * FROM registros_periodos_limite WHERE limite_id = ? ORDER BY periodo_inicio DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $id);
$stmt->execute();
$res = $stmt->get_result();
if ($res->num_rows == 0) {
    echo '<div style="color:#888;">Nenhum período fechado encontrado.</div>';
    exit;
}
echo '<div style="overflow-x:auto;">';
echo '<table class="modern-table" style="min-width:700px;">';
echo '<thead><tr><th>Período</th><th>Qtd Total</th><th>Valor Total</th><th>Valor Bruto Total</th><th>Detalhes</th></tr></thead><tbody>';
while ($row = $res->fetch_assoc()) {
    $inicio = (new DateTime($row['periodo_inicio']))->format('d/m/Y');
    $fim = (new DateTime($row['periodo_fim']))->format('d/m/Y');
    $detalhes = htmlspecialchars($row['dados_json']);
    echo '<tr>';
    echo '<td>' . $inicio . ' a ' . $fim . '</td>';
    echo '<td>' . intval($row['total_quantidade']) . '</td>';
    echo '<td>R$ ' . number_format($row['total_valor'], 2, ',', '.') . '</td>';
    echo '<td>R$ ' . number_format($row['total_valor_bruto'], 2, ',', '.') . '</td>';
    echo '<td><button type="button" class="btn-modern btn-confirm" onclick="mostrarDetalhesPeriodoLimite(this)" data-detalhes="' . htmlspecialchars($row['dados_json'], ENT_QUOTES) . '">Ver Itens</button></td>';
    echo '</tr>';
}
echo '</tbody></table></div>';
echo "<script>
function mostrarDetalhesPeriodoLimite(btn){
    const dados=JSON.parse(btn.getAttribute('data-detalhes'));
    let html='<table class=\"modern-table\"><thead><tr><th>Data</th><th>Origem</th><th>Produto</th><th>Qtd</th><th>Valor</th><th>Valor Bruto</th></tr></thead><tbody>';
    if(dados.length===0){
        html+='<tr><td colspan=6 style=\"text-align:center;color:#888;\">Nenhum item</td></tr>';
    }else{
        dados.forEach(function(g){
            html+='<tr><td>'+(g.data?g.data.split(' ')[0]:'')+'</td><td>'+g.origem+'</td><td>'+g.produto+'</td><td>'+g.quantidade+'</td><td>R$ '+(parseFloat(g.valor)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})+'</td><td>R$ '+(parseFloat(g.valor_bruto)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})+'</td></tr>';
        });
    }
    html+='</tbody></table>';
    const d=document.createElement('div');
    d.innerHTML=html;
    d.style.maxHeight='400px';
    d.style.overflowY='auto';
    d.style.marginTop='18px';
    btn.parentNode.appendChild(d);
    btn.style.display='none';
    // Preencher datas se for período vigente
    if (btn.dataset.inicio && btn.dataset.fim) {
        if (document.getElementById('gastos_data_inicio')) {
            document.getElementById('gastos_data_inicio').value = btn.dataset.inicio.slice(0,10);
        }
        if (document.getElementById('gastos_data_fim')) {
            document.getElementById('gastos_data_fim').value = btn.dataset.fim.slice(0,10);
        }
    }
}
// Preencher datas automaticamente ao carregar o popup, se existir período vigente
setTimeout(function() {
    var inicio = document.getElementById('periodoVigenteInicio');
    var fim = document.getElementById('periodoVigenteFim');
    if (inicio && fim) {
        if (document.getElementById('gastos_data_inicio')) {
            document.getElementById('gastos_data_inicio').value = inicio.value;
        }
        if (document.getElementById('gastos_data_fim')) {
            document.getElementById('gastos_data_fim').value = fim.value;
        }
    }
}, 200);
</script>"; 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

include 'conexao.php';

try {
    $sql = "SELECT codigo, nome FROM produtos WHERE status = 'ativo' ORDER BY nome ASC";
    $result = $conn->query($sql);
    
    $produtos = [];
    while ($row = $result->fetch_assoc()) {
        $produtos[] = [
            'codigo' => $row['codigo'],
            'nome' => $row['nome']
        ];
    }
    
    echo json_encode(['success' => true, 'produtos' => $produtos]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}
?>

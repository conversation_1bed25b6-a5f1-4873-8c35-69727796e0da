<?php
include 'conexao.php';

$tipo = isset($_GET['tipo']) ? $_GET['tipo'] : '';
$id = isset($_GET['id']) ? $_GET['id'] : 0;

if (empty($tipo) || $id <= 0) {
    header('Location: registro-saidas.php');
    exit;
}

// Determinar a tabela e os campos com base no tipo
switch ($tipo) {
    case 'requisicao':
        $tabela = 'requisicoes';
        $id_campo = 'codigo_solicitacao';
        $itens_tabela = 'itens_solicitacao';
        $itens_id_campo = 'codigo_solicitacao';
        $titulo = 'Requisição';
        break;
    case 'pedido_mensal':
        $tabela = 'pedidos_mensais';
        $id_campo = 'codigo_pedido';
        $itens_tabela = 'itens_pedido_mensal';
        $itens_id_campo = 'codigo_pedido';
        $titulo = 'Pedido Mensal';
        break;
    case 'pedido_especial':
        $tabela = 'pedidos_especiais';
        $id_campo = 'codigo_pedido';
        $itens_tabela = ''; // Pedidos especiais não têm itens
        $itens_id_campo = '';
        $titulo = 'Pedido Especial';
        break;
    default:
        header('Location: registro-saidas.php');
        exit;
}

// Buscar detalhes da solicitação
$stmt = $conn->prepare("SELECT * FROM $tabela WHERE $id_campo = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: registro-saidas.php');
    exit;
}

$solicitacao = $result->fetch_assoc();

// Buscar nome e setor do funcionário/destinatário se for requisicao ou pedido_mensal
$nome_funcionario = '-';
$setor_funcionario = '-';
if (($tipo === 'requisicao' || $tipo === 'pedido_mensal') && !empty($solicitacao['funcionario'])) {
    $stmt_func = $conn->prepare("SELECT nome, setor FROM pessoas WHERE id = ?");
    $stmt_func->bind_param("i", $solicitacao['funcionario']);
    $stmt_func->execute();
    $result_func = $stmt_func->get_result();
    if ($row_func = $result_func->fetch_assoc()) {
        $nome_funcionario = $row_func['nome'];
        $setor_funcionario = $row_func['setor'];
    }
}

// Buscar itens da solicitação (se aplicável)
$itens = [];
if (!empty($itens_tabela)) {
    $stmt = $conn->prepare("
        SELECT i.*, p.nome as nome_produto 
        FROM $itens_tabela i
        LEFT JOIN produtos p ON i.produto = p.codigo
        WHERE i.$itens_id_campo = ?
    ");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $itens[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Solicitação</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .info-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 25px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .info-item {
            margin-bottom: 10px;
        }
        
        .info-item strong {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }
        
        .info-item span {
            display: block;
            padding: 8px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .buttons {
            display: flex;
            justify-content: space-between;
        }
        
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-concluido {
            background-color: #4CAF50;
            color: white;
        }
        
        .status-pendente {
            background-color: #ff9800;
            color: white;
        }
        
        .status-cancelado {
            background-color: #f44336;
            color: white;
        }
        
        .assinatura-container {
            margin-top: 30px;
            padding: 20px;
            border: 1px dashed #ccc;
            border-radius: 5px;
        }
        
        .assinatura-linha {
            margin: 20px 0;
            border-bottom: 1px solid #000;
        }
        
        .assinatura-nome {
            text-align: center;
            margin-top: 10px;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                padding: 0;
                background-color: #fff;
            }
            
            .container {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    
    <div class="container">
        <h1>
            Detalhes da <?= $titulo ?> #<?= $id ?>
            <span class="status-badge status-<?= $solicitacao['status'] ?? 'concluido' ?>"><?= ucfirst($solicitacao['status'] ?? 'Concluído') ?></span>
        </h1>
        
        <div class="info-section">
            <div class="info-grid">
                <div class="info-item">
                    <strong>Solicitante:</strong>
                    <span><?= $solicitacao['solicitante'] ?? '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Empresa:</strong>
                    <span><?= $solicitacao['empresa'] ?? '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Contrato:</strong>
                    <span><?= $solicitacao['contrato'] ?? '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Finalidade:</strong>
                    <span><?= $solicitacao['finalidade'] ?? '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Funcionário:</strong>
                    <span><?= $nome_funcionario ?></span>
                </div>
                <div class="info-item">
                    <strong>Setor:</strong>
                    <span><?= $setor_funcionario ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Função:</strong>
                    <span><?= $solicitacao['funcao'] ?? '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Data:</strong>
                    <span><?= isset($solicitacao['data_solicitacao']) ? date('d/m/Y', strtotime($solicitacao['data_solicitacao'])) : '-' ?></span>
                </div>
                
                <div class="info-item">
                    <strong>Observação:</strong>
                    <span><?= $solicitacao['observacao'] ?? $solicitacao['detalhes'] ?? '-' ?></span>
                </div>
            </div>
        </div>
        
        <?php if (!empty($itens)): ?>
            <h2>Itens da Solicitação</h2>
            <table>
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Produto</th>
                        <th>Quantidade</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($itens as $item): ?>
                        <tr>
                            <td><?= $item['produto'] ?></td>
                            <td><?= $item['nome_produto'] ?? '-' ?></td>
                            <td><?= $item['quantidade'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        
        <div class="assinatura-container">
            <h3>Assinatura do Recebedor</h3>
            <div class="assinatura-linha"></div>
            <div class="assinatura-nome">Nome: _____________________________</div>
        </div>
        
        <div class="buttons no-print">
            <a href="registro-saidas.php" class="btn btn-secondary">Voltar</a>
            <button onclick="window.print()" class="btn btn-primary">Imprimir</button>
        </div>
    </div>
    
    <script>
        // Script para imprimir a página
        function imprimirPagina() {
            window.print();
        }
    </script>
</body>
</html>

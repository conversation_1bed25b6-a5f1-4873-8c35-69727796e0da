<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Grid Layout</title>
    <style>
        :root {
            --primary-color: #6366f1;
            --card-bg: #ffffff;
            --light-bg: #f8fafc;
            --text-primary: #1e293b;
            --border-color: #e2e8f0;
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius-lg: 12px;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: var(--light-bg);
            margin: 0;
            padding: 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }

        .modern-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            min-height: 400px;
        }

        .card-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--light-bg) 0%, #ffffff 100%);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-content {
            padding: 24px;
        }

        .card-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            text-align: center;
            margin: 20px 0;
        }

        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <h1>Teste de Grid Layout - Dashboard</h1>
    <p>Verificando se os cards ficam lado a lado</p>

    <div class="dashboard-grid">
        <!-- Card 1 -->
        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-chart-pie"></i>
                    Gastos por Setor
                </h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 1</div>
                <p>Este é o primeiro card que deve ficar lado a lado com os outros.</p>
            </div>
        </div>

        <!-- Card 2 -->
        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-contract"></i>
                    Valor Total por Contrato
                </h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 2</div>
                <p>Este é o segundo card que deve ficar lado a lado com os outros.</p>
            </div>
        </div>

        <!-- Card 3 -->
        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt"></i>
                    EPIs Próximos do Vencimento
                </h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 3</div>
                <p>Este é o terceiro card que deve ficar lado a lado com os outros.</p>
            </div>
        </div>

        <!-- Cards adicionais para testar o grid -->
        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">Card 4</h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 4</div>
                <p>Card adicional para testar o grid.</p>
            </div>
        </div>

        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">Card 5</h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 5</div>
                <p>Card adicional para testar o grid.</p>
            </div>
        </div>

        <div class="modern-card">
            <div class="card-header">
                <h3 class="card-title">Card 6</h3>
            </div>
            <div class="card-content">
                <div class="card-number">Card 6</div>
                <p>Card adicional para testar o grid.</p>
            </div>
        </div>
    </div>

    <script>
        // Mostrar informações sobre o grid
        window.addEventListener('load', function() {
            const grid = document.querySelector('.dashboard-grid');
            const cards = document.querySelectorAll('.modern-card');
            
            console.log('Grid container:', grid);
            console.log('Total de cards:', cards.length);
            console.log('Grid computed style:', window.getComputedStyle(grid).gridTemplateColumns);
            
            // Adicionar informações na tela
            const info = document.createElement('div');
            info.style.cssText = 'position: fixed; top: 10px; right: 10px; background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); font-size: 12px; z-index: 1000;';
            info.innerHTML = `
                <strong>Grid Info:</strong><br>
                Cards: ${cards.length}<br>
                Largura da tela: ${window.innerWidth}px<br>
                Grid columns: ${window.getComputedStyle(grid).gridTemplateColumns}
            `;
            document.body.appendChild(info);
        });
    </script>
</body>
</html>

<?php
include 'conexao.php';

// Consultar produtos
$sql = "SELECT codigo, nome, quantidade FROM produtos WHERE status = 'ativo' ORDER BY nome";
$result = $conn->query($sql);

if (!$result) {
    echo json_encode(['success' => false, 'message' => 'Erro ao consultar produtos: ' . $conn->error]);
    exit;
}

$produtos = [];
while ($row = $result->fetch_assoc()) {
    $produtos[] = $row;
}

echo json_encode(['success' => true, 'produtos' => $produtos]);
?>

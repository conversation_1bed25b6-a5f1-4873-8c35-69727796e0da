<?php
require_once 'conexao.php';

echo "<h2>Teste das Correções de Devolução</h2>";

// 1. Verificar se os campos de devolução existem na tabela pessoa_epi
echo "<h3>1. Verificação dos Campos de Devolução:</h3>";
$result = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'status_devolucao'");
echo "<p>Campo 'status_devolucao': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

$result = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'data_devolucao'");
echo "<p>Campo 'data_devolucao': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

$result = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'quantidade_devolvida'");
echo "<p>Campo 'quantidade_devolvida': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

// 2. Testar consulta de produtos por pessoa
echo "<h3>2. Teste de Quantidades Corretas:</h3>";
$sql_pessoas_com_epi = "
    SELECT DISTINCT pe.pessoa_id, p.nome 
    FROM pessoa_epi pe 
    JOIN pessoas p ON pe.pessoa_id = p.id 
    WHERE p.status = 'ativo' 
    LIMIT 3
";
$result = $conn->query($sql_pessoas_com_epi);

if ($result && $result->num_rows > 0) {
    while ($pessoa = $result->fetch_assoc()) {
        echo "<h4>Pessoa: {$pessoa['nome']} (ID: {$pessoa['pessoa_id']})</h4>";
        
        // Testar consulta antiga vs nova
        echo "<strong>Consulta Antiga (apenas última entrega):</strong><br>";
        $sql_antiga = "
            SELECT pe.produto_id, p.nome, pe.quantidade, pe.data_entrega
            FROM pessoa_epi pe
            INNER JOIN (
                SELECT produto_id, MAX(data_entrega) as max_data_entrega
                FROM pessoa_epi
                WHERE pessoa_id = {$pessoa['pessoa_id']}
                GROUP BY produto_id
            ) as pe_recente 
                ON pe.produto_id = pe_recente.produto_id 
                AND pe.data_entrega = pe_recente.max_data_entrega
            JOIN produtos p ON pe.produto_id = p.codigo
            WHERE pe.pessoa_id = {$pessoa['pessoa_id']}
        ";
        
        $result_antiga = $conn->query($sql_antiga);
        if ($result_antiga) {
            while ($row = $result_antiga->fetch_assoc()) {
                echo "- {$row['nome']}: {$row['quantidade']} (entrega: {$row['data_entrega']})<br>";
            }
        }
        
        echo "<br><strong>Consulta Nova (total disponível):</strong><br>";
        $sql_nova = "
            SELECT 
                pe.produto_id,
                p.nome,
                SUM(pe.quantidade) - COALESCE(SUM(pe.quantidade_devolvida), 0) as quantidade_disponivel
            FROM pessoa_epi pe
            JOIN produtos p ON pe.produto_id = p.codigo
            WHERE pe.pessoa_id = {$pessoa['pessoa_id']}
            GROUP BY pe.produto_id, p.nome
            HAVING quantidade_disponivel > 0
        ";
        
        $result_nova = $conn->query($sql_nova);
        if ($result_nova) {
            while ($row = $result_nova->fetch_assoc()) {
                echo "- {$row['nome']}: {$row['quantidade_disponivel']} disponível<br>";
            }
        }
        
        echo "<hr>";
    }
} else {
    echo "<p>Nenhuma pessoa com EPIs encontrada</p>";
}

// 3. Verificar produtos SEMI existentes
echo "<h3>3. Produtos SEMI Existentes:</h3>";
$sql_semi = "SELECT codigo, nome, quantidade FROM produtos WHERE nome LIKE '% SEMI' ORDER BY nome";
$result_semi = $conn->query($sql_semi);

if ($result_semi && $result_semi->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Código</th><th>Nome</th><th>Quantidade</th></tr>";
    while ($row = $result_semi->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['codigo']}</td>";
        echo "<td>{$row['nome']}</td>";
        echo "<td>{$row['quantidade']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Nenhum produto SEMI encontrado ainda</p>";
}

// 4. Verificar devoluções registradas
echo "<h3>4. Devoluções Registradas:</h3>";
$result = $conn->query("SHOW TABLES LIKE 'devolucoes_epi'");
if ($result->num_rows > 0) {
    $sql_devolucoes = "SELECT * FROM devolucoes_epi ORDER BY data_devolucao DESC LIMIT 5";
    $result_dev = $conn->query($sql_devolucoes);
    
    if ($result_dev && $result_dev->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Pessoa</th><th>Produto</th><th>Quantidade</th><th>Estado</th><th>Data</th></tr>";
        while ($row = $result_dev->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['pessoa_nome']}</td>";
            echo "<td>{$row['produto_nome']}</td>";
            echo "<td>{$row['quantidade']}</td>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($row['data_devolucao'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Nenhuma devolução registrada ainda</p>";
    }
} else {
    echo "<p>Tabela de devoluções será criada automaticamente</p>";
}

echo "<h3>5. Correções Implementadas:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Ficha EPI:</strong> Adicionados campos status_devolucao, data_devolucao, quantidade_devolvida</li>";
echo "<li>✅ <strong>Exibição na ficha:</strong> Status de devolução aparece em ficha-epi-detalhes.php</li>";
echo "<li>✅ <strong>Quantidades corretas:</strong> Consulta soma total - devolvido</li>";
echo "<li>✅ <strong>Produtos SEMI:</strong> Criados automaticamente para usado_bom e usado_leve</li>";
echo "<li>✅ <strong>Estoque original:</strong> Atualizado apenas para estado 'novo'</li>";
echo "<li>✅ <strong>Não volta ao estoque:</strong> danificado_irrecuperavel, vencido, descarte</li>";
echo "</ul>";

echo "<h3>6. Próximos Passos para Teste:</h3>";
echo "<ol>";
echo "<li>Acesse <a href='devolucao.php' target='_blank'>devolucao.php</a></li>";
echo "<li>Selecione uma pessoa com EPIs</li>";
echo "<li>Faça uma devolução de teste com diferentes estados</li>";
echo "<li>Verifique em <a href='fichas-epi.php' target='_blank'>fichas-epi.php</a> se o status aparece</li>";
echo "<li>Verifique em <a href='tabela-produtos.php' target='_blank'>tabela-produtos.php</a> se os produtos SEMI foram criados</li>";
echo "</ol>";

$conn->close();
?>

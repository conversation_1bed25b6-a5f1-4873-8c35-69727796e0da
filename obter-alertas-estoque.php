<?php
include 'conexao.php';

// Verificar se a coluna estoque_minimo existe
$result = $conn->query("SHOW COLUMNS FROM produtos LIKE 'estoque_minimo'");
if ($result->num_rows == 0) {
    // Se não existir, criar a coluna
    $conn->query("ALTER TABLE produtos ADD COLUMN estoque_minimo INT DEFAULT 0");
}

// Buscar produtos com estoque abaixo do mínimo
$sql = "SELECT codigo, nome, quantidade, estoque_minimo 
        FROM produtos 
        WHERE quantidade <= estoque_minimo AND estoque_minimo > 0 
        ORDER BY (estoque_minimo - quantidade) DESC";

$result = $conn->query($sql);
$produtos = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $produtos[] = $row;
    }
}

// Retornar como JSON
header('Content-Type: application/json');
echo json_encode($produtos);
?>
<?php
session_start();
require_once 'conexao.php';

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: fichas-epi.php');
    exit;
}

$id_pessoa = intval($_GET['id']);

// Obter dados da pessoa
$sql_pessoa = "SELECT id, nome, posto, setor, funcao, data_admissao FROM pessoas WHERE id = ?";
$stmt = $conn->prepare($sql_pessoa);
$stmt->bind_param("i", $id_pessoa);
$stmt->execute();
$result_pessoa = $stmt->get_result();

if ($result_pessoa->num_rows === 0) {
    header('Location: fichas-epi.php');
    exit;
}

$pessoa = $result_pessoa->fetch_assoc();

// 1. Obter as datas das entregas mais recentes de cada tipo de EPI
$sql_recentes = "SELECT produto_id, MAX(data_entrega) as max_data_entrega FROM pessoa_epi WHERE pessoa_id = ? GROUP BY produto_id";
$stmt_recentes = $conn->prepare($sql_recentes);
$stmt_recentes->bind_param("i", $id_pessoa);
$stmt_recentes->execute();
$result_recentes = $stmt_recentes->get_result();
$epis_recentes = [];
while ($row_recente = $result_recentes->fetch_assoc()) {
    $epis_recentes[$row_recente['produto_id']] = $row_recente['max_data_entrega'];
}
$stmt_recentes->close();

// Obter TODOS os EPIs associados à pessoa para exibir o histórico completo
$sql_epis = "
    SELECT
        pe.id, p.codigo, p.nome, p.ca, pe.quantidade, pe.data_entrega, pe.assinatura, p.validade_uso, pe.produto_id, pe.data_assinatura,
        pe.status_devolucao, pe.data_devolucao, pe.quantidade_devolvida
    FROM
        pessoa_epi pe
    JOIN
        produtos p ON pe.produto_id = p.codigo
    WHERE
        pe.pessoa_id = ?
    ORDER BY
        pe.data_entrega DESC, p.nome ASC
";
$stmt = $conn->prepare($sql_epis);
$stmt->bind_param("i", $id_pessoa);
$stmt->execute();
$result_epis = $stmt->get_result();

$produtos_epi = [];
while ($row = $result_epis->fetch_assoc()) {
    $produtos_epi[] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ficha EPI - <?php echo htmlspecialchars($pessoa['nome']); ?></title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px;
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .info-pessoa {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        
        .produtos-container {
            margin-top: 30px;
        }
        
        .produto-item {
            display: flex;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .produto-info {
            flex: 1;
            padding: 15px;
            background-color: #f9f9f9;
        }
        
        .produto-nome {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .produto-codigo {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .produto-quantidade {
            font-size: 16px;
        }
        
        .produto-data {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .produto-vencimento {
            margin-top: 10px;
            font-size: 14px;
        }
        .badge-ativo, .badge-vencido, .badge-substituido {
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-weight: 500;
            font-size: 0.85em;
        }
        .badge-ativo { background-color: #28a745; }
        .badge-vencido { background-color: #dc3545; }
        .badge-substituido { background-color: #6c757d; }
        
        .assinatura-container {
            width: 300px;
            padding: 15px;
            border-left: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #fff;
        }
        
        .assinatura-area {
            width: 100%;
            height: 150px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            background-color: #fff;
            position: relative;
        }
        
        .assinatura-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #aaa;
            text-align: center;
            width: 100%;
        }
        
        .assinatura-imagem {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .btn-assinar {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            width: 100%;
        }
        
        .btn-limpar {
            background-color: #f44336;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
            width: 100%;
        }
        
        .btn-imprimir {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 20px;
        }
        
        .btn-voltar {
            background-color: #555;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-right: 10px;
        }
        
        /* Estilos para status de devolução */
        .produto-devolucao {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #dc3545;
        }

        .status-devolucao {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }

        .status-devolucao.novo {
            background: #d4edda;
            color: #155724;
        }

        .status-devolucao.usado_bom {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-devolucao.usado_leve {
            background: #fff3cd;
            color: #856404;
        }

        .status-devolucao.danificado_reparo {
            background: #f8d7da;
            color: #721c24;
        }

        .status-devolucao.danificado_irrecuperavel,
        .status-devolucao.vencido,
        .status-devolucao.descarte {
            background: #f5c6cb;
            color: #721c24;
        }

        /* Estilos para o modal de assinatura */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal {
            background-color: white;
            border-radius: 5px;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .modal-title {
            margin: 0;
            font-size: 20px;
        }
        
        .modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 28px;
            color: #111;
            cursor: pointer;
            line-height: 1;
            padding: 0;
            width: 32px;
            height: 32px;
            z-index: 2;
            transition: color 0.2s;
        }
        .modal-close:hover {
            color: #e11d48;
            background: none;
        }
        
        .canvas-container {
            border: 1px solid #ccc;
            margin-bottom: 20px;
            position: relative;
        }
        
        .canvas-container::after {
            content: "Assine acima desta linha";
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            color: #ddd;
            font-size: 14px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 16px;
            margin-top: 10px;
        }

        .btn-salvar-assinatura {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .produto-item {
                flex-direction: column;
            }
            .assinatura-container {
                width: 100%;
                border-left: none;
                border-top: 1px solid #ddd;
            }
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
        }
        @media print {
            body * {
                visibility: hidden;
            }
            .printable-area, .printable-area * {
                visibility: visible;
            }
            .printable-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            .btn-imprimir, .btn-voltar, .assinatura-container .btn-assinar, .assinatura-container .btn-limpar, .modal-overlay {
                display: none !important;
            }
            .produto-item {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <div class="container">
            <div class="no-print" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 18px;">
                <button onclick="window.history.back();" style="background: none; border: none; color: #111; display: flex; align-items: center; gap: 10px; font-size: 1em; cursor: pointer; padding: 0; margin: 0; font-weight: 500; width: 40px; height: 40px;">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 19L9 12L16 5" stroke="#111" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </button>
                <button onclick="imprimirFicha()" style="background: none; border: none; color: #111; display: flex; align-items: center; cursor: pointer; padding: 0; margin: 0; margin-left: auto; width: 32px; height: 32px;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#111" stroke-width="2"><rect x="6" y="3" width="12" height="5" rx="1"/><rect x="4" y="8" width="16" height="10" rx="2"/><rect x="8" y="16" width="8" height="4" rx="1"/><line x1="8" y1="12" x2="16" y2="12"/></svg>
                </button>
            </div>
            
            <h1>Ficha de Controle de EPI</h1>
            <div class="info-pessoa">
                <div class="info-row">
                    <div class="info-label">Nome:</div>
                    <div><?php echo htmlspecialchars($pessoa['nome']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Função:</div>
                    <div><?php echo htmlspecialchars($pessoa['funcao']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Setor:</div>
                    <div><?php echo htmlspecialchars($pessoa['setor']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Posto de Trabalho:</div>
                    <div><?php echo htmlspecialchars($pessoa['posto']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Data de Admissão:</div>
                    <div><?php echo date('d/m/Y', strtotime($pessoa['data_admissao'])); ?></div>
                </div>
            </div>

            <div class="produtos-container">
                <h2>Histórico de EPIs Recebidos</h2>
                <?php if (empty($produtos_epi)): ?>
                    <p>Nenhum EPI registrado para esta pessoa.</p>
                <?php else: ?>
                    <?php foreach ($produtos_epi as $epi): ?>
                        <div class="produto-item">
                            <div class="produto-info">
                                <div class="produto-nome"><?php echo htmlspecialchars($epi['nome']); ?></div>
                                <div class="produto-codigo">
                                    Código: <?php echo htmlspecialchars($epi['codigo']); ?> | 
                                    C.A: <?php echo htmlspecialchars($epi['ca'] ?? 'N/A'); ?>
                                </div>
                                <div class="produto-quantidade">
                                    Quantidade: <?php echo htmlspecialchars($epi['quantidade']); ?>
                                </div>
                                <div class="produto-data">
                                    Data de Entrega: <?php echo date('d/m/Y', strtotime($epi['data_entrega'])); ?>
                                </div>

                                <?php if (!empty($epi['status_devolucao'])): ?>
                                <div class="produto-devolucao">
                                    <strong>Status de Devolução:</strong>
                                    <span class="status-devolucao <?php echo strtolower(str_replace([' ', '–', '/'], ['_', '_', '_'], $epi['status_devolucao'])); ?>">
                                        <?php
                                        $status_map = [
                                            'novo' => 'Novo / Sem Uso',
                                            'usado_bom' => 'Usado – Bom Estado',
                                            'usado_leve' => 'Usado – Desgaste leve',
                                            'danificado_reparo' => 'Danificado – Reparo Possível',
                                            'danificado_irrecuperavel' => 'Danificado – Irrecuperável',
                                            'vencido' => 'Vencido / Fora da Validade',
                                            'descarte' => 'Descarte'
                                        ];
                                        echo $status_map[$epi['status_devolucao']] ?? $epi['status_devolucao'];
                                        ?>
                                    </span>
                                    <br>
                                    <small>Devolvido em: <?php echo date('d/m/Y', strtotime($epi['data_devolucao'])); ?>
                                    (Qtd: <?php echo $epi['quantidade_devolvida']; ?>)</small>
                                </div>
                                <?php endif; ?>
                                
                                <div class="produto-vencimento">
                                    <?php
                                    $is_recente = ($epi['data_entrega'] == ($epis_recentes[$epi['produto_id']] ?? null));
                                    if ($is_recente && !empty($epi['validade_uso']) && $epi['validade_uso'] > 0):
                                        $dataEntrega = new DateTime($epi['data_entrega']);
                                        $dataVencimento = clone $dataEntrega;
                                        $dataVencimento->add(new DateInterval('P' . $epi['validade_uso'] . 'M'));
                                        $hoje = new DateTime();
                                        $dias_restantes = (int)$hoje->diff($dataVencimento)->format('%r%a');

                                        // Mostrar alerta apenas se faltar 30 dias ou menos, ou se já estiver vencido
                                        if ($dias_restantes <= 100):
                                    ?>
                                        <?php if ($dias_restantes < 0): ?>
                                            <span class="badge-vencido">Vencido há <?php echo abs($dias_restantes); ?> dias</span>
                                        <?php else: ?>
                                            <span class="badge-ativo">Vence em <?php echo $dias_restantes; ?> dias (<?php echo $dataVencimento->format('d/m/Y'); ?>)</span>
                                        <?php endif; ?>
                                    <?php
                                        endif; // Fim da condição de dias_restantes <= 30
                                    elseif (!$is_recente):
                                    ?>
                                        <span class="badge-substituido">Substituído</span>
                                    <?php endif; ?>
                                </div>

                            </div>
                            <div class="assinatura-container no-print">
                                <div class="assinatura-area">
                                    <?php if (!empty($epi['assinatura'])): ?>
                                        <img src="<?php echo $epi['assinatura']; ?>" alt="Assinatura" class="assinatura-imagem">
                                    <?php else: ?>
                                        <div class="assinatura-placeholder">Aguardando assinatura</div>
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($epi['data_assinatura'])): ?>
                                    <div class="assinatura-data" style="font-size: 12px; color: #666; margin-top: 5px;">
                                        Assinado em: <?php echo date('d/m/Y H:i', strtotime($epi['data_assinatura'])); ?>
                                    </div>
                                <?php endif; ?>
                                <?php if (empty($epi['assinatura'])): ?>
                                    <button class="btn-assinar" onclick="abrirModalAssinatura(<?php echo $epi['id']; ?>)">Assinar Recebimento</button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal de Assinatura -->
    <div id="modalAssinatura" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">Assinar Recebimento</h3>
                <button class="modal-close" onclick="fecharModalAssinatura()">&times;</button>
            </div>
            <div class="canvas-container">
                <canvas id="signature-pad" width="558" height="200"></canvas>
            </div>
            <div class="modal-footer">
                <button id="clear-signature" class="btn-limpar">Limpar</button>
                <button id="save-signature" class="btn-salvar-assinatura">Salvar Assinatura</button>
            </div>
        </div>
    </div>

    <!-- Popup de feedback moderno -->
    <div id="feedback-popup" style="display:none;">
        <div class="feedback-popup-content">
            <span id="feedback-popup-message"></span>
        </div>
    </div>

    <style>
    #feedback-popup {
        position: fixed;
        top: 0; left: 0; width: 100vw; height: 100vh;
        background: rgba(20, 23, 31, 0.18);
        z-index: 4000;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
    }
    .feedback-popup-content {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 32px rgba(0,0,0,0.12);
        padding: 36px 38px 30px 38px;
        min-width: 260px;
        max-width: 90vw;
        text-align: center;
        font-size: 1.15em;
        color: #111827;
        font-weight: 500;
        letter-spacing: 0.2px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        animation: popup-fadein 0.2s;
    }
    @keyframes popup-fadein {
        from { opacity: 0; transform: scale(0.97); }
        to { opacity: 1; transform: scale(1); }
    }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <script>
        const produtosEpiData = <?php echo json_encode($produtos_epi); ?>;
        let currentEpiId = null;
        const modal = document.getElementById('modalAssinatura');
        const canvas = document.getElementById('signature-pad');
        const signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)'
        });

        function abrirModalAssinatura(epiId) {
            currentEpiId = epiId;
            signaturePad.clear();
            modal.style.display = 'flex';
        }

        function fecharModalAssinatura() {
            modal.style.display = 'none';
        }

        document.getElementById('clear-signature').addEventListener('click', function () {
            signaturePad.clear();
        });

        document.getElementById('save-signature').addEventListener('click', function () {
            if (signaturePad.isEmpty()) {
                alert("Por favor, forneça uma assinatura.");
            } else {
                const dataURL = signaturePad.toDataURL('image/png');
                
                fetch('salvar-assinatura-epi.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: currentEpiId,
                        assinatura: dataURL
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        mostrarFeedbackPopup('Assinatura salva com sucesso!');
                        setTimeout(() => location.reload(), 1800);
                    } else {
                        alert('Erro ao salvar a assinatura: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Ocorreu um erro de comunicação.');
                });
            }
        });

        function imprimirFicha() {
            const pessoaInfo = document.querySelector('.info-pessoa').outerHTML;

            let htmlTabela = `
                <table style="width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 12px;">
                    <thead>
                        <tr>
                            <th style="border: 1px solid #333; padding: 8px; text-align: left; background-color: #f2f2f2;">EPI</th>
                            <th style="border: 1px solid #333; padding: 8px; text-align: left; background-color: #f2f2f2;">C.A</th>
                            <th style="border: 1px solid #333; padding: 8px; text-align: left; background-color: #f2f2f2;">Data Entrega</th>
                            <th style="border: 1px solid #333; padding: 8px; text-align: left; background-color: #f2f2f2;">Qtd</th>
                            <th style="border: 1px solid #333; padding: 8px; text-align: left; background-color: #f2f2f2;">Assinatura</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            produtosEpiData.forEach(epi => {
                const dataEntrega = new Date(epi.data_entrega).toLocaleDateString('pt-BR', { timeZone: 'UTC' });
                
                let assinaturaHtml = '<span>Não assinado</span>';
                if (epi.assinatura) {
                    let dataAssinaturaTexto = '';
                    if (epi.data_assinatura) {
                        const dataAssinatura = new Date(epi.data_assinatura).toLocaleString('pt-BR', {
                            timeZone: 'America/Sao_Paulo',
                            day: '2-digit', month: '2-digit', year: 'numeric',
                            hour: '2-digit', minute: '2-digit'
                        });
                        dataAssinaturaTexto = `<br><span style="font-size: 10px; color: #333;">Assinado em: ${dataAssinatura}</span>`;
                    }
                    assinaturaHtml = `<div style="text-align: center;">
                                        <img src="${epi.assinatura}" alt="Assinatura" style="max-width: 150px; max-height: 40px; display: block; margin: 0 auto;">
                                        ${dataAssinaturaTexto}
                                      </div>`;
                }

                htmlTabela += `
                    <tr>
                        <td style="border: 1px solid #333; padding: 8px;">${epi.nome}</td>
                        <td style="border: 1px solid #333; padding: 8px;">${epi.ca || 'N/A'}</td>
                        <td style="border: 1px solid #333; padding: 8px;">${dataEntrega}</td>
                        <td style="border: 1px solid #333; padding: 8px;">${epi.quantidade}</td>
                        <td style="border: 1px solid #333; padding: 8px; vertical-align: middle;">${assinaturaHtml}</td>
                    </tr>
                `;
            });

            htmlTabela += `</tbody></table>`;

            const printWindow = window.open('', '', 'height=800,width=800');
            printWindow.document.write('<html><head><title>Imprimir Ficha de EPI</title>');
            printWindow.document.write(`
                <style>
                    body { font-family: Arial, sans-serif; }
                    .info-pessoa { background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #ddd; }
                    .info-row { display: flex; margin-bottom: 8px; font-size: 14px; }
                    .info-label { font-weight: bold; width: 150px; }
                    @media print {
                        body { margin: 1cm; }
                        table { page-break-inside: auto; }
                        tr { page-break-inside: avoid; page-break-after: auto; }
                    }
                </style>
            `);
            printWindow.document.write('</head><body>');
            printWindow.document.write('<h1>Ficha de Controle de EPI</h1>');
            printWindow.document.write(pessoaInfo);
            printWindow.document.write('<h2>Histórico de EPIs Recebidos</h2>');
            printWindow.document.write(htmlTabela);
            printWindow.document.write('</body></html>');

            setTimeout(() => {
                printWindow.document.close();
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }, 500);
        }

        function mostrarFeedbackPopup(mensagem, tempo=1800) {
            const popup = document.getElementById('feedback-popup');
            const msg = document.getElementById('feedback-popup-message');
            msg.textContent = mensagem;
            popup.style.display = 'flex';
            setTimeout(() => {
                popup.style.display = 'none';
            }, tempo);
        }

    </script>
</body>
</html>





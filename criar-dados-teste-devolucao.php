<?php
require_once 'conexao.php';

echo "<h2>Criar Dad<PERSON> de Teste para Devolução</h2>";

try {
    // 1. Verificar se existem pessoas
    $result_pessoas = $conn->query("SELECT COUNT(*) as total FROM pessoas WHERE status = 'ativo'");
    $total_pessoas = $result_pessoas->fetch_assoc()['total'];
    
    if ($total_pessoas == 0) {
        echo "<h3>Criando pessoas de teste...</h3>";
        $pessoas_teste = [
            ['<PERSON> Silva', 'Operador', 'Produção', 'Operador de Máquina'],
            ['<PERSON>', 'Técnica', 'Qualidade', 'Técnica de Qualidade'],
            ['<PERSON>', 'Auxiliar', 'Manutenção', 'Auxiliar de Manutenção']
        ];
        
        foreach ($pessoas_teste as $pessoa) {
            $sql = "INSERT INTO pessoas (nome, posto, setor, funcao, status) VALUES (?, ?, ?, ?, 'ativo')";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssss", $pessoa[0], $pessoa[1], $pessoa[2], $pessoa[3]);
            if ($stmt->execute()) {
                echo "<p>✅ Pessoa criada: {$pessoa[0]}</p>";
            }
        }
    } else {
        echo "<p>✅ Já existem $total_pessoas pessoas ativas</p>";
    }
    
    // 2. Verificar se existem produtos
    $result_produtos = $conn->query("SELECT COUNT(*) as total FROM produtos");
    $total_produtos = $result_produtos->fetch_assoc()['total'];
    
    if ($total_produtos == 0) {
        echo "<h3>Criando produtos de teste...</h3>";
        $produtos_teste = [
            ['001', 'Capacete de Segurança', 'EPI', 'Proteção Individual', '12345', 25.00, 30.00, 50],
            ['002', 'Luva de Segurança', 'EPI', 'Proteção Individual', '12346', 15.00, 18.00, 100],
            ['003', 'Óculos de Proteção', 'EPI', 'Proteção Individual', '12347', 20.00, 24.00, 75],
            ['004', 'Bota de Segurança', 'EPI', 'Proteção Individual', '12348', 80.00, 96.00, 30]
        ];
        
        foreach ($produtos_teste as $produto) {
            $sql = "INSERT INTO produtos (codigo, nome, categoria, unidade_medida, ca, valor, valor_bruto, quantidade) VALUES (?, ?, ?, 'UN', ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssddi", $produto[0], $produto[1], $produto[2], $produto[4], $produto[5], $produto[6], $produto[7]);
            if ($stmt->execute()) {
                echo "<p>✅ Produto criado: {$produto[1]} (Código: {$produto[0]})</p>";
            }
        }
    } else {
        echo "<p>✅ Já existem $total_produtos produtos</p>";
    }
    
    // 3. Verificar se existem EPIs vinculados
    $result_epis = $conn->query("SELECT COUNT(*) as total FROM pessoa_epi");
    $total_epis = $result_epis->fetch_assoc()['total'];
    
    if ($total_epis == 0) {
        echo "<h3>Criando vínculos pessoa-EPI de teste...</h3>";
        
        // Buscar pessoas e produtos criados
        $pessoas = $conn->query("SELECT id, nome FROM pessoas WHERE status = 'ativo' LIMIT 3");
        $produtos = $conn->query("SELECT codigo, nome FROM produtos LIMIT 4");
        
        $lista_pessoas = [];
        while ($row = $pessoas->fetch_assoc()) {
            $lista_pessoas[] = $row;
        }
        
        $lista_produtos = [];
        while ($row = $produtos->fetch_assoc()) {
            $lista_produtos[] = $row;
        }
        
        // Criar vínculos
        foreach ($lista_pessoas as $pessoa) {
            foreach ($lista_produtos as $index => $produto) {
                $quantidade = rand(1, 5); // Quantidade aleatória entre 1 e 5
                
                $sql = "INSERT INTO pessoa_epi (pessoa_id, produto_id, quantidade, data_entrega) VALUES (?, ?, ?, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("isi", $pessoa['id'], $produto['codigo'], $quantidade);
                
                if ($stmt->execute()) {
                    echo "<p>✅ EPI vinculado: {$pessoa['nome']} → {$produto['nome']} (Qtd: $quantidade)</p>";
                }
                
                // Não vincular todos os produtos a todas as pessoas
                if ($index >= 2) break;
            }
        }
    } else {
        echo "<p>✅ Já existem $total_epis vínculos pessoa-EPI</p>";
    }
    
    // 4. Verificar se os campos de devolução existem
    echo "<h3>Verificando campos de devolução...</h3>";
    $result = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'quantidade_devolvida'");
    if ($result->num_rows == 0) {
        echo "<p>Adicionando campos de devolução...</p>";
        $conn->query("ALTER TABLE pessoa_epi ADD COLUMN status_devolucao VARCHAR(50) DEFAULT NULL");
        $conn->query("ALTER TABLE pessoa_epi ADD COLUMN data_devolucao DATETIME DEFAULT NULL");
        $conn->query("ALTER TABLE pessoa_epi ADD COLUMN quantidade_devolvida INT DEFAULT 0");
        echo "<p>✅ Campos de devolução adicionados</p>";
    } else {
        echo "<p>✅ Campos de devolução já existem</p>";
    }
    
    echo "<h3>✅ Dados de teste criados com sucesso!</h3>";
    
    // Mostrar resumo
    echo "<h4>Resumo dos dados:</h4>";
    $result_pessoas = $conn->query("SELECT COUNT(*) as total FROM pessoas WHERE status = 'ativo'");
    $total_pessoas = $result_pessoas->fetch_assoc()['total'];
    
    $result_produtos = $conn->query("SELECT COUNT(*) as total FROM produtos");
    $total_produtos = $result_produtos->fetch_assoc()['total'];
    
    $result_epis = $conn->query("SELECT COUNT(*) as total FROM pessoa_epi");
    $total_epis = $result_epis->fetch_assoc()['total'];
    
    echo "<ul>";
    echo "<li>Pessoas ativas: $total_pessoas</li>";
    echo "<li>Produtos: $total_produtos</li>";
    echo "<li>Vínculos pessoa-EPI: $total_epis</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<h3>🧪 Próximos passos:</h3>";
echo "<ol>";
echo "<li><a href='debug-produtos-funcionario.php' target='_blank'>Verificar se os dados estão corretos</a></li>";
echo "<li><a href='devolucao.php' target='_blank'>Testar a página de devolução</a></li>";
echo "<li>Na aba 'Devolução por Funcionário', selecionar uma pessoa</li>";
echo "<li>Verificar se os produtos aparecem na tabela</li>";
echo "</ol>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Ir para Devolução</a>";
echo "</p>";

$conn->close();
?>

<?php
// Desabilitar exibição de erros para não quebrar o JSON
error_reporting(0);
ini_set('display_errors', 0);

// Definir header JSON antes de qualquer saída
header('Content-Type: application/json');

session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
}

require_once 'conexao.php';

$historico = [];

// 1. Buscar devoluções por funcionário
$result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'");
if ($result && $result->fetch_assoc()['count'] > 0) {
    // Verificar se tabela usuarios existe
    $usuarios_existe = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'");
    $tem_usuarios = $usuarios_existe && $usuarios_existe->fetch_assoc()['count'] > 0;

    if ($tem_usuarios) {
        $sql = "
            SELECT
                d.id,
                d.pessoa_id,
                d.pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                COALESCE(u.nome, CONCAT('Usuário ID: ', d.usuario_id)) as usuario_nome,
                'funcionario' as tipo
            FROM devolucoes_epi d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            ORDER BY d.data_devolucao DESC
        ";
    } else {
        $sql = "
            SELECT
                id,
                pessoa_id,
                pessoa_nome,
                produto_id,
                produto_nome,
                quantidade,
                estado,
                data_devolucao,
                usuario_id,
                CONCAT('Usuário ID: ', usuario_id) as usuario_nome,
                'funcionario' as tipo
            FROM devolucoes_epi
            ORDER BY data_devolucao DESC
        ";
    }

    $result = $conn->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $row['assinatura'] = '';
            $historico[] = $row;
        }
    }
}
    
// 2. Buscar devoluções rápidas
$result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'");
if ($result && $result->fetch_assoc()['count'] > 0) {
    if ($tem_usuarios) {
        $sql = "
            SELECT
                d.id,
                NULL as pessoa_id,
                NULL as pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                COALESCE(u.nome, CONCAT('Usuário ID: ', d.usuario_id)) as usuario_nome,
                'rapida' as tipo
            FROM devolucoes_rapidas d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            ORDER BY d.data_devolucao DESC
        ";
    } else {
        $sql = "
            SELECT
                id,
                NULL as pessoa_id,
                NULL as pessoa_nome,
                produto_id,
                produto_nome,
                quantidade,
                estado,
                data_devolucao,
                usuario_id,
                CONCAT('Usuário ID: ', usuario_id) as usuario_nome,
                'rapida' as tipo
            FROM devolucoes_rapidas
            ORDER BY data_devolucao DESC
        ";
    }

    $result = $conn->query($sql);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $row['assinatura'] = '';
            $historico[] = $row;
        }
    }
}

// 3. Ordenar por data
if (count($historico) > 0) {
    usort($historico, function($a, $b) {
        $timeA = strtotime($a['data_devolucao']);
        $timeB = strtotime($b['data_devolucao']);
        if ($timeA === false) $timeA = 0;
        if ($timeB === false) $timeB = 0;
        return $timeB - $timeA;
    });
}

// Garantir que o JSON seja válido
$json = json_encode($historico);
if ($json === false) {
    echo json_encode([]);
} else {
    echo $json;
}

if (isset($conn)) {
    $conn->close();
}
?>

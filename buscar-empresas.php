<?php
include 'conexao.php';
$termo = $_GET['termo'] ?? '';
$sql = "SELECT codigo_empresa as codigo, nome_empresa as nome FROM empresas WHERE nome_empresa LIKE ? AND status = 'ativo' ORDER BY nome_empresa";
$stmt = $conn->prepare($sql);
$like = "%$termo%";
$stmt->bind_param("s", $like);
$stmt->execute();
$result = $stmt->get_result();
$empresas = [];
while ($row = $result->fetch_assoc()) {
    $empresas[] = [
        'codigo' => ltrim($row['codigo'], '0'), // Remove zeros à esquerda
        'nome' => $row['nome']
    ];
}
echo json_encode($empresas); 
<?php
include 'conexao.php';
header('Content-Type: application/json');
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id <= 0) {
    echo json_encode(['success' => false, 'error' => 'ID inválido']);
    exit;
}
$res = $conn->query("SELECT nome FROM setor WHERE id = $id LIMIT 1");
if ($res && $row = $res->fetch_assoc()) {
    echo json_encode(['success' => true, 'nome' => $row['nome']]);
} else {
    echo json_encode(['success' => false, 'error' => 'Setor não encontrado']);
} 
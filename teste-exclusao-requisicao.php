<?php
include 'conexao.php';

echo "<h2>Teste de Exclusão de Requisição Urgente</h2>";

// Primeiro, criar uma requisição urgente de teste
echo "<h3>1. <PERSON><PERSON>do requisição urgente de teste...</h3>";

$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Usuario', '001', 'TESTE-001', 'Teste de exclusão', '1', 'Teste', 'TI', 'Requisição de teste para exclusão', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✓ Requisição urgente criada com código: $codigo_requisicao</p>";
    
    // Adicionar alguns itens de teste
    echo "<h3>2. Adicionando itens de teste...</h3>";
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'PROD001', 5)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✓ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    // Verificar se a requisição foi criada corretamente
    echo "<h3>3. Verificando requisição criada...</h3>";
    $result = $conn->query("SELECT * FROM requisicoes WHERE codigo_solicitacao = $codigo_requisicao");
    if ($result && $result->num_rows > 0) {
        $req = $result->fetch_assoc();
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Campo</th><th>Valor</th></tr>";
        foreach ($req as $campo => $valor) {
            echo "<tr><td>$campo</td><td>$valor</td></tr>";
        }
        echo "</table>";
    }
    
    // Verificar itens
    echo "<h3>4. Verificando itens da requisição...</h3>";
    $result = $conn->query("SELECT * FROM itens_solicitacao WHERE codigo_solicitacao = $codigo_requisicao");
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Código Solicitação</th><th>Produto</th><th>Quantidade</th></tr>";
        while ($item = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $item['id'] . "</td>";
            echo "<td>" . $item['codigo_solicitacao'] . "</td>";
            echo "<td>" . $item['produto'] . "</td>";
            echo "<td>" . $item['quantidade'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Agora testar a exclusão
    echo "<h3>5. Testando exclusão...</h3>";
    echo "<p><strong>Simulando chamada para excluir-pedido-urgente.php</strong></p>";
    
    // Simular o processo de exclusão
    $tipo = 'requisicao';
    $codigo = $codigo_requisicao;
    
    // Verificar se é realmente um pedido urgente
    $campo_urgente = 'requisicao_urgente';
    $stmt = $conn->prepare("SELECT $campo_urgente FROM requisicoes WHERE codigo_solicitacao = ?");
    $stmt->bind_param("i", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "<p style='color: red;'>✗ Solicitação não encontrada</p>";
    } else {
        $solicitacao = $result->fetch_assoc();
        
        if ($solicitacao[$campo_urgente] != 1) {
            echo "<p style='color: red;'>✗ Apenas pedidos urgentes podem ser excluídos</p>";
        } else {
            echo "<p style='color: green;'>✓ Requisição é urgente, pode ser excluída</p>";
            
            // Primeiro, excluir itens relacionados
            echo "<p>Excluindo itens relacionados...</p>";
            $stmt_itens = $conn->prepare("DELETE FROM itens_solicitacao WHERE codigo_solicitacao = ?");
            $stmt_itens->bind_param("i", $codigo);
            
            if ($stmt_itens->execute()) {
                echo "<p style='color: green;'>✓ Itens excluídos com sucesso</p>";
                
                // Agora excluir a requisição principal
                echo "<p>Excluindo requisição principal...</p>";
                $stmt_delete = $conn->prepare("DELETE FROM requisicoes WHERE codigo_solicitacao = ?");
                $stmt_delete->bind_param("i", $codigo);
                
                if ($stmt_delete->execute()) {
                    echo "<p style='color: green;'>✓ Requisição excluída com sucesso!</p>";
                    echo "<p><strong>Teste de exclusão PASSOU!</strong></p>";
                } else {
                    echo "<p style='color: red;'>✗ Erro ao excluir requisição: " . $stmt_delete->error . "</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ Erro ao excluir itens: " . $stmt_itens->error . "</p>";
            }
        }
    }
    
} else {
    echo "<p style='color: red;'>✗ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<h3>Teste concluído!</h3>";
echo "<p><a href='todas-solitacoes-estoque.php'>Voltar para Todas as Solicitações</a></p>";

$conn->close();
?>

<?php
include 'conexao.php';

// Verificar se o ID e o tipo foram fornecidos
if (!isset($_POST['delete_id']) || empty($_POST['delete_id']) || !isset($_POST['delete_tipo']) || empty($_POST['delete_tipo'])) {
    header("Location: requisicoes-feitas.php?status=delete-error&message=" . urlencode("ID ou tipo da solicitação não fornecido"));
    exit;
}

$id = $_POST['delete_id'];
$tipo = $_POST['delete_tipo'];

// Iniciar transação
$conn->begin_transaction();

try {
    switch ($tipo) {
        case 'requisicao':
            // Buscar itens da requisição
            $stmt = $conn->prepare("SELECT produto, quantidade FROM itens_solicitacao WHERE codigo_solicitacao = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $itens = $result->fetch_all(MYSQLI_ASSOC);

            // Devolver estoque
            foreach ($itens as $item) {
                $stmtEstoque = $conn->prepare("UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?");
                $stmtEstoque->bind_param("is", $item['quantidade'], $item['produto']);
                $stmtEstoque->execute();
            }

            // Remover lançamentos de EPI feitos por esta requisição
            // Obter o id do funcionário da requisição
            $stmt = $conn->prepare("SELECT funcionario FROM requisicoes WHERE codigo_solicitacao = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();
            if ($row && !empty($row['funcionario'])) {
                $pessoa_id = $row['funcionario'];
                if (!empty($pessoa_id) && is_numeric($pessoa_id)) {
                    // Verificar se algum dos lançamentos já foi assinado
                    $assinados = [];
                    foreach ($itens as $item) {
                        $stmtCheck = $conn->prepare("SELECT id FROM pessoa_epi WHERE pessoa_id = ? AND produto_id = ? AND assinatura IS NOT NULL AND assinatura != '' ORDER BY data_entrega DESC LIMIT 1");
                        $stmtCheck->bind_param("is", $pessoa_id, $item['produto']);
                        $stmtCheck->execute();
                        $resultCheck = $stmtCheck->get_result();
                        if ($resultCheck->num_rows > 0) {
                            $assinados[] = $item['produto'];
                        }
                    }
                    if (count($assinados) > 0) {
                        throw new Exception("Não é possível cancelar: um ou mais itens já foram confirmados por assinatura na ficha de EPI.");
                    }
                    // Remover todos os lançamentos de EPI para os produtos desta requisição feitos para este funcionário
                    foreach ($itens as $item) {
                        // Remover o registro mais recente para cada produto
                        $stmtRemoverEPI = $conn->prepare("DELETE FROM pessoa_epi WHERE id = (SELECT id FROM (SELECT id FROM pessoa_epi WHERE pessoa_id = ? AND produto_id = ? ORDER BY data_entrega DESC LIMIT 1) as sub)");
                        $stmtRemoverEPI->bind_param("is", $pessoa_id, $item['produto']);
                        $stmtRemoverEPI->execute();
                    }
                }
            }

            // Excluir os itens da requisição
            $stmt = $conn->prepare("DELETE FROM itens_solicitacao WHERE codigo_solicitacao = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            // Excluir a requisição
            $stmt = $conn->prepare("DELETE FROM requisicoes WHERE codigo_solicitacao = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            break;
        case 'pedido_mensal':
            // Buscar itens do pedido mensal
            $stmt = $conn->prepare("SELECT produto, quantidade FROM itens_pedido_mensal WHERE codigo_pedido = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            $result = $stmt->get_result();
            $itens = $result->fetch_all(MYSQLI_ASSOC);
            // Devolver estoque
            foreach ($itens as $item) {
                $stmtEstoque = $conn->prepare("UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?");
                $stmtEstoque->bind_param("is", $item['quantidade'], $item['produto']);
                $stmtEstoque->execute();
            }
            // Excluir os itens do pedido mensal
            $stmt = $conn->prepare("DELETE FROM itens_pedido_mensal WHERE codigo_pedido = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            // Excluir o pedido mensal
            $stmt = $conn->prepare("DELETE FROM pedidos_mensais WHERE codigo_pedido = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            break;
        case 'pedido_especial':
            // Excluir o pedido especial
            $stmt = $conn->prepare("DELETE FROM pedidos_especiais WHERE codigo_pedido = ?");
            $stmt->bind_param("i", $id);
            $stmt->execute();
            break;
        default:
            throw new Exception("Tipo de solicitação inválido");
    }
    if ($stmt->affected_rows === 0) {
        throw new Exception("Erro ao excluir a solicitação");
    }
    $conn->commit();
    // Retornar JSON para AJAX
    echo json_encode(['success' => true]);
    exit;
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    exit;
}
?>
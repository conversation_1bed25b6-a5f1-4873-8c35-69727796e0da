<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
include 'conexao.php';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos Pedido Mensal</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        h1 {
            font-size: 1.7rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 70px;
            text-align: center;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px 0 0 0;
        }
        label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 6px;
        }
        select, input[type="text"], input[type="number"] {
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            transition: border 0.2s;
            margin-bottom: 0;
        }
        select:focus, input[type="text"]:focus, input[type="number"]:focus {
            border-color: #2563eb;
            outline: none;
        }
        .btn {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 10px 18px;
            font-size: 15px;
            font-weight: 600;
            margin-top: 8px;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: none;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .close-btn {
            background: #f44336;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 10px 18px;
            font-size: 15px;
            font-weight: 600;
            margin-left: 10px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .close-btn:hover {
            background: #dc2626;
        }
        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 14px;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            border: 1px solid #e5e7eb;
        }
        .table th, .table td {
            border-bottom: 1px solid #e5e7eb;
            padding: 14px 16px;
            text-align: left;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            border: none;
        }
        .table tr:last-child td {
            border-bottom: none;
        }
        .table tr:hover {
            background: #f9fafb;
        }
        .table td.codigo {
            color: #2563eb;
            font-weight: 700;
        }
        .table td.status {
            padding: 0 0 0 16px;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            vertical-align: middle;
            background: #d1fae5;
            color: #047857;
            border: none;
            box-shadow: none;
            text-transform: uppercase;
            transition: background 0.2s, color 0.2s;
        }
        .status-inativo {
            background: #fee2e2 !important;
            color: #b91c1c !important;
        }
        .status-ativo {
            background: #d1fae5 !important;
            color: #047857 !important;
        }
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active, .popup-overlay[style*="display: flex"] {
            display: flex !important;
        }
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        .popup h2, .popup h3 {
            margin: 0 0 18px 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        .popup input, .popup select {
            width: 100%;
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            margin-bottom: 10px;
        }
        .produtos-lista {
            margin-top: 10px;
            width: 100%;
        }
        .produtos-lista th, .produtos-lista td {
            padding: 8px 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        .produtos-lista th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
        }
        .produtos-lista tr:last-child td {
            border-bottom: none;
        }
        .produtos-lista tr:hover {
            background: #f9fafb;
        }
        .produtos-lista button {
            background: none !important;
            color: #111 !important;
            border: none;
            border-radius: 6px;
            padding: 6px 14px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .produtos-lista button:hover {
            background: #f3f4f6;
        }
        .btn-inativar {
            background: #ff9800;
            color: #fff;
        }
        .btn-inativar:hover {
            background: #e65100;
        }
        .btn-excluir {
            background: #f44336;
            color: #fff;
        }
        .btn-excluir:hover {
            background: #b71c1c;
        }
        .popup-table button, .popup-table .btn-select {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 4px 12px;
            font-size: 13px;
            height: 28px;
            min-width: 60px;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: none;
            font-weight: 500;
        }
        .popup-table button:hover, .popup-table .btn-select:hover {
            background: #1d4ed8;
        }
        .popup-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 6px;
            font-size: 14px;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
        }
        .popup-table th, .popup-table td {
            padding: 12px 14px;
            text-align: left;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #fff;
        }
        .popup-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            border: none;
        }
        .popup-table tbody tr {
            border-radius: 8px;
            box-shadow: none;
        }
        .popup-table tbody tr:not(:last-child) td {
            border-bottom: 1px solid #e5e7eb;
        }
        .btn-add-produto {
            background: #22c55e !important;
            color: #fff !important;
            transition: background 0.2s;
        }
        .btn-add-produto:hover {
            background: #22c55e !important;
            color: #fff !important;
        }
        .produtos-lista button[title='Remover'] {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
            padding: 0 !important;
            margin: 0 !important;
            min-width: 32px !important;
            min-height: 32px !important;
            width: 32px !important;
            height: 32px !important;
            border-radius: 4px !important;
            transition: background 0.18s;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .produtos-lista button[title='Remover']:hover {
            background: #e5e7ebcc !important;
            box-shadow: none !important;
            filter: none;
        }
        .aba-grupo {
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            color: #374151;
            font-size: 16px;
            font-weight: 500;
            padding: 8px 0 10px 0;
            cursor: pointer;
            transition: border 0.2s, color 0.2s;
        }
        .aba-ativa {
            border-bottom: 2.5px solid #22c55e;
            color: #22c55e;
        }
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        .popup-tab.active {
            background: #fff;
            color: #2563eb;
            border-color: #2563eb;
        }
        .popup-tab:hover {
            background: #f3f4f6;
        }
        .filtros-bar {
            display: flex;
            align-items: center;
            gap: 18px;
            margin-bottom: 15px;
            margin-top: 18px;
        }
        #filtroNome {
            flex: 2;
            min-width: 400px;
            max-width: 800px;
            padding: 10px 14px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            font-size: 15px;
            background: #fff;
            color: #222;
        }
        #filtroStatus {
            flex: 1;
            min-width: 90px;
            max-width: 120px;
            padding: 10px 8px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            font-size: 15px;
            background: #fff;
            color: #222;
        }

        /* Popup de aviso para produto duplicado */
        .popup-aviso-produto {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .popup-aviso-content {
            background: #dc2626;
            color: white;
            padding: 20px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            animation: popup-bounce 0.3s ease-out;
        }

        @keyframes popup-bounce {
            0% {
                transform: scale(0.7);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <div class="container">
            <h1>Produtos Pedido Mensal</h1>
            <div class="filtros-bar">
                <input type="text" id="filtroNome" placeholder="Pesquisar por nome..." oninput="filtrarPorNome()">
                <select id="filtroStatus" onchange="carregarGrupos()">
                    <option value="ativo">Ativos</option>
                    <option value="inativo">Inativos</option>
                    <option value="todos">Todos</option>
                </select>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome</th>
                        <th>Produtos</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="grupos-tbody">
                    <!-- Preenchido via JS -->
                </tbody>
            </table>
            <button class="btn" onclick="abrirPopupGrupo()" style="margin-top:24px;">Adicionar Grupo</button>
        </div>
        <div class="popup-overlay" id="popupGrupoOverlay">
            <div class="popup" style="max-width:750px; position:relative; background:#f8fafc;">
                <h2 style="margin-bottom: 22px;">Novo Grupo de Produtos</h2>
                <button class="popup-close" onclick="fecharPopupGrupo()" style="position:absolute; right:18px; top:18px; background:none; border:none; font-size:24px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; transition:background 0.2s, color 0.2s;">&times;</button>
                <form id="formGrupo" style="width:100%;">
                    <div style="display:flex; gap:18px; margin-bottom:18px;">
                        <div style="flex:0.6; display:flex; flex-direction:column; margin-right:16px;">
                    <label>Código do Grupo:</label>
                            <input type="text" name="codigo" id="novo-codigo" required readonly style="width:100%;">
                        </div>
                        <div style="flex:1.2; display:flex; flex-direction:column;">
                    <label>Nome do Grupo:</label>
                            <input type="text" name="nome" required style="width:90%;">
                        </div>
                    </div>
                    <div style="width:100%; height:1px; background:#e5e7eb; margin:18px 0 18px 0;"></div>
                    <h3 style="margin-bottom:40px;">Produtos do Grupo</h3>
                    <div id="produtos-grupo-lista"></div>
                    <div id="nenhum-produto-msg" style="display:none; text-align:center; color:#6b7280; font-size:14px; margin-bottom:8px;">Nenhum produto inserido</div>
                    <button type="button" class="btn btn-add-produto" onclick="abrirPopupProdutosNovoGrupo()" style="background:#22c55e; color:#fff; margin-top:45px; margin-bottom:10px;">Adicionar Produto</button>
                    <div style="margin-top:20px; text-align:right; display:flex; gap:10px; justify-content:flex-end;">
                        <button type="button" class="btn btn-inativar" style="display:none;">Inativar</button>
                        <button type="button" class="btn btn-excluir" style="display:none;">Excluir</button>
                        <button type="submit" class="btn">Salvar Grupo</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="popup-overlay" id="popupEditarGrupoOverlay" style="display:none;">
            <div class="popup" style="max-width:600px; max-height:90vh; overflow-y:auto; position:relative;">
                <div style="display:flex; justify-content:flex-end; align-items:center; height:32px; margin-top:0;">
                    <button class="popup-close" onclick="tentarFecharPopupEditarGrupo()" style="background:none; border:none; font-size:24px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; transition:background 0.2s, color 0.2s;">&times;</button>
                </div>
                <div class="popup-header" style="display:flex; align-items:center; justify-content:space-between; border-bottom:1px solid #e5e7eb; padding-bottom:10px; margin-bottom:18px;">
                    <h2 id="titulo-editar-grupo" style="margin:0; font-size:22px; font-weight:600; color:#222; flex:1 1 auto;">Detalhes Grupo (<span id="titulo-nome-grupo"></span>)</h2>
                    <button id="btn-editar-grupo" type="button" title="Editar" style="background:none; border:none; padding:2px; border-radius:4px; cursor:pointer; display:flex; align-items:center; justify-content:center; margin-left:16px; width:70px; height:40px;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#374151" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 1 1 3 3L7 19.5 3 21l1.5-4L16.5 3.5z"/></svg>
                    </button>
                </div>
                <div class="popup-tabs">
                    <div class="popup-tab active" id="tab-detalhes" onclick="mostrarAbaGrupo('detalhes')">Detalhes</div>
                    <div class="popup-tab" id="tab-acoes" onclick="mostrarAbaGrupo('acoes')">Ações</div>
                </div>
                <div id="aba-detalhes-grupo">
                <form id="formEditarGrupo">
                    <input type="hidden" name="id" id="edit-id">
                        <div style="display:flex; gap:18px; margin-bottom:18px;">
                            <div style="flex:0.6; display:flex; flex-direction:column; margin-right:16px;">
                    <label>Código do Grupo:</label>
                                <input type="text" name="codigo" id="edit-codigo" readonly style="width:100%;">
                            </div>
                            <div style="flex:1.2; display:flex; flex-direction:column;">
                    <label>Nome do Grupo:</label>
                                <input type="text" name="nome" id="edit-nome" required style="width:90%;" readonly>
                            </div>
                        </div>
                        <div style="width:100%; height:1px; background:#e5e7eb; margin:18px 0 18px 0;"></div>
                    <h3>Produtos do Grupo</h3>
                    <div id="edit-produtos-grupo-lista"></div>
                        <button type="button" class="btn" id="btn-add-produto-editar" onclick="abrirPopupProdutosEditar()" style="display:none; background:#22c55e; color:#fff; margin-top:24px; margin-bottom:10px;">Adicionar Produto</button>
                    <div style="margin-top:20px; text-align:right; display:flex; gap:10px; justify-content:flex-end;">
                            <button type="submit" class="btn" id="btn-salvar-editar" style="display:none;">Salvar Alterações</button>
                        </div>
                    </form>
                </div>
                <div id="aba-acoes-grupo" style="display:none;">
                    <div style="background:#f8fafc; border-radius:10px; padding:24px 18px 24px 18px; margin-bottom:10px;">
                        <h4 style="margin:0 0 10px 0; font-size:18px; font-weight:700; color:#111827;">Ações do Grupo</h4>
                        <p style="margin:0 0 22px 0; color:#222; font-size:15px;">Selecione uma ação para executar no grupo <strong id="grupo-nome-acoes" style="font-weight:700; color:#111827;"></strong>:</p>
                        <button type="button" class="btn-acoes-grupo btn-inativar-grupo" onclick="inativarGrupo()" style="width:100%; background:#ffb020; color:#fff; font-weight:600; font-size:16px; display:flex; align-items:center; justify-content:center; gap:8px; margin-bottom:14px; border-radius:7px; border:none; padding:12px 0;">
                            <svg width="20" height="20" fill="#fff" viewBox="0 0 24 24" style="margin-right:6px;"><rect x="6" y="11" width="12" height="2" rx="1"/></svg> Inativar Grupo
                        </button>
                        <button type="button" class="btn-acoes-grupo btn-excluir-grupo" onclick="excluirGrupo()" style="width:100%; background:#e53935; color:#fff; font-weight:600; font-size:16px; display:flex; align-items:center; justify-content:center; gap:8px; border-radius:7px; border:none; padding:12px 0;">
                            <svg width="20" height="20" fill="#fff" viewBox="0 0 24 24" style="margin-right:6px;"><path d="M6 7V19C6 20.1046 6.89543 21 8 21H16C17.1046 21 18 20.1046 18 19V7M9 10V17M15 10V17M4 7H20M10 4H14C14.5523 4 15 4.44772 15 5V6H9V5C9 4.44772 9.44772 4 10 4Z"/></svg> Excluir Grupo
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="popup-overlay" id="popupProdutosOverlay" style="display:none;">
            <div class="popup" style="max-width:600px;">
                <div class="popup-header" style="display:flex; justify-content:space-between; align-items:center; margin-bottom:24px; padding-bottom:16px; border-bottom:1px solid #e5e7eb;">
                    <h3 style="margin:0; color:#111827; font-size:20px; font-weight:600;">Selecionar Produto</h3>
                    <button class="popup-close" onclick="fecharPopupProdutosEditar()" style="position:static; background:none; border:none; font-size:24px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; transition:background 0.2s, color 0.2s;">&times;</button>
                </div>
                <div class="popup-content" style="padding:24px; display:flex; flex-direction:column; align-items:flex-start;">
                    <input type="text" class="popup-search" id="filtro-produtos" placeholder="Pesquisar produto..." oninput="filtrarProdutos()" style="width:100%; max-width:320px; margin:0 auto 18px auto; padding:10px 16px; border:1px solid #d1d5db; border-radius:8px; font-size:15px; background:#f8fafc; color:#222; outline:none; transition:border 0.2s; display:block;">
                    <div class="popup-table-container" style="width:100%; overflow-x:auto;">
                        <table class="popup-table" style="width:100%; border-collapse:collapse; font-size:14px;">
                            <thead>
                                <tr>
                                    <th style="background:#f8fafc; color:#374151; font-weight:600; padding:10px 12px; border-bottom:1px solid #f3f4f6; text-align:left;">Código</th>
                                    <th style="background:#f8fafc; color:#374151; font-weight:600; padding:10px 12px; border-bottom:1px solid #f3f4f6; text-align:left;">Nome</th>
                                    <th style="background:#f8fafc; color:#374151; font-weight:600; padding:10px 12px; border-bottom:1px solid #f3f4f6; text-align:left;">Ação</th>
                                </tr>
                            </thead>
                            <tbody id="produtosPopupLista">
                                <!-- Preenchido via JS -->
                            </tbody>
                    </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="popup-overlay" id="popupConfirmarSaidaEdicao" style="display:none; z-index:9999; background:rgba(0,0,0,0.35);">
            <div class="popup" style="max-width:340px; margin:120px auto; text-align:center;">
                <h3 style="margin-bottom:18px;">Descartar alterações?</h3>
                <p style="margin-bottom:24px; color:#444;">Você tem alterações não salvas. Deseja realmente sair?</p>
                <div style="display:flex; gap:16px; justify-content:center;">
                    <button class="btn" onclick="confirmarSaidaEdicaoGrupo(true)" style="background:#f44336; color:#fff;">Descartar</button>
                    <button class="btn" onclick="confirmarSaidaEdicaoGrupo(false)" style="background:#374151; color:#fff;">Cancelar</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        let produtosGrupo = [];
        let editProdutosGrupo = [];
        let modoNovoGrupo = true;
        let modoEdicaoGrupo = false;
        let alteracoesPendentesGrupo = false;
        function abrirPopupGrupo() {
            document.getElementById('popupGrupoOverlay').style.display = 'flex';
            document.getElementById('formGrupo').reset();
            produtosGrupo = [];
            atualizarProdutosGrupo();
            buscarProximoCodigoGrupo(function(codigo){
                document.getElementById('novo-codigo').value = codigo;
            });
        }
        function fecharPopupGrupo() {
            document.getElementById('popupGrupoOverlay').style.display = 'none';
        }
        function adicionarProdutoGrupoDireto(codigo, nome) {
            // Verificar se o produto já foi selecionado
            if (produtoJaSelecionadoGrupo(codigo, produtosGrupo)) {
                mostrarPopupAviso("Produto já inserido");
                return;
            }

            produtosGrupo.push({codigo, nome, quantidade:1});
            atualizarProdutosGrupo();
            fecharPopupProdutosEditar();
        }
        function atualizarProdutosGrupo() {
            const div = document.getElementById('produtos-grupo-lista');
            const msg = document.getElementById('nenhum-produto-msg');
            if (produtosGrupo.length === 0) {
                div.innerHTML = '';
                if (msg) msg.style.display = 'block';
            } else {
                if (msg) msg.style.display = 'none';
            div.innerHTML = '<table class="produtos-lista"><tr><th>Produto</th><th>Quantidade</th><th></th></tr>' +
                    produtosGrupo.map((p, i) => `<tr><td>${p.nome}</td><td><input type='number' min='1' value='${p.quantidade}' style='width:60px;' onchange='salvarQuantidadeGrupo(${i}, this.value)'></td><td><button type='button' onclick='removerProdutoGrupo(${i})' title='Remover'><svg width="18" height="18" viewBox="0 0 24 24" fill="black" xmlns="http://www.w3.org/2000/svg"><path d="M6 7V19C6 20.1046 6.89543 21 8 21H16C17.1046 21 18 20.1046 18 19V7M9 10V17M15 10V17M4 7H20M10 4H14C14.5523 4 15 4.44772 15 5V6H9V5C9 4.44772 9.44772 4 10 4Z"/></svg></button></td></tr>`).join('') + '</table>';
            }
        }
        function removerProdutoGrupo(idx) {
            produtosGrupo.splice(idx, 1);
            atualizarProdutosGrupo();
        }
        function carregarGrupos() {
            const status = document.getElementById('filtroStatus').value;
            fetch('listar-grupos-pedido-mensal.php?status='+status)
                .then(r => r.json())
                .then(data => {
                    const tbody = document.getElementById('grupos-tbody');
                    tbody.innerHTML = '';
                    data.forEach(grupo => {
                        const statusClass = grupo.status === 'inativo' ? 'status-badge status-inativo' : 'status-badge status-ativo';
                        tbody.innerHTML += `<tr>
                            <td class='codigo'>${grupo.codigo}</td>
                            <td>${grupo.nome}</td>
                            <td>${grupo.produtos.map(p => p.nome + ' (' + p.quantidade + ')').join(', ')}</td>
                            <td class='status'><span class='${statusClass}'>${grupo.status ? grupo.status.charAt(0).toUpperCase() + grupo.status.slice(1) : 'Ativo'}</span></td>
                            <td><button class='btn' onclick='abrirPopupEditarGrupo(${JSON.stringify(grupo)})'>Detalhes</button></td>
                        </tr>`;
                    });
                });
        }
        document.addEventListener('DOMContentLoaded', carregarGrupos);
        function buscarProximoCodigoGrupo(callback) {
            fetch('listar-grupos-pedido-mensal.php')
                .then(r => r.json())
                .then(data => {
                    let max = 0;
                    data.forEach(g => { if (parseInt(g.codigo) > max) max = parseInt(g.codigo); });
                    callback((max+1).toString());
                });
        }
        function abrirPopupEditarGrupo(grupo) {
            document.getElementById('popupEditarGrupoOverlay').style.display = 'flex';
            document.getElementById('edit-id').value = grupo.id;
            document.getElementById('edit-codigo').value = grupo.codigo;
            document.getElementById('edit-nome').value = grupo.nome;
            document.getElementById('titulo-nome-grupo').textContent = grupo.nome;
            document.getElementById('grupo-nome-acoes').textContent = grupo.nome;
            modoEdicaoGrupo = false;
            document.getElementById('edit-nome').readOnly = true;
            document.getElementById('btn-add-produto-editar').style.display = 'none';
            document.getElementById('btn-salvar-editar').style.display = 'none';
            editProdutosGrupo = grupo.produtos.map(p => ({codigo:p.codigo, nome:p.nome, quantidade:p.quantidade}));
            atualizarProdutosGrupoEditar();
            mostrarAbaGrupo('detalhes');
            const btnInativar = document.querySelector('.btn-inativar-grupo');
            const btnAtivar = document.querySelector('.btn-ativar-grupo');
            if (grupo.status === 'inativo') {
                if (!btnAtivar) {
                    const btn = document.createElement('button');
                    btn.type = 'button';
                    btn.className = 'btn-acoes-grupo btn-ativar-grupo';
                    btn.style = 'width:100%; background:#2563eb; color:#fff; font-weight:600; font-size:16px; display:flex; align-items:center; justify-content:center; gap:8px; margin-bottom:14px; border-radius:7px; border:none; padding:12px 0;';
                    btn.innerHTML = '<svg width="20" height="20" fill="#fff" viewBox="0 0 24 24" style="margin-right:6px;"><rect x="6" y="11" width="12" height="2" rx="1"/><rect x="11" y="6" width="2" height="12" rx="1"/></svg> Ativar Grupo';
                    btn.onclick = ativarGrupo;
                    const parent = document.querySelector('#aba-acoes-grupo > div');
                    if (btnInativar) parent.replaceChild(btn, btnInativar);
                    else parent.insertBefore(btn, parent.firstChild);
                }
            } else {
                if (!btnInativar) {
                    const btn = document.createElement('button');
                    btn.type = 'button';
                    btn.className = 'btn-acoes-grupo btn-inativar-grupo';
                    btn.style = 'width:100%; background:#ffb020; color:#fff; font-weight:600; font-size:16px; display:flex; align-items:center; justify-content:center; gap:8px; margin-bottom:14px; border-radius:7px; border:none; padding:12px 0;';
                    btn.innerHTML = '<svg width="20" height="20" fill="#fff" viewBox="0 0 24 24" style="margin-right:6px;"><rect x="6" y="11" width="12" height="2" rx="1"/></svg> Inativar Grupo';
                    btn.onclick = inativarGrupo;
                    const parent = document.querySelector('#aba-acoes-grupo > div');
                    if (btnAtivar) parent.replaceChild(btn, btnAtivar);
                    else parent.insertBefore(btn, parent.firstChild);
                }
            }
        }
        function fecharPopupEditarGrupo() {
            document.getElementById('popupEditarGrupoOverlay').style.display = 'none';
        }
        function atualizarProdutosGrupoEditar() {
            const div = document.getElementById('edit-produtos-grupo-lista');
            div.innerHTML = '<table class="produtos-lista"><tr><th>Produto</th><th>Quantidade</th><th></th></tr>' +
                editProdutosGrupo.map((p, i) => `<tr><td>${p.nome}</td><td><input type='number' min='1' value='${p.quantidade}' style='width:60px;' onchange='salvarQuantidadeGrupoEditar(${i}, this.value)' ${modoEdicaoGrupo ? '' : 'readonly'}></td><td>${modoEdicaoGrupo ? `<button type='button' onclick='removerProdutoGrupoEditar(${i})' title='Remover' style='background:none; border:none; padding:0; margin:0; box-shadow:none;'><svg width="18" height="18" viewBox="0 0 24 24" fill="black" xmlns="http://www.w3.org/2000/svg"><path d="M6 7V19C6 20.1046 6.89543 21 8 21H16C17.1046 21 18 20.1046 18 19V7M9 10V17M15 10V17M4 7H20M10 4H14C14.5523 4 15 4.44772 15 5V6H9V5C9 4.44772 9.44772 4 10 4Z"/></svg></button>` : ''}</td></tr>`).join('') + '</table>';
        }
        function removerProdutoGrupoEditar(idx) {
            editProdutosGrupo.splice(idx, 1);
            atualizarProdutosGrupoEditar();
        }
        function salvarQuantidadeGrupo(idx, valor) {
            let v = parseInt(valor);
            if (!isNaN(v) && v > 0) produtosGrupo[idx].quantidade = v;
            atualizarProdutosGrupo();
        }
        function salvarQuantidadeGrupoEditar(idx, valor) {
            let v = parseInt(valor);
            if (!isNaN(v) && v > 0) editProdutosGrupo[idx].quantidade = v;
            atualizarProdutosGrupoEditar();
        }
        function carregarProdutosPopup() {
            fetch('listar-produtos.php')
                .then(r => r.json())
                .then(produtos => {
                    const tbody = document.getElementById('produtosPopupLista');
                    tbody.innerHTML = '';
                    produtos.forEach(prod => {
                        tbody.innerHTML += `<tr><td>${prod.codigo}</td><td>${prod.nome}</td><td><button type='button' onclick='selecionarProdutoPopup(${prod.codigo}, \"${prod.nome.replace(/"/g, '&quot;')}\")'>Selecionar</button></td></tr>`;
                    });
                });
        }
        function abrirPopupProdutosNovoGrupo() {
            modoNovoGrupo = true;
            document.getElementById('popupProdutosOverlay').style.display = 'flex';
            document.getElementById('filtro-produtos').value = '';
            carregarProdutosPopup();
        }
        function abrirPopupProdutosEditar() {
            modoNovoGrupo = false;
            document.getElementById('popupProdutosOverlay').style.display = 'flex';
            document.getElementById('filtro-produtos').value = '';
            carregarProdutosPopup();
        }
        function fecharPopupProdutosEditar() {
            document.getElementById('popupProdutosOverlay').style.display = 'none';
        }
        function selecionarProdutoPopup(codigo, nome) {
            if (modoNovoGrupo) {
                adicionarProdutoGrupoDireto(codigo, nome);
            } else {
                selecionarProdutoEditar(codigo, nome);
            }
        }
        function selecionarProdutoEditar(codigo, nome) {
            // Verificar se o produto já foi selecionado
            if (produtoJaSelecionadoGrupo(codigo, editProdutosGrupo)) {
                mostrarPopupAviso("Produto já inserido");
                return;
            }

            editProdutosGrupo.push({codigo, nome, quantidade:1});
            atualizarProdutosGrupoEditar();
            fecharPopupProdutosEditar();
        }

        // Função para verificar se o produto já foi selecionado no grupo
        function produtoJaSelecionadoGrupo(codigo, listaProdutos) {
            for (let produto of listaProdutos) {
                if (produto.codigo == codigo) {
                    return true;
                }
            }
            return false;
        }

        // Função para mostrar popup de aviso
        function mostrarPopupAviso(mensagem) {
            // Criar o popup se não existir
            let popup = document.getElementById('popup-aviso-produto');
            if (!popup) {
                popup = document.createElement('div');
                popup.id = 'popup-aviso-produto';
                popup.className = 'popup-aviso-produto';
                popup.innerHTML = `
                    <div class="popup-aviso-content">
                        <span class="popup-aviso-texto">${mensagem}</span>
                    </div>
                `;
                document.body.appendChild(popup);
            } else {
                popup.querySelector('.popup-aviso-texto').textContent = mensagem;
            }

            // Mostrar o popup
            popup.style.display = 'flex';

            // Esconder após 2 segundos
            setTimeout(() => {
                popup.style.display = 'none';
            }, 2000);
        }

        function filtrarProdutos() {
            const filtro = document.getElementById('filtro-produtos').value.toLowerCase();
            const linhas = document.querySelectorAll('#produtosPopupLista tr');
            linhas.forEach(linha => {
                if (linha.cells && linha.cells.length > 0) {
                    const texto = linha.textContent.toLowerCase();
                    linha.style.display = texto.includes(filtro) ? '' : 'none';
                }
            });
        }
        function filtrarPorNome() {
            const filtro = document.getElementById('filtroNome').value.toLowerCase();
            const linhas = document.querySelectorAll('#grupos-tbody tr');
            linhas.forEach(linha => {
                const nome = linha.children[1]?.textContent.toLowerCase() || '';
                if (nome.includes(filtro)) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
        }
        // Chamar filtrarPorNome sempre que carregarGrupos for chamado
        const originalCarregarGrupos = carregarGrupos;
        carregarGrupos = function() {
            originalCarregarGrupos();
            setTimeout(filtrarPorNome, 100); // Pequeno delay para garantir que as linhas já foram renderizadas
        };
        document.getElementById('formGrupo').onsubmit = function(e) {
            e.preventDefault();
            const form = e.target;
            const dados = new FormData(form);
            dados.append('produtos', JSON.stringify(produtosGrupo));
            fetch('salvar-grupo-pedido-mensal.php', { method: 'POST', body: dados })
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        fecharPopupGrupo();
                        carregarGrupos();
                    } else {
                        alert('Erro ao salvar grupo!');
                    }
                });
        };
        document.getElementById('formEditarGrupo').onsubmit = function(e) {
            e.preventDefault();
            const form = e.target;
            const dados = new FormData(form);
            dados.append('produtos', JSON.stringify(editProdutosGrupo));
            fetch('editar-grupo-pedido-mensal.php', { method: 'POST', body: dados })
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        fecharPopupEditarGrupo();
                        carregarGrupos();
                    } else {
                        alert('Erro ao editar grupo!');
                    }
                });
        };
        function excluirGrupo() {
            if (!confirm('Tem certeza que deseja excluir este grupo?')) return;
            const id = document.getElementById('edit-id').value;
            fetch('excluir-grupo-pedido-mensal.php', { method: 'POST', body: new URLSearchParams({id}) })
                .then(r => r.json())
                .then(data => { if (data.success) { fecharPopupEditarGrupo(); carregarGrupos(); } else alert('Erro ao excluir!'); });
        }
        function inativarGrupo() {
            const id = document.getElementById('edit-id').value;
            fetch('inativar-grupo-pedido-mensal.php', { method: 'POST', body: new URLSearchParams({id}) })
                .then(r => r.json())
                .then(data => { if (data.success) { fecharPopupEditarGrupo(); carregarGrupos(); } else alert('Erro ao inativar!'); });
        }
        function ativarGrupo() {
            const id = document.getElementById('edit-id').value;
            fetch('ativar-grupo-pedido-mensal.php', { method: 'POST', body: new URLSearchParams({id}) })
                .then(r => r.json())
                .then(data => { if (data.success) { fecharPopupEditarGrupo(); carregarGrupos(); } else alert('Erro ao ativar!'); });
        }
        function mostrarAbaGrupo(qual) {
            document.getElementById('tab-detalhes').classList.remove('active');
            document.getElementById('tab-acoes').classList.remove('active');
            if (qual === 'detalhes') {
                document.getElementById('tab-detalhes').classList.add('active');
                document.getElementById('aba-detalhes-grupo').style.display = '';
                document.getElementById('aba-acoes-grupo').style.display = 'none';
            } else {
                document.getElementById('tab-acoes').classList.add('active');
                document.getElementById('aba-detalhes-grupo').style.display = 'none';
                document.getElementById('aba-acoes-grupo').style.display = '';
            }
        }
        document.getElementById('btn-editar-grupo').onclick = function() {
            modoEdicaoGrupo = true;
            document.getElementById('edit-nome').readOnly = false;
            document.getElementById('btn-add-produto-editar').style.display = '';
            document.getElementById('btn-salvar-editar').style.display = '';
            atualizarProdutosGrupoEditar();
        };
        function tentarFecharPopupEditarGrupo() {
            if (modoEdicaoGrupo) {
                document.getElementById('popupConfirmarSaidaEdicao').style.display = 'flex';
            } else {
                fecharPopupEditarGrupo();
            }
        }
        function confirmarSaidaEdicaoGrupo(descartar) {
            document.getElementById('popupConfirmarSaidaEdicao').style.display = 'none';
            if (descartar) {
                modoEdicaoGrupo = false;
                document.getElementById('edit-nome').readOnly = true;
                document.getElementById('btn-add-produto-editar').style.display = 'none';
                document.getElementById('btn-salvar-editar').style.display = 'none';
                atualizarProdutosGrupoEditar();
                fecharPopupEditarGrupo();
            }
        }
    </script>
</body>
</html> 
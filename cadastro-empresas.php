<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';
$prox_codigo_empresa = 1;
$res = $conn->query("SELECT MAX(codigo_empresa) as max_codigo FROM empresas");
if ($res && $row = $res->fetch_assoc()) {
    $prox_codigo_empresa = $row['max_codigo'] + 1;
    if ($prox_codigo_empresa < 1) $prox_codigo_empresa = 1;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Cadastro de Empresas</title>
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        /* Título minimalista */
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        /* Formulário minimalista */
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        /* Grupos de campos */
        .form-group {
            margin-bottom: 10px;
        }
        
        /* Labels minimalistas */
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        
        /* Inputs minimalistas */
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            font-family: inherit;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        input:hover, select:hover, textarea:hover {
            border-color: #9ca3af;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        /* Botão minimalista */
        button[type="submit"] {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        button[type="submit"]:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        button[type="submit"]:active {
            transform: translateY(0);
        }
        
        /* Mensagem de erro */
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        /* Popup minimalista */
        .popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2563eb;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid #1d4ed8;
        }

        .popup.show {
            opacity: 1;
            transform: translateY(0);
        }

        .popup.error {
            background: #dc2626;
            border-color: #b91c1c;
        }
        
        /* Validação de campos */
        input:invalid, select:invalid, textarea:invalid {
            border-color: #d1d5db;
            background-color: #ffffff;
        }
        
        input:invalid:focus, select:invalid:focus, textarea:invalid:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Classe para campos com erro */
        .field-error {
            border-color: #dc2626 !important;
            background-color: #fef2f2 !important;
        }
        
        .field-error:focus {
            border-color: #dc2626 !important;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
        }
        
        /* Placeholder para campos com erro */
        .field-error::placeholder {
            color: #dc2626;
            font-weight: 500;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            
            form {
                padding: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <h1>Cadastro de Empresas</h1>
        <form action="salvar-empresa.php" method="POST">
            <div class="form-group">
                <label for="codigo_empresa">Código</label>
                <input type="number" id="codigo_empresa" name="codigo_empresa" value="<?php echo isset($_GET['codigo_empresa']) ? htmlspecialchars($_GET['codigo_empresa']) : $prox_codigo_empresa; ?>">
            </div>

            <div class="form-group">
                <label for="nome_empresa">Nome da Empresa</label>
                <?php if (isset($_GET['status'], $_GET['message']) && ($_GET['message'] === 'nome_duplicado' || $_GET['message'] === 'codigo_empresa_duplicado')): ?>
                    <div class="error-message">Este nome de empresa ou código já existe.</div>
                <?php endif; ?>
                <input type="text" id="nome_empresa" name="nome_empresa" value="<?php echo isset($_GET['nome_empresa']) ? htmlspecialchars($_GET['nome_empresa']) : ''; ?>">
            </div>

            <div class="form-group">
                <label for="fantasia_empresa">Nome Fantasia</label>
                <input type="text" id="fantasia_empresa" name="fantasia_empresa" value="<?php echo isset($_GET['fantasia_empresa']) ? htmlspecialchars($_GET['fantasia_empresa']) : ''; ?>">
            </div>

            <div class="form-group">
                <label for="cnpj">CNPJ</label>
                <input type="text" id="cnpj" name="cnpj" value="<?php echo isset($_GET['cnpj']) ? htmlspecialchars($_GET['cnpj']) : ''; ?>">
            </div>

            <div class="form-group">
                <label for="contrato">Contrato</label>
                <input type="text" id="contrato" name="contrato" value="<?php echo isset($_GET['contrato']) ? htmlspecialchars($_GET['contrato']) : ''; ?>">
            </div>

            <div class="form-group">
                <label for="regiao">Região</label>
                <input type="text" id="regiao" name="regiao" value="<?php echo isset($_GET['regiao']) ? htmlspecialchars($_GET['regiao']) : ''; ?>">
            </div>

            <div class="form-group">
                <label for="contratos">Contratos</label>
                <textarea id="contratos" name="contratos" rows="4"><?php echo isset($_GET['contratos']) ? htmlspecialchars($_GET['contratos']) : ''; ?></textarea>
            </div>

            <button type="submit">Cadastrar Empresa</button>
        </form>

        <!-- Popup para mensagens -->
        <div id="popup" class="popup"></div>

        <script>
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            const message = urlParams.get('message');
            const popup = document.getElementById('popup');

            if (status === 'success') {
                popup.textContent = "Empresa cadastrada com sucesso!";
                popup.classList.add('show');
            } else if (status === 'error') {
                if (message === 'codigo_empresa_duplicado') {
                    popup.textContent = "Erro: o código da empresa já existe!";
                } else if (message === 'nome_duplicado') {
                    popup.textContent = "Erro: o nome da empresa já existe!";
                } else {
                    popup.textContent = "Erro ao cadastrar empresa.";
                }
                popup.classList.add('show', 'error');
            }

            if (popup.classList.contains('show')) {
                setTimeout(() => {
                    popup.classList.remove('show');
                }, 5000);
            }

            // Validação no envio do formulário
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                let isValid = true;
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    if (!input.value.trim()) {
                        isValid = false;
                        input.classList.add('field-error');
                        // Adicionar placeholder de erro baseado no nome do campo
                        const fieldName = input.getAttribute('name');
                        let errorText = '';
                        switch(fieldName) {
                            case 'codigo_empresa':
                                errorText = 'Preencha o campo Código';
                                break;
                            case 'nome_empresa':
                                errorText = 'Preencha o campo Nome da Empresa';
                                break;
                            case 'fantasia_empresa':
                                errorText = 'Preencha o campo Nome Fantasia';
                                break;
                            case 'cnpj':
                                errorText = 'Preencha o campo CNPJ';
                                break;
                            case 'contrato':
                                errorText = 'Preencha o campo Contrato';
                                break;
                            case 'regiao':
                                errorText = 'Preencha o campo Região';
                                break;
                            case 'contratos':
                                errorText = 'Preencha o campo Contratos';
                                break;
                            default:
                                errorText = 'Campo obrigatório';
                        }
                        input.setAttribute('placeholder', errorText);
                    } else {
                        input.classList.remove('field-error');
                        // Restaurar placeholder original se existir
                        const originalPlaceholder = input.getAttribute('data-original-placeholder');
                        if (originalPlaceholder) {
                            input.setAttribute('placeholder', originalPlaceholder);
                        } else {
                            input.removeAttribute('placeholder');
                        }
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    return;
                }
            });

            // Remover classe de erro quando o usuário começar a digitar
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (this.classList.contains('field-error')) {
                        this.classList.remove('field-error');
                    }
                });
                
                input.addEventListener('change', function() {
                    if (this.classList.contains('field-error')) {
                        this.classList.remove('field-error');
                    }
                });
            });
        </script>
    </div>
</body>
</html>

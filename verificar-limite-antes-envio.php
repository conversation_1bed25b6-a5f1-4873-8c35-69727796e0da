<?php
// Iniciar sessão apenas se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'conexao.php';

header('Content-Type: application/json');

try {
    // Obter dados da requisição
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!$data) {
        throw new Exception('Dados inválidos');
    }

    $empresa = trim($data['empresa'] ?? '');
    $setor = trim($data['setor'] ?? '');
    $valor_operacao = floatval($data['valor_total'] ?? 0);

    if (empty($empresa) && empty($setor)) {
        throw new Exception('Empresa ou setor deve ser informado');
    }

    if ($valor_operacao <= 0) {
        throw new Exception('Valor da operação deve ser maior que zero');
    }

    // Buscar limite ativo (prioriza empresa, depois setor)
    $limite_encontrado = null;
    $gastos_realizados = 0;
    $tipo_limite = '';

    // 1. Tentar buscar limite por empresa
    if (!empty($empresa)) {
        $stmt = $conn->prepare("
            SELECT lg.*, e.nome_empresa 
            FROM limites_gastos lg 
            JOIN empresas e ON lg.id_empresa = e.codigo_empresa 
            WHERE lg.tipo = 'empresa' AND e.nome_empresa = ? AND lg.status = 'ativo'
            ORDER BY lg.id DESC 
            LIMIT 1
        ");
        $stmt->bind_param("s", $empresa);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $limite_encontrado = $result->fetch_assoc();
            $tipo_limite = 'empresa';
            
            // Buscar gastos realizados para esta empresa
            $stmt_gastos = $conn->prepare("
                SELECT COALESCE(SUM(valor_total), 0) as total_gastos 
                FROM gastos_realizados 
                WHERE empresa = ? AND MONTH(data_operacao) = MONTH(CURRENT_DATE()) AND YEAR(data_operacao) = YEAR(CURRENT_DATE())
            ");
            $stmt_gastos->bind_param("s", $empresa);
            $stmt_gastos->execute();
            $result_gastos = $stmt_gastos->get_result();
            $row_gastos = $result_gastos->fetch_assoc();
            $gastos_realizados = floatval($row_gastos['total_gastos']);
        }
    }

    // 2. Se não encontrou limite de empresa, tentar por setor
    if (!$limite_encontrado && !empty($setor)) {
        $stmt = $conn->prepare("
            SELECT lg.*, s.nome 
            FROM limites_gastos lg 
            JOIN setor s ON lg.id_setor = s.id 
            WHERE lg.tipo = 'setor' AND s.nome = ? AND lg.status = 'ativo'
            ORDER BY lg.id DESC 
            LIMIT 1
        ");
        $stmt->bind_param("s", $setor);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $limite_encontrado = $result->fetch_assoc();
            $tipo_limite = 'setor';
            
            // Buscar gastos realizados para este setor
            $stmt_gastos = $conn->prepare("
                SELECT COALESCE(SUM(valor_total), 0) as total_gastos 
                FROM gastos_realizados 
                WHERE setor = ? AND MONTH(data_operacao) = MONTH(CURRENT_DATE()) AND YEAR(data_operacao) = YEAR(CURRENT_DATE())
            ");
            $stmt_gastos->bind_param("s", $setor);
            $stmt_gastos->execute();
            $result_gastos = $stmt_gastos->get_result();
            $row_gastos = $result_gastos->fetch_assoc();
            $gastos_realizados = floatval($row_gastos['total_gastos']);
        }
    }

    // Se não encontrou limite, não há restrição
    if (!$limite_encontrado) {
        echo json_encode([
            'success' => true,
            'tem_limite' => false,
            'pode_prosseguir' => true,
            'message' => 'Nenhum limite cadastrado'
        ]);
        exit;
    }

    // Calcular valores
    $valor_limite = floatval($limite_encontrado['valor_limite']);
    $saldo_aviso = floatval($limite_encontrado['saldo_aviso']);
    $total_com_operacao = $gastos_realizados + $valor_operacao;
    
    // Determinar status
    $status = 'ok';
    $message = '';
    $pode_prosseguir = true;

    if ($total_com_operacao >= $valor_limite) {
        $status = 'limite_excedido';
        $message = "O valor para esta {$tipo_limite} excedeu o limite de R$ " . number_format($valor_limite, 2, ',', '.') . ". Operação bloqueada.";
        $pode_prosseguir = false;
    } elseif ($total_com_operacao >= $saldo_aviso) {
        $status = 'aviso';
        $message = "O valor para esta {$tipo_limite} está chegando ao limite. Atual: R$ " . number_format($total_com_operacao, 2, ',', '.') . " / Limite: R$ " . number_format($valor_limite, 2, ',', '.');
        $pode_prosseguir = true;
    }

    echo json_encode([
        'success' => true,
        'tem_limite' => true,
        'pode_prosseguir' => $pode_prosseguir,
        'status' => $status,
        'message' => $message,
        'dados' => [
            'tipo_limite' => $tipo_limite,
            'nome_limite' => $tipo_limite === 'empresa' ? $empresa : $setor,
            'valor_limite' => $valor_limite,
            'saldo_aviso' => $saldo_aviso,
            'gastos_realizados' => $gastos_realizados,
            'valor_operacao' => $valor_operacao,
            'total_com_operacao' => $total_com_operacao
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao verificar limite: ' . $e->getMessage()
    ]);
}

$conn->close();
?>

<?php
include 'conexao.php';

echo "<h2>Teste do Popup Instantâneo</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Popup Instantaneo', '001', 'INSTANT', 'Teste do popup instantaneo', '1', 'Teste', 'TI', 'Requisição para testar popup instantaneo', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'INSTANT', 1)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>⚡ Popup Instantâneo Configurado!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Configuração aplicada:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Sem animação:</strong> Aparece instantaneamente</li>";
    echo "<li>✅ <strong>Sem efeitos:</strong> Nenhum fade, slide ou escala</li>";
    echo "<li>✅ <strong>Posicionamento direto:</strong> Centro da tela imediatamente</li>";
    echo "<li>✅ <strong>Sem transições:</strong> Aparição imediata</li>";
    echo "<li>✅ <strong>Performance otimizada:</strong> Sem processamento de animação</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<h4>⚡ Teste de Aparição Instantânea:</h4>";
    echo "<ol>";
    echo "<li>Ir para 'Todas as Solicitações'</li>";
    echo "<li>Localizar a requisição com fundo vermelho claro</li>";
    echo "<li>Clicar na requisição para abrir o popup de detalhes</li>";
    echo "<li>Clicar no botão 'Excluir Pedido Urgente'</li>";
    echo "<li><strong>Verificar:</strong> Popup aparece INSTANTANEAMENTE no centro</li>";
    echo "<li><strong>Verificar:</strong> SEM nenhum efeito visual ou animação</li>";
    echo "<li><strong>Verificar:</strong> Aparição imediata e direta</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='todas-solitacoes-estoque.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>⚡ Testar Popup Instantâneo</a></p>";

$conn->close();
?>

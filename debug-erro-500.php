<?php
// Ativar exibição de erros
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug - Erro 500 no Histórico</h2>";

// Simular sessão se não existir
session_start();
if (!isset($_SESSION['usuario_id'])) {
    echo "<p>⚠️ Simulando login de usuário...</p>";
    $_SESSION['usuario_id'] = 1;
}

echo "<p>✅ Usuário ID: " . $_SESSION['usuario_id'] . "</p>";

// Testar conexão
echo "<h3>1. Teste de Conexão:</h3>";
try {
    require_once 'conexao.php';
    echo "<p>✅ Conexão com banco estabelecida</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro na conexão: " . $e->getMessage() . "</p>";
    exit();
}

// Testar cada parte do código separadamente
echo "<h3>2. Teste das Consultas:</h3>";

try {
    $historico = [];
    
    // Verificar tabela usuarios
    echo "<h4>Verificando tabela usuarios:</h4>";
    $result_usuarios = $conn->query("SHOW TABLES LIKE 'usuarios'");
    if ($result_usuarios) {
        $tem_usuarios = $result_usuarios->num_rows > 0;
        echo "<p>Tabela usuarios existe: " . ($tem_usuarios ? "Sim" : "Não") . "</p>";
    } else {
        echo "<p style='color: red;'>Erro ao verificar tabela usuarios: " . $conn->error . "</p>";
    }
    
    // Testar devolucoes_epi
    echo "<h4>Testando devolucoes_epi:</h4>";
    $result_func = $conn->query("SHOW TABLES LIKE 'devolucoes_epi'");
    if ($result_func) {
        echo "<p>Consulta SHOW TABLES executada com sucesso</p>";
        
        if ($result_func->num_rows > 0) {
            echo "<p>✅ Tabela devolucoes_epi existe</p>";
            
            // Testar consulta simples
            $sql_test = "SELECT COUNT(*) as total FROM devolucoes_epi";
            $result_count = $conn->query($sql_test);
            if ($result_count) {
                $count = $result_count->fetch_assoc()['total'];
                echo "<p>Total de registros: $count</p>";
                
                if ($count > 0) {
                    // Testar consulta completa
                    $sql_funcionario = "
                        SELECT 
                            d.id,
                            d.pessoa_id,
                            d.pessoa_nome,
                            d.produto_id,
                            d.produto_nome,
                            d.quantidade,
                            d.estado,
                            d.data_devolucao,
                            d.usuario_id,
                            'Sistema' as usuario_nome,
                            'funcionario' as tipo,
                            COALESCE(d.assinatura, '') as assinatura
                        FROM devolucoes_epi d
                        ORDER BY d.data_devolucao DESC
                        LIMIT 3
                    ";
                    
                    echo "<p>Executando consulta completa...</p>";
                    $result = $conn->query($sql_funcionario);
                    if ($result) {
                        echo "<p>✅ Consulta executada com sucesso</p>";
                        echo "<p>Linhas retornadas: " . $result->num_rows . "</p>";
                        
                        while ($row = $result->fetch_assoc()) {
                            $historico[] = $row;
                            echo "<p>Registro ID " . $row['id'] . " adicionado</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>❌ Erro na consulta: " . $conn->error . "</p>";
                    }
                }
            } else {
                echo "<p style='color: red;'>❌ Erro ao contar registros: " . $conn->error . "</p>";
            }
        } else {
            echo "<p>⚠️ Tabela devolucoes_epi não existe</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Erro ao verificar tabela: " . $conn->error . "</p>";
    }
    
    // Testar devolucoes_rapidas
    echo "<h4>Testando devolucoes_rapidas:</h4>";
    $result_rapida = $conn->query("SHOW TABLES LIKE 'devolucoes_rapidas'");
    if ($result_rapida) {
        if ($result_rapida->num_rows > 0) {
            echo "<p>✅ Tabela devolucoes_rapidas existe</p>";
            
            $sql_test = "SELECT COUNT(*) as total FROM devolucoes_rapidas";
            $result_count = $conn->query($sql_test);
            if ($result_count) {
                $count = $result_count->fetch_assoc()['total'];
                echo "<p>Total de registros: $count</p>";
                
                if ($count > 0) {
                    $sql_rapida = "
                        SELECT 
                            d.id,
                            NULL as pessoa_id,
                            NULL as pessoa_nome,
                            d.produto_id,
                            d.produto_nome,
                            d.quantidade,
                            d.estado,
                            d.data_devolucao,
                            d.usuario_id,
                            'Sistema' as usuario_nome,
                            'rapida' as tipo,
                            COALESCE(d.assinatura, '') as assinatura
                        FROM devolucoes_rapidas d
                        ORDER BY d.data_devolucao DESC
                        LIMIT 3
                    ";
                    
                    $result = $conn->query($sql_rapida);
                    if ($result) {
                        echo "<p>✅ Consulta executada com sucesso</p>";
                        while ($row = $result->fetch_assoc()) {
                            $historico[] = $row;
                        }
                    } else {
                        echo "<p style='color: red;'>❌ Erro na consulta: " . $conn->error . "</p>";
                    }
                }
            }
        } else {
            echo "<p>⚠️ Tabela devolucoes_rapidas não existe</p>";
        }
    }
    
    // Testar ordenação
    echo "<h4>Testando ordenação:</h4>";
    if (!empty($historico)) {
        echo "<p>Registros antes da ordenação: " . count($historico) . "</p>";
        
        usort($historico, function($a, $b) {
            return strtotime($b['data_devolucao']) - strtotime($a['data_devolucao']);
        });
        
        echo "<p>✅ Ordenação executada com sucesso</p>";
        echo "<p>Registros após ordenação: " . count($historico) . "</p>";
    } else {
        echo "<p>⚠️ Nenhum registro para ordenar</p>";
    }
    
    // Testar JSON
    echo "<h4>Testando JSON:</h4>";
    $json = json_encode($historico);
    if ($json !== false) {
        echo "<p>✅ JSON gerado com sucesso</p>";
        echo "<p>Tamanho do JSON: " . strlen($json) . " bytes</p>";
        
        if (count($historico) > 0) {
            echo "<h5>Primeiro registro:</h5>";
            echo "<pre>" . json_encode($historico[0], JSON_PRETTY_PRINT) . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ Erro ao gerar JSON: " . json_last_error_msg() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exceção capturada: " . $e->getMessage() . "</p>";
    echo "<p>Arquivo: " . $e->getFile() . "</p>";
    echo "<p>Linha: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>3. Teste do Endpoint Real:</h3>";
echo "<p>Agora vamos testar o endpoint real...</p>";

// Capturar saída do endpoint
ob_start();
$error_occurred = false;

try {
    include 'obter-historico-devolucoes.php';
} catch (Exception $e) {
    $error_occurred = true;
    echo "Erro no endpoint: " . $e->getMessage();
}

$output = ob_get_clean();

if ($error_occurred) {
    echo "<p style='color: red;'>❌ Erro ao executar endpoint</p>";
} else {
    echo "<p>✅ Endpoint executado</p>";
}

echo "<h4>Saída do endpoint:</h4>";
echo "<pre>" . htmlspecialchars($output) . "</pre>";

echo "<h3>4. Links para Teste:</h3>";
echo "<ul>";
echo "<li><a href='obter-historico-devolucoes.php' target='_blank'>Testar endpoint diretamente</a></li>";
echo "<li><a href='devolucao.php' target='_blank'>Testar página de devolução</a></li>";
echo "</ul>";

$conn->close();
?>

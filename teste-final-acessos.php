<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
}

require_once 'conexao.php';

echo "<h2>✅ Teste Final - Novas Abas de Acesso</h2>";

// 1. Verificar se as alterações foram aplicadas
echo "<h3>1. 📋 Verificação das Alterações:</h3>";

// Ler o arquivo gerenciar-usuarios.php e verificar se contém as novas abas
$conteudo = file_get_contents('gerenciar-usuarios.php');

$abas_verificar = [
    'limites-gastos' => 'Limites de Gastos',
    'devolucao' => 'Devolução'
];

foreach ($abas_verificar as $aba => $nome) {
    if (strpos($conteudo, $aba) !== false) {
        echo "<p>✅ <strong>$nome</strong> encontrada no código</p>";
    } else {
        echo "<p>❌ <strong>$nome</strong> NÃO encontrada no código</p>";
    }
}

// 2. Verificar estrutura de menus no JavaScript
echo "<h3>2. 🎯 Estrutura de Menus Atualizada:</h3>";

if (strpos($conteudo, 'configurar-limites') !== false) {
    echo "<p>✅ Sub-item 'Configurar Limites' encontrado</p>";
}
if (strpos($conteudo, 'historico-devolucoes') !== false) {
    echo "<p>✅ Sub-item 'Histórico de Devoluções' encontrado</p>";
}

// 3. Testar endpoint de acessos
echo "<h3>3. 🌐 Teste do Endpoint de Acessos:</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/sistema/obter-acessos-usuario.php?usuario_id=1');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code == 200) {
    echo "<p>✅ Endpoint respondeu com sucesso (HTTP 200)</p>";
    
    $data = json_decode($response, true);
    if (is_array($data)) {
        echo "<p>✅ JSON válido retornado</p>";
        echo "<p>Total de acessos configurados: " . count($data) . "</p>";
        
        // Verificar se as novas abas estão nos dados
        $tem_limites = false;
        $tem_devolucao = false;
        
        foreach ($data as $acesso) {
            if (isset($acesso['menu'])) {
                if ($acesso['menu'] === 'limites-gastos') $tem_limites = true;
                if ($acesso['menu'] === 'devolucao') $tem_devolucao = true;
            }
        }
        
        echo "<p>" . ($tem_limites ? "✅" : "⚠️") . " Aba 'Limites de Gastos' " . ($tem_limites ? "configurada" : "não encontrada") . "</p>";
        echo "<p>" . ($tem_devolucao ? "✅" : "⚠️") . " Aba 'Devolução' " . ($tem_devolucao ? "configurada" : "não encontrada") . "</p>";
        
    } else {
        echo "<p>❌ Resposta não é um JSON válido</p>";
    }
} else {
    echo "<p>❌ Endpoint retornou erro HTTP $http_code</p>";
}

// 4. Verificar tabela de acessos
echo "<h3>4. 🗄️ Verificação da Tabela de Acessos:</h3>";

$sql_check = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios_acessos'";
$result = $conn->query($sql_check);

if ($result && $result->fetch_assoc()['count'] > 0) {
    echo "<p>✅ Tabela 'usuarios_acessos' existe</p>";
    
    // Verificar se existem registros para as novas abas
    $sql_limites = "SELECT COUNT(*) as count FROM usuarios_acessos WHERE menu = 'limites-gastos'";
    $result_limites = $conn->query($sql_limites);
    $count_limites = $result_limites->fetch_assoc()['count'];
    
    $sql_devolucao = "SELECT COUNT(*) as count FROM usuarios_acessos WHERE menu = 'devolucao'";
    $result_devolucao = $conn->query($sql_devolucao);
    $count_devolucao = $result_devolucao->fetch_assoc()['count'];
    
    echo "<p>Registros 'limites-gastos': $count_limites</p>";
    echo "<p>Registros 'devolucao': $count_devolucao</p>";
    
} else {
    echo "<p>❌ Tabela 'usuarios_acessos' não existe</p>";
}

// 5. Resumo das novas funcionalidades
echo "<h3>5. 🎉 Resumo das Novas Abas Adicionadas:</h3>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";

echo "<div style='background: linear-gradient(135deg, #e8f5e8, #d4edda); padding: 20px; border-radius: 10px; border-left: 5px solid #28a745;'>";
echo "<h4>💰 Limites de Gastos</h4>";
echo "<ul>";
echo "<li><strong>configurar-limites</strong> - Configurar Limites</li>";
echo "<li><strong>relatorio-gastos</strong> - Relatório de Gastos</li>";
echo "<li><strong>alertas-limites</strong> - Alertas de Limites</li>";
echo "</ul>";
echo "<p><em>Controle orçamentário e financeiro</em></p>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #e3f2fd, #d1ecf1); padding: 20px; border-radius: 10px; border-left: 5px solid #007cba;'>";
echo "<h4>🔄 Devolução</h4>";
echo "<ul>";
echo "<li><strong>devolucao</strong> - Processar Devoluções</li>";
echo "<li><strong>historico-devolucoes</strong> - Histórico de Devoluções</li>";
echo "<li><strong>relatorio-devolucoes</strong> - Relatório de Devoluções</li>";
echo "</ul>";
echo "<p><em>Gestão completa de devoluções de EPIs</em></p>";
echo "</div>";

echo "</div>";

// 6. Instruções de teste
echo "<h3>6. 🧪 Como Testar:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 5px solid #ffc107;'>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> <a href='gerenciar-usuarios.php' target='_blank' style='color: #007cba; font-weight: bold;'>gerenciar-usuarios.php</a></li>";
echo "<li><strong>Clique</strong> no botão 'Gerenciar Acessos' de qualquer usuário</li>";
echo "<li><strong>Procure pelas novas seções:</strong>";
echo "<ul>";
echo "<li>📊 <strong>Limites de Gastos</strong> (com 3 sub-itens)</li>";
echo "<li>🔄 <strong>Devolução</strong> (com 3 sub-itens)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Marque/desmarque</strong> as permissões conforme necessário</li>";
echo "<li><strong>Clique 'Salvar'</strong> para aplicar as alterações</li>";
echo "</ol>";
echo "</div>";

echo "<h3>7. 🔗 Links Úteis:</h3>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='gerenciar-usuarios.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px;'>👥 Gerenciar Usuários</a>";
echo "<a href='obter-acessos-usuario.php?usuario_id=1' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px;'>🔧 API de Acessos</a>";
echo "<a href='verificar-tabela-acessos.php' target='_blank' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px;'>🗄️ Verificar Tabela</a>";
echo "</div>";

$conn->close();
?>

<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Verificar se o ID foi fornecido
if (!isset($_GET['id'])) {
    echo "ID da saída não fornecido";
    exit;
}

$saida_id = intval($_GET['id']);

// Obter informações da saída
$stmt = $conn->prepare("
    SELECT se.*, e.nome_empresa 
    FROM saidas_estoque se 
    LEFT JOIN empresas e ON se.empresa_destino = e.codigo_empresa 
    WHERE se.id = ?
");
$stmt->bind_param("i", $saida_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "Saída não encontrada";
    exit;
}

$saida = $result->fetch_assoc();

// Obter produtos da saída
$stmt_produtos = $conn->prepare("SELECT * FROM produtos_saida WHERE saida_id = ?");
$stmt_produtos->bind_param("i", $saida_id);
$stmt_produtos->execute();
$result_produtos = $stmt_produtos->get_result();
$produtos = [];
while ($produto = $result_produtos->fetch_assoc()) {
    $produtos[] = $produto;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Saída #<?= $saida['id'] ?></title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .header h1 {
            color: #111827;
            font-size: 24px;
            margin: 0;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        
        .info-item label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            display: block;
            margin-bottom: 5px;
        }
        
        .info-item span {
            color: #111827;
            font-size: 16px;
        }
        
        .produtos-section {
            margin-top: 30px;
        }
        
        .produtos-section h2 {
            color: #111827;
            font-size: 20px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .produtos-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .produtos-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .produtos-table td {
            padding: 15px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
        }
        
        .produtos-table tr:hover {
            background: #f9fafb;
        }
        
        .btn-voltar {
            background: #6b7280;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
            transition: background 0.2s;
        }
        
        .btn-voltar:hover {
            background: #374151;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-ativa {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-cancelada {
            background: #fef2f2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Detalhes da Saída de Estoque #<?= $saida['id'] ?></h1>
        </div>
        
        <div class="info-grid">
            <div class="info-item">
                <label>Responsável:</label>
                <span><?= htmlspecialchars($saida['responsavel']) ?></span>
            </div>
            
            <div class="info-item">
                <label>Data da Saída:</label>
                <span><?= date('d/m/Y', strtotime($saida['data_saida'])) ?></span>
            </div>
            
            <div class="info-item">
                <label>Empresa Destino:</label>
                <span><?= htmlspecialchars($saida['nome_empresa'] ?? 'Código: ' . $saida['empresa_destino']) ?></span>
            </div>
            
            <div class="info-item">
                <label>Destinatário:</label>
                <span><?= htmlspecialchars($saida['destinatario'] ?? 'Não informado') ?></span>
            </div>
            
            <div class="info-item">
                <label>Setor Destinatário:</label>
                <span><?= htmlspecialchars($saida['setor_destinatario'] ?? 'Não informado') ?></span>
            </div>
            
            <div class="info-item">
                <label>Motivo:</label>
                <span><?= htmlspecialchars($saida['motivo']) ?></span>
            </div>
            
            <div class="info-item">
                <label>Status:</label>
                <span class="status-badge <?= $saida['status'] === 'ativa' ? 'status-ativa' : 'status-cancelada' ?>">
                    <?= ucfirst($saida['status'] ?? 'ativa') ?>
                </span>
            </div>
            
            <div class="info-item">
                <label>Data de Registro:</label>
                <span><?= date('d/m/Y H:i', strtotime($saida['data_registro'])) ?></span>
            </div>
        </div>
        
        <?php if (!empty($saida['observacoes'])): ?>
        <div class="info-item" style="grid-column: 1 / -1;">
            <label>Observações:</label>
            <span><?= htmlspecialchars($saida['observacoes']) ?></span>
        </div>
        <?php endif; ?>
        
        <div class="produtos-section">
            <h2>Produtos da Saída</h2>
            
            <?php if (count($produtos) > 0): ?>
            <table class="produtos-table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome do Produto</th>
                        <th>Quantidade</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($produtos as $produto): ?>
                    <tr>
                        <td><?= htmlspecialchars($produto['codigo']) ?></td>
                        <td><?= htmlspecialchars($produto['nome']) ?></td>
                        <td><?= $produto['quantidade'] ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <p style="text-align: center; color: #6b7280; padding: 20px;">Nenhum produto encontrado para esta saída.</p>
            <?php endif; ?>
        </div>
        
        <button onclick="window.close()" class="btn-voltar">Fechar</button>
    </div>
</body>
</html>

<?php
include 'conexao.php';

echo "<h2>Teste Final - Aviso de Pedido Urgente</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('TESTE FINAL AVISO', '001', 'FINAL-AVISO', 'Teste final do aviso urgente', '1', 'Teste', 'TI', 'Requisição para teste final do aviso', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar item
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'FINAL-AVISO', 1)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    $stmt->execute();
    
    echo "<div style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎯 Teste Final Configurado!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Status:</strong> Urgente (requisicao_urgente = 1)</p>";
    echo "<p><strong>Deve aparecer:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Fundo vermelho claro na linha da tabela</li>";
    echo "<li>✅ Aviso '🚨 Pedido urgente após valor limite excedido' no popup</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
    echo "<h4>📋 Passos para testar:</h4>";
    echo "<ol>";
    echo "<li>Ir para 'Solicitações Realizadas'</li>";
    echo "<li>Localizar a linha com fundo vermelho claro (código $codigo_requisicao)</li>";
    echo "<li>Clicar no botão 'Informações' dessa linha</li>";
    echo "<li><strong>Verificar:</strong> Popup abre com título 'Detalhes da Solicitação'</li>";
    echo "<li><strong>Verificar:</strong> Logo abaixo do título aparece o aviso vermelho</li>";
    echo "<li><strong>Verificar:</strong> Aviso contém '🚨 Pedido urgente após valor limite excedido'</li>";
    echo "</ol>";
    echo "</div>";
    
    // Testar endpoint diretamente
    echo "<h3>Verificação do Endpoint:</h3>";
    $stmt = $conn->prepare("SELECT r.*, e.nome_empresa FROM requisicoes r LEFT JOIN empresas e ON r.empresa = e.codigo_empresa WHERE r.codigo_solicitacao = ?");
    $stmt->bind_param("i", $codigo_requisicao);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $requisicao = $result->fetch_assoc();
        echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>requisicao_urgente:</strong> " . $requisicao['requisicao_urgente'] . "</p>";
        echo "<p><strong>motivo_urgente:</strong> " . $requisicao['motivo_urgente'] . "</p>";
        echo "<p><strong>Condição JavaScript:</strong> data.requisicao.requisicao_urgente == 1 → " . ($requisicao['requisicao_urgente'] == 1 ? 'TRUE' : 'FALSE') . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p style='margin-top: 30px;'>";
echo "<a href='requisicoes-feitas.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🚀 Testar em Requisições Feitas</a>";
echo "</p>";

$conn->close();
?>

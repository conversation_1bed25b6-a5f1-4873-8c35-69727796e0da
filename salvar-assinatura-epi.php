<?php
require_once 'conexao.php';

// Habilitar relatório de erros
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Verificar se a requisição é do tipo POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

// Obter dados do corpo da requisição
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!isset($data['id']) || !isset($data['assinatura'])) {
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$id = $data['id'];
$assinatura = $data['assinatura'];

// Verificar se o ID é válido
if (!is_numeric($id) || $id <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID inválido']);
    exit;
}

try {
    // Verificar se a coluna data_assinatura existe na tabela pessoa_epi
    $check_column = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'data_assinatura'");
    
    // Se a coluna não existir, adicioná-la
    if ($check_column->num_rows === 0) {
        $conn->query("ALTER TABLE pessoa_epi ADD COLUMN data_assinatura DATETIME");
    }
    
    // Atualizar o registro na tabela pessoa_epi
    $sql = "UPDATE pessoa_epi SET assinatura = ?, data_assinatura = NOW() WHERE id = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        throw new Exception("Erro ao preparar a consulta: " . $conn->error);
    }
    
    $stmt->bind_param("si", $assinatura, $id);
    
    if (!$stmt->execute()) {
        throw new Exception("Erro ao executar a consulta: " . $stmt->error);
    }
    
    // Verificar se alguma linha foi afetada
    if ($stmt->affected_rows === 0) {
        // Verificar se o registro existe
        $check = $conn->query("SELECT id FROM pessoa_epi WHERE id = $id");
        if ($check->num_rows === 0) {
            throw new Exception("Registro não encontrado com o ID: $id");
        }
    }
    
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    $conn->close();
}
?>


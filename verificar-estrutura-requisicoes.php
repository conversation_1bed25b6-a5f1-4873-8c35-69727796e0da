<?php
include 'conexao.php';

echo "<h2>Estrutura da tabela requisicoes:</h2>";
$result = $conn->query("DESCRIBE requisicoes");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Erro ao consultar estrutura: " . $conn->error;
}

echo "<h2>Verificar se existem requisições urgentes:</h2>";
$result = $conn->query("SELECT codigo_solicitacao, solicitante, requisicao_urgente, pedido_urgente, motivo_urgente FROM requisicoes LIMIT 10");
if ($result) {
    echo "<table border='1'>";
    echo "<tr><th>Código</th><th>Solicitante</th><th>requisicao_urgente</th><th>pedido_urgente</th><th>motivo_urgente</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['codigo_solicitacao'] . "</td>";
        echo "<td>" . $row['solicitante'] . "</td>";
        echo "<td>" . ($row['requisicao_urgente'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['pedido_urgente'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['motivo_urgente'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Erro ao consultar dados: " . $conn->error;
}

$conn->close();
?>

<?php
include 'conexao.php';

// Verificar se a tabela usuarios existe
$result = $conn->query("SHOW TABLES LIKE 'usuarios'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome_usuario VARCHAR(100) NOT NULL UNIQUE,
        senha VARCHAR(255) NOT NULL,
        tipo_usuario ENUM('administrador', 'supervisor', 'almoxarife') NOT NULL,
        data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
        status ENUM('ativo', 'inativo') DEFAULT 'ativo'
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela usuarios criada com sucesso!<br>";
        
        // Criar usuário administrador padrão
        $senha_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_admin = "INSERT INTO usuarios (nome_usuario, senha, tipo_usuario) VALUES ('admin', '$senha_hash', 'administrador')";
        
        if ($conn->query($sql_admin) === TRUE) {
            echo "Usuário administrador padrão criado com sucesso!<br>";
            echo "Login: admin<br>";
            echo "Senha: admin123<br>";
        } else {
            echo "Erro ao criar usuário administrador: " . $conn->error . "<br>";
        }
    } else {
        echo "Erro ao criar tabela usuarios: " . $conn->error . "<br>";
    }
} else {
    echo "Tabela usuarios já existe!<br>";
}

$conn->close();
?> 
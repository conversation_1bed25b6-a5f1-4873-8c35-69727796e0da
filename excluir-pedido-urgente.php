<?php
session_start();
include 'conexao.php';

header('Content-Type: application/json');

if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Não autenticado']);
    exit;
}

$tipo = $_POST['tipo'] ?? '';
$codigo = $_POST['codigo'] ?? '';

if (empty($tipo) || empty($codigo)) {
    echo json_encode(['success' => false, 'message' => 'Tipo e código são obrigatórios']);
    exit;
}

try {
    // Verificar se é realmente um pedido urgente antes de excluir
    $tabela = '';
    $campo_codigo = '';
    
    switch ($tipo) {
        case 'requisicao':
            $tabela = 'requisicoes';
            $campo_codigo = 'codigo_solicitacao';
            break;
        case 'pedido_mensal':
            $tabela = 'pedidos_mensais';
            $campo_codigo = 'codigo_pedido';
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Tipo de solicitação inválido']);
            exit;
    }
    
    // Verificar se existe e se é pedido urgente
    $campo_urgente = ($tipo === 'requisicao') ? 'requisicao_urgente' : 'pedido_urgente';
    $stmt = $conn->prepare("SELECT $campo_urgente FROM $tabela WHERE $campo_codigo = ?");
    $stmt->bind_param("i", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Solicitação não encontrada']);
        exit;
    }

    $solicitacao = $result->fetch_assoc();

    if ($solicitacao[$campo_urgente] != 1) {
        echo json_encode(['success' => false, 'message' => 'Apenas pedidos urgentes podem ser excluídos']);
        exit;
    }
    
    // Primeiro, excluir itens relacionados (devido à restrição de chave estrangeira)
    $tabela_itens = '';
    $campo_referencia = '';

    switch ($tipo) {
        case 'requisicao':
            $tabela_itens = 'itens_solicitacao';
            $campo_referencia = 'codigo_solicitacao';
            break;
        case 'pedido_mensal':
            $tabela_itens = 'itens_pedido_mensal';
            $campo_referencia = 'codigo_pedido';
            break;
    }

    if ($tabela_itens) {
        // Verificar se a tabela de itens existe
        $result_table = $conn->query("SHOW TABLES LIKE '$tabela_itens'");
        if ($result_table->num_rows > 0) {
            $stmt_itens = $conn->prepare("DELETE FROM $tabela_itens WHERE $campo_referencia = ?");
            $stmt_itens->bind_param("i", $codigo);
            $stmt_itens->execute();
        }
    }

    // Agora excluir a solicitação principal
    $stmt_delete = $conn->prepare("DELETE FROM $tabela WHERE $campo_codigo = ?");
    $stmt_delete->bind_param("i", $codigo);

    if ($stmt_delete->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'Pedido urgente excluído com sucesso'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao excluir pedido: ' . $conn->error]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}

$conn->close();
?>

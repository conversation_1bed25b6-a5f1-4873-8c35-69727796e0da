<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

require_once 'conexao.php';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devolução de EPIs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #111;
            min-height: 100vh;
        }

        .content-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2rem;
            font-weight: 600;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #007cba;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            min-width: 120px;
        }

        .form-group input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007cba;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
        }

        .btn-lupa {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-lupa:hover {
            background: #005a8b;
        }

        .produtos-section {
            display: none;
            background: #fff;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            padding: 25px;
            margin-top: 20px;
        }

        .produtos-section.show {
            display: block;
        }

        .produtos-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .produtos-table th,
        .produtos-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }

        .produtos-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .produtos-table tr:hover {
            background: #f8f9fa;
        }

        .input-devolucao {
            width: 80px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }

        .select-estado {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 180px;
        }

        .btn-finalizar {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: background-color 0.3s ease;
        }

        .btn-finalizar:hover {
            background: #218838;
        }

        .btn-finalizar:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        /* Popup de seleção de pessoa */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .popup {
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e8ed;
        }

        .popup-header h2 {
            margin: 0;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
        }

        .close-btn:hover {
            color: #dc3545;
        }

        .pessoas-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .pessoas-table th,
        .pessoas-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e1e8ed;
        }

        .pessoas-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .pessoas-table tr:hover {
            background: #f8f9fa;
        }

        .btn-selecionar {
            background: #007cba;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-selecionar:hover {
            background: #005a8b;
        }

        .pessoa-selecionada {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .pessoa-selecionada h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }

        .pessoa-info {
            color: #6c757d;
            font-size: 14px;
        }

        /* Sistema de abas */
        .tabs-container {
            display: flex;
            gap: 0;
            margin-bottom: 32px;
            border-bottom: 2px solid #e5e7eb;
        }

        .tab-btn {
            background: none;
            border: none;
            outline: none;
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            padding: 14px 32px 12px 32px;
            cursor: pointer;
            border-radius: 12px 12px 0 0;
            transition: background 0.2s, color 0.2s;
            margin-right: 2px;
        }

        .tab-btn.active {
            background: #fff !important;
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Estilos para devolução rápida */
        .produto-linha {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }

        .campo-codigo {
            min-width: 100px;
        }

        .campo-nome {
            flex: 1;
        }

        .campo-quantidade {
            min-width: 100px;
        }

        .campo-estado {
            min-width: 200px;
        }

        .btn-remover {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-remover:hover {
            background: #c82333;
        }

        .btn-adicionar-produto {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
        }

        .btn-adicionar-produto:hover {
            background: #218838;
        }

        /* Estilos para histórico */
        .historico-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .historico-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .historico-data {
            font-weight: bold;
            color: #007cba;
        }

        .historico-tipo {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .tipo-funcionario {
            background: #d4edda;
            color: #155724;
        }

        .tipo-rapida {
            background: #d1ecf1;
            color: #0c5460;
        }

        /* Estilos para detalhes */
        .detalhes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .detalhes-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }

        .detalhes-section h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }

        /* Estilos para assinatura */
        #canvas-assinatura {
            border-radius: 4px;
        }

        .btn-imprimir {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-imprimir:hover {
            background: #138496;
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    
    <div class="content-container">
        <h1>Devolução de EPIs</h1>

        <!-- Sistema de abas -->
        <div class="tabs-container">
            <button class="tab-btn active" id="tab-funcionario" onclick="mostrarAba('funcionario')">Devolução por Funcionário</button>
            <button class="tab-btn" id="tab-rapida" onclick="mostrarAba('rapida')">Devolução Rápida</button>
            <button class="tab-btn" id="tab-historico" onclick="mostrarAba('historico')">Histórico de Devoluções</button>
        </div>

        <!-- Aba 1: Devolução por Funcionário -->
        <div class="tab-content active" id="conteudo-funcionario">
            <div class="form-section">
            <div class="form-group">
                <label for="funcionario">Funcionário:</label>
                <input type="text" id="funcionario" name="funcionario" placeholder="Digite o nome do funcionário" readonly>
                <button type="button" class="btn-lupa" onclick="abrirPopupPessoas()">
                    🔍
                </button>
            </div>
        </div>

        <div id="pessoa-selecionada-info" class="pessoa-selecionada" style="display: none;">
            <h3 id="pessoa-nome"></h3>
            <div class="pessoa-info">
                <span id="pessoa-detalhes"></span>
            </div>
        </div>

        <div id="produtos-section" class="produtos-section">
            <h2>Produtos Vinculados ao Funcionário</h2>
            <table class="produtos-table" id="produtos-table">
                <thead>
                    <tr>
                        <th>Produto</th>
                        <th>Quantidade Recebida</th>
                        <th>Quantidade a Devolver</th>
                        <th>Estado do Produto</th>
                    </tr>
                </thead>
                <tbody id="produtos-tbody">
                </tbody>
            </table>
            
            <button type="button" class="btn-finalizar" id="btn-finalizar" onclick="finalizarDevolucao()" disabled>
                Finalizar Devolução
            </button>
        </div>
        </div>

        <!-- Aba 2: Devolução Rápida -->
        <div class="tab-content" id="conteudo-rapida">
            <div class="form-section">
                <h2>Devolução Rápida por Produto</h2>
                <p style="color: #6c757d; margin-bottom: 20px;">Adicione produtos diretamente pelo código ou seleção</p>

                <div id="produtos-rapida-container">
                    <!-- Primeira linha de produto -->
                    <div class="produto-linha" data-index="0">
                        <div class="campo-codigo">
                            <label>Código:</label>
                            <input type="text" class="form-control codigo-produto" placeholder="Código" onchange="buscarProdutoPorCodigo(this, 0)">
                        </div>
                        <div class="campo-nome">
                            <label>Produto:</label>
                            <input type="text" class="form-control nome-produto" placeholder="Nome do produto" readonly>
                        </div>
                        <div>
                            <label>&nbsp;</label>
                            <button type="button" class="btn-lupa" onclick="abrirPopupProdutos(0)">🔍</button>
                        </div>
                        <div class="campo-quantidade">
                            <label>Quantidade:</label>
                            <input type="number" class="form-control quantidade-produto" min="1" placeholder="Qtd">
                        </div>
                        <div class="campo-estado">
                            <label>Estado:</label>
                            <select class="form-control estado-produto">
                                <option value="">Selecione o estado</option>
                                <option value="novo">Novo / Sem Uso</option>
                                <option value="usado_bom">Usado – Bom Estado</option>
                                <option value="usado_leve">Usado – Desgaste leve</option>
                                <option value="danificado_reparo">Danificado – Reparo Possível</option>
                                <option value="danificado_irrecuperavel">Danificado – Irrecuperável</option>
                                <option value="vencido">Vencido / Fora da Validade</option>
                                <option value="descarte">Descarte</option>
                            </select>
                        </div>
                        <div>
                            <label>&nbsp;</label>
                            <button type="button" class="btn-remover" onclick="removerProdutoLinha(0)" style="display: none;">✕</button>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn-adicionar-produto" onclick="adicionarNovoProduto()">
                    + Adicionar Outro Produto
                </button>

                <button type="button" class="btn-finalizar" id="btn-finalizar-rapida" onclick="finalizarDevolucaoRapida()" style="margin-left: 15px;">
                    Finalizar Devolução Rápida
                </button>
            </div>
        </div>

        <!-- Aba 3: Histórico de Devoluções -->
        <div class="tab-content" id="conteudo-historico">
            <div class="form-section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>Histórico de Devoluções</h2>
                    <div>
                        <input type="text" id="search-historico" placeholder="Buscar por funcionário, produto..." style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                        <button type="button" class="btn-lupa" onclick="carregarHistorico()">🔄 Atualizar</button>
                    </div>
                </div>

                <div id="historico-container">
                    <table class="produtos-table" id="historico-table">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Tipo</th>
                                <th>Funcionário</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Estado</th>
                                <th>Usuário</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="historico-tbody">
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 20px; color: #6c757d;">
                                    Carregando histórico...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de seleção de pessoas -->
    <div id="popup-overlay" class="popup-overlay" onclick="fecharPopupPessoas()">
        <div class="popup" onclick="event.stopPropagation()">
            <div class="popup-header">
                <h2>Selecionar Funcionário</h2>
                <button class="close-btn" onclick="fecharPopupPessoas()">&times;</button>
            </div>
            
            <div style="margin-bottom: 15px;">
                <input type="text" id="search-pessoa" placeholder="Buscar por nome..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <table class="pessoas-table" id="pessoas-table">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Posto</th>
                        <th>Setor</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="pessoas-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Popup de seleção de produtos -->
    <div id="popup-produtos-overlay" class="popup-overlay" onclick="fecharPopupProdutos()">
        <div class="popup" onclick="event.stopPropagation()">
            <div class="popup-header">
                <h2>Selecionar Produto</h2>
                <button class="close-btn" onclick="fecharPopupProdutos()">&times;</button>
            </div>

            <div style="margin-bottom: 15px;">
                <input type="text" id="search-produto" placeholder="Buscar por nome ou código..." style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
            </div>

            <table class="pessoas-table" id="produtos-table">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome</th>
                        <th>Categoria</th>
                        <th>Quantidade</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="produtos-tbody">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Popup de detalhes do histórico -->
    <div id="popup-detalhes-overlay" class="popup-overlay" onclick="fecharPopupDetalhes()">
        <div class="popup" onclick="event.stopPropagation()" style="max-width: 800px; width: 90%;">
            <div class="popup-header">
                <h2>Detalhes da Devolução</h2>
                <div style="display: flex; gap: 10px;">
                    <button class="btn-imprimir" onclick="gerarPDF()" title="Gerar PDF">
                        📄 PDF
                    </button>
                    <button class="close-btn" onclick="fecharPopupDetalhes()">&times;</button>
                </div>
            </div>

            <div id="detalhes-content" style="padding: 20px;">
                <!-- Conteúdo será preenchido dinamicamente -->
            </div>

            <!-- Campo de assinatura -->
            <div style="padding: 20px; border-top: 1px solid #ddd; background: #f8f9fa;">
                <h4>Assinatura de Devolução</h4>
                <div style="display: flex; gap: 15px; align-items: center; margin-top: 10px;">
                    <canvas id="canvas-assinatura" width="400" height="150" style="border: 1px solid #ddd; background: white; cursor: crosshair;"></canvas>
                    <div>
                        <button type="button" class="btn-limpar" onclick="limparAssinatura()" style="margin-bottom: 10px;">
                            Limpar Assinatura
                        </button>
                        <br>
                        <button type="button" class="btn-finalizar" onclick="salvarAssinatura()">
                            Salvar Assinatura
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let pessoaSelecionada = null;
        let produtosVinculados = [];
        let produtosDisponiveis = [];
        let linhaAtualProduto = 0;

        // Sistema de abas
        function mostrarAba(aba) {
            // Remover classe active de todas as abas
            document.getElementById('tab-funcionario').classList.remove('active');
            document.getElementById('tab-rapida').classList.remove('active');
            document.getElementById('tab-historico').classList.remove('active');
            document.getElementById('conteudo-funcionario').classList.remove('active');
            document.getElementById('conteudo-rapida').classList.remove('active');
            document.getElementById('conteudo-historico').classList.remove('active');

            // Adicionar classe active na aba selecionada
            if (aba === 'funcionario') {
                document.getElementById('tab-funcionario').classList.add('active');
                document.getElementById('conteudo-funcionario').classList.add('active');
            } else if (aba === 'rapida') {
                document.getElementById('tab-rapida').classList.add('active');
                document.getElementById('conteudo-rapida').classList.add('active');
                // Carregar produtos se ainda não carregou
                if (produtosDisponiveis.length === 0) {
                    carregarProdutosDisponiveis();
                }
            } else if (aba === 'historico') {
                document.getElementById('tab-historico').classList.add('active');
                document.getElementById('conteudo-historico').classList.add('active');
                // Carregar histórico
                carregarHistorico();
            }
        }

        // Abrir popup de pessoas
        function abrirPopupPessoas() {
            document.getElementById('popup-overlay').style.display = 'flex';
            carregarPessoas();
        }

        // Fechar popup de pessoas
        function fecharPopupPessoas() {
            document.getElementById('popup-overlay').style.display = 'none';
        }

        // Carregar lista de pessoas
        function carregarPessoas() {
            fetch('obter-pessoas.php')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('pessoas-tbody');
                    tbody.innerHTML = '';
                    
                    data.forEach(pessoa => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `
                            <td>${pessoa.nome}</td>
                            <td>${pessoa.posto || '-'}</td>
                            <td>${pessoa.setor || '-'}</td>
                            <td>
                                <button class="btn-selecionar" onclick="selecionarPessoa(${pessoa.id}, '${pessoa.nome}', '${pessoa.posto || ''}', '${pessoa.setor || ''}')">
                                    Selecionar
                                </button>
                            </td>
                        `;
                        tbody.appendChild(tr);
                    });
                })
                .catch(error => {
                    console.error('Erro ao carregar pessoas:', error);
                    alert('Erro ao carregar lista de pessoas');
                });
        }

        // Selecionar pessoa
        function selecionarPessoa(id, nome, posto, setor) {
            pessoaSelecionada = { id, nome, posto, setor };
            
            // Atualizar campo de funcionário
            document.getElementById('funcionario').value = nome;
            
            // Mostrar informações da pessoa selecionada
            document.getElementById('pessoa-nome').textContent = nome;
            document.getElementById('pessoa-detalhes').textContent = `Posto: ${posto || 'N/A'} | Setor: ${setor || 'N/A'}`;
            document.getElementById('pessoa-selecionada-info').style.display = 'block';
            
            // Carregar produtos vinculados
            carregarProdutosVinculados(id);
            
            // Fechar popup
            fecharPopupPessoas();
        }

        // Carregar produtos vinculados à pessoa
        function carregarProdutosVinculados(pessoaId) {
            console.log('Carregando produtos para pessoa ID:', pessoaId);

            fetch(`obter-produtos-pessoa.php?pessoa_id=${pessoaId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Produtos recebidos:', data);
                    produtosVinculados = data;
                    renderizarTabelaProdutos();
                    document.getElementById('produtos-section').classList.add('show');
                    document.getElementById('btn-finalizar').disabled = data.length === 0;

                    if (data.length === 0) {
                        alert('Nenhum produto encontrado para este funcionário ou todos já foram devolvidos.');
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar produtos:', error);
                    alert('Erro ao carregar produtos vinculados: ' + error.message);
                });
        }

        // Renderizar tabela de produtos
        function renderizarTabelaProdutos() {
            const tbody = document.getElementById('produtos-tbody');
            tbody.innerHTML = '';
            
            produtosVinculados.forEach((produto, index) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${produto.nome}</td>
                    <td>${produto.quantidade}</td>
                    <td>
                        <input type="number" class="input-devolucao" min="0" max="${produto.quantidade}" 
                               value="0" onchange="validarQuantidade(this, ${produto.quantidade})" 
                               data-index="${index}">
                    </td>
                    <td>
                        <select class="select-estado" data-index="${index}">
                            <option value="">Selecione o estado</option>
                            <option value="novo">Novo / Sem Uso</option>
                            <option value="usado_bom">Usado – Bom Estado</option>
                            <option value="usado_leve">Usado – Desgaste leve</option>
                            <option value="danificado_reparo">Danificado – Reparo Possível</option>
                            <option value="danificado_irrecuperavel">Danificado – Irrecuperável</option>
                            <option value="vencido">Vencido / Fora da Validade</option>
                            <option value="descarte">Descarte</option>
                        </select>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // Validar quantidade de devolução
        function validarQuantidade(input, maxQuantidade) {
            const valor = parseInt(input.value) || 0;
            if (valor > maxQuantidade) {
                input.value = maxQuantidade;
                alert(`Quantidade máxima disponível: ${maxQuantidade}`);
            }
            if (valor < 0) {
                input.value = 0;
            }
        }

        // Finalizar devolução
        function finalizarDevolucao() {
            if (!pessoaSelecionada) {
                alert('Selecione um funcionário primeiro');
                return;
            }

            // Coletar dados de devolução
            const devolucoes = [];
            const inputs = document.querySelectorAll('.input-devolucao');
            const selects = document.querySelectorAll('.select-estado');
            
            let temDevolucao = false;
            
            inputs.forEach((input, index) => {
                const quantidade = parseInt(input.value) || 0;
                const estado = selects[index].value;
                
                if (quantidade > 0) {
                    if (!estado) {
                        alert('Selecione o estado do produto para todos os itens com quantidade de devolução');
                        return;
                    }
                    
                    devolucoes.push({
                        produto_id: produtosVinculados[index].produto_id,
                        produto_nome: produtosVinculados[index].nome,
                        quantidade: quantidade,
                        estado: estado
                    });
                    
                    temDevolucao = true;
                }
            });
            
            if (!temDevolucao) {
                alert('Informe pelo menos um item para devolução');
                return;
            }
            
            // Confirmar devolução
            if (confirm(`Confirma a devolução de ${devolucoes.length} item(ns) do funcionário ${pessoaSelecionada.nome}?`)) {
                processarDevolucao(devolucoes);
            }
        }

        // Processar devolução
        function processarDevolucao(devolucoes) {
            const dados = {
                pessoa_id: pessoaSelecionada.id,
                pessoa_nome: pessoaSelecionada.nome,
                devolucoes: devolucoes
            };
            
            fetch('processar-devolucao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dados)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Devolução processada com sucesso!');
                    // Limpar formulário
                    limparFormulario();
                } else {
                    alert('Erro ao processar devolução: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao processar devolução');
            });
        }

        // Limpar formulário
        function limparFormulario() {
            pessoaSelecionada = null;
            produtosVinculados = [];
            document.getElementById('funcionario').value = '';
            document.getElementById('pessoa-selecionada-info').style.display = 'none';
            document.getElementById('produtos-section').classList.remove('show');
            document.getElementById('btn-finalizar').disabled = true;
        }

        // ===== FUNÇÕES PARA DEVOLUÇÃO RÁPIDA =====

        // Carregar produtos disponíveis
        function carregarProdutosDisponiveis() {
            fetch('obter-todos-produtos.php')
                .then(response => response.json())
                .then(data => {
                    produtosDisponiveis = data;
                })
                .catch(error => {
                    console.error('Erro ao carregar produtos:', error);
                });
        }

        // Abrir popup de produtos
        function abrirPopupProdutos(index) {
            linhaAtualProduto = index;
            document.getElementById('popup-produtos-overlay').style.display = 'flex';
            renderizarTabelaProdutosPopup();
        }

        // Fechar popup de produtos
        function fecharPopupProdutos() {
            document.getElementById('popup-produtos-overlay').style.display = 'none';
        }

        // Renderizar tabela de produtos no popup
        function renderizarTabelaProdutosPopup() {
            const tbody = document.getElementById('produtos-tbody');
            tbody.innerHTML = '';

            produtosDisponiveis.forEach(produto => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${produto.codigo}</td>
                    <td>${produto.nome}</td>
                    <td>${produto.categoria || '-'}</td>
                    <td>${produto.quantidade}</td>
                    <td>
                        <button class="btn-selecionar" onclick="selecionarProduto('${produto.codigo}', '${produto.nome}')">
                            Selecionar
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // Selecionar produto
        function selecionarProduto(codigo, nome) {
            const linha = document.querySelector(`[data-index="${linhaAtualProduto}"]`);
            linha.querySelector('.codigo-produto').value = codigo;
            linha.querySelector('.nome-produto').value = nome;
            fecharPopupProdutos();

            // Mostrar botão remover se não for a primeira linha
            if (linhaAtualProduto > 0) {
                linha.querySelector('.btn-remover').style.display = 'block';
            }
        }

        // Buscar produto por código
        function buscarProdutoPorCodigo(input, index) {
            const codigo = input.value;
            if (!codigo) return;

            const produto = produtosDisponiveis.find(p => p.codigo === codigo);
            const linha = document.querySelector(`[data-index="${index}"]`);

            if (produto) {
                linha.querySelector('.nome-produto').value = produto.nome;
                // Mostrar botão remover se não for a primeira linha
                if (index > 0) {
                    linha.querySelector('.btn-remover').style.display = 'block';
                }
            } else {
                linha.querySelector('.nome-produto').value = '';
                alert('Produto não encontrado');
            }
        }

        // Adicionar nova linha de produto
        function adicionarNovoProduto() {
            const container = document.getElementById('produtos-rapida-container');
            const novoIndex = container.children.length;

            const novaLinha = document.createElement('div');
            novaLinha.className = 'produto-linha';
            novaLinha.setAttribute('data-index', novoIndex);

            novaLinha.innerHTML = `
                <div class="campo-codigo">
                    <label>Código:</label>
                    <input type="text" class="form-control codigo-produto" placeholder="Código" onchange="buscarProdutoPorCodigo(this, ${novoIndex})">
                </div>
                <div class="campo-nome">
                    <label>Produto:</label>
                    <input type="text" class="form-control nome-produto" placeholder="Nome do produto" readonly>
                </div>
                <div>
                    <label>&nbsp;</label>
                    <button type="button" class="btn-lupa" onclick="abrirPopupProdutos(${novoIndex})">🔍</button>
                </div>
                <div class="campo-quantidade">
                    <label>Quantidade:</label>
                    <input type="number" class="form-control quantidade-produto" min="1" placeholder="Qtd">
                </div>
                <div class="campo-estado">
                    <label>Estado:</label>
                    <select class="form-control estado-produto">
                        <option value="">Selecione o estado</option>
                        <option value="novo">Novo / Sem Uso</option>
                        <option value="usado_bom">Usado – Bom Estado</option>
                        <option value="usado_leve">Usado – Desgaste leve</option>
                        <option value="danificado_reparo">Danificado – Reparo Possível</option>
                        <option value="danificado_irrecuperavel">Danificado – Irrecuperável</option>
                        <option value="vencido">Vencido / Fora da Validade</option>
                        <option value="descarte">Descarte</option>
                    </select>
                </div>
                <div>
                    <label>&nbsp;</label>
                    <button type="button" class="btn-remover" onclick="removerProdutoLinha(${novoIndex})">✕</button>
                </div>
            `;

            container.appendChild(novaLinha);
        }

        // Remover linha de produto
        function removerProdutoLinha(index) {
            const linha = document.querySelector(`[data-index="${index}"]`);
            if (linha) {
                linha.remove();
                // Reindexar as linhas restantes
                reindexarLinhas();
            }
        }

        // Reindexar linhas após remoção
        function reindexarLinhas() {
            const linhas = document.querySelectorAll('.produto-linha');
            linhas.forEach((linha, index) => {
                linha.setAttribute('data-index', index);
                // Atualizar eventos onclick
                const btnLupa = linha.querySelector('.btn-lupa');
                const btnRemover = linha.querySelector('.btn-remover');
                const inputCodigo = linha.querySelector('.codigo-produto');

                btnLupa.setAttribute('onclick', `abrirPopupProdutos(${index})`);
                btnRemover.setAttribute('onclick', `removerProdutoLinha(${index})`);
                inputCodigo.setAttribute('onchange', `buscarProdutoPorCodigo(this, ${index})`);

                // Primeira linha não tem botão remover
                if (index === 0) {
                    btnRemover.style.display = 'none';
                }
            });
        }

        // Finalizar devolução rápida
        function finalizarDevolucaoRapida() {
            const linhas = document.querySelectorAll('.produto-linha');
            const devolucoes = [];

            let temDevolucao = false;

            linhas.forEach((linha, index) => {
                const codigo = linha.querySelector('.codigo-produto').value;
                const nome = linha.querySelector('.nome-produto').value;
                const quantidade = parseInt(linha.querySelector('.quantidade-produto').value) || 0;
                const estado = linha.querySelector('.estado-produto').value;

                if (codigo && nome && quantidade > 0 && estado) {
                    devolucoes.push({
                        produto_id: codigo,
                        produto_nome: nome,
                        quantidade: quantidade,
                        estado: estado
                    });
                    temDevolucao = true;
                } else if (codigo || nome || quantidade > 0) {
                    alert(`Linha ${index + 1}: Preencha todos os campos (código, nome, quantidade e estado)`);
                    return;
                }
            });

            if (!temDevolucao) {
                alert('Adicione pelo menos um produto para devolução');
                return;
            }

            if (confirm(`Confirma a devolução rápida de ${devolucoes.length} produto(s)?`)) {
                processarDevolucaoRapida(devolucoes);
            }
        }

        // Processar devolução rápida
        function processarDevolucaoRapida(devolucoes) {
            const dados = {
                tipo: 'rapida',
                devolucoes: devolucoes
            };

            fetch('processar-devolucao-rapida.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dados)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Devolução rápida processada com sucesso!');
                    limparFormularioRapido();
                } else {
                    alert('Erro ao processar devolução rápida: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao processar devolução rápida');
            });
        }

        // Limpar formulário rápido
        function limparFormularioRapido() {
            const container = document.getElementById('produtos-rapida-container');
            // Manter apenas a primeira linha
            const primeiraLinha = container.querySelector('[data-index="0"]');
            container.innerHTML = '';
            container.appendChild(primeiraLinha);

            // Limpar campos da primeira linha
            primeiraLinha.querySelector('.codigo-produto').value = '';
            primeiraLinha.querySelector('.nome-produto').value = '';
            primeiraLinha.querySelector('.quantidade-produto').value = '';
            primeiraLinha.querySelector('.estado-produto').value = '';
            primeiraLinha.querySelector('.btn-remover').style.display = 'none';
        }

        // ===== FUNÇÕES PARA HISTÓRICO DE DEVOLUÇÕES =====

        // Carregar histórico de devoluções
        function carregarHistorico() {
            fetch('obter-historico-devolucoes-final.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Histórico recebido:', data);
                    // Garantir que data é um array
                    if (Array.isArray(data)) {
                        renderizarHistorico(data);
                    } else {
                        console.error('Dados recebidos não são um array:', data);
                        renderizarHistorico([]);
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar histórico:', error);
                    document.getElementById('historico-tbody').innerHTML =
                        '<tr><td colspan="8" style="text-align: center; color: red;">Erro ao carregar histórico: ' + error.message + '</td></tr>';
                });
        }

        // Renderizar tabela de histórico
        function renderizarHistorico(historico) {
            const tbody = document.getElementById('historico-tbody');
            tbody.innerHTML = '';

            if (historico.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6c757d;">Nenhuma devolução encontrada</td></tr>';
                return;
            }

            historico.forEach(item => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${formatarData(item.data_devolucao)}</td>
                    <td><span class="historico-tipo tipo-${item.tipo}">${item.tipo === 'funcionario' ? 'Por Funcionário' : 'Rápida'}</span></td>
                    <td>${item.pessoa_nome || 'N/A'}</td>
                    <td>${item.produto_nome}</td>
                    <td>${item.quantidade}</td>
                    <td>${formatarEstado(item.estado)}</td>
                    <td>${item.usuario_nome || 'N/A'}</td>
                    <td>
                        <button class="btn-selecionar" onclick="mostrarDetalhes(${item.id}, '${item.tipo}')">
                            Detalhes
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // Mostrar detalhes da devolução
        function mostrarDetalhes(id, tipo) {
            fetch(`obter-detalhes-devolucao.php?id=${id}&tipo=${tipo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        preencherDetalhes(data.devolucao);
                        document.getElementById('popup-detalhes-overlay').style.display = 'flex';
                        inicializarAssinatura();
                    } else {
                        alert('Erro ao carregar detalhes: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar detalhes:', error);
                    alert('Erro ao carregar detalhes da devolução');
                });
        }

        // Preencher popup de detalhes
        function preencherDetalhes(devolucao) {
            const content = document.getElementById('detalhes-content');
            content.innerHTML = `
                <div class="detalhes-grid">
                    <div class="detalhes-section">
                        <h4>Informações Gerais</h4>
                        <p><strong>Data:</strong> ${formatarDataCompleta(devolucao.data_devolucao)}</p>
                        <p><strong>Tipo:</strong> ${devolucao.tipo === 'funcionario' ? 'Devolução por Funcionário' : 'Devolução Rápida'}</p>
                        <p><strong>Usuário:</strong> ${devolucao.usuario_nome || 'N/A'}</p>
                        ${devolucao.pessoa_nome ? `<p><strong>Funcionário:</strong> ${devolucao.pessoa_nome}</p>` : ''}
                    </div>
                    <div class="detalhes-section">
                        <h4>Produto Devolvido</h4>
                        <p><strong>Código:</strong> ${devolucao.produto_id}</p>
                        <p><strong>Nome:</strong> ${devolucao.produto_nome}</p>
                        <p><strong>Quantidade:</strong> ${devolucao.quantidade}</p>
                        <p><strong>Estado:</strong> ${formatarEstado(devolucao.estado)}</p>
                    </div>
                </div>
                ${devolucao.assinatura ? `
                    <div class="detalhes-section">
                        <h4>Assinatura Registrada</h4>
                        <img src="data:image/png;base64,${devolucao.assinatura}" style="max-width: 400px; border: 1px solid #ddd;">
                    </div>
                ` : ''}
            `;

            // Armazenar ID para uso posterior
            window.currentDevolucaoId = devolucao.id;
            window.currentDevolucaoTipo = devolucao.tipo;
        }

        // Fechar popup de detalhes
        function fecharPopupDetalhes() {
            document.getElementById('popup-detalhes-overlay').style.display = 'none';
        }

        // Formatadores
        function formatarData(data) {
            return new Date(data).toLocaleDateString('pt-BR');
        }

        function formatarDataCompleta(data) {
            return new Date(data).toLocaleString('pt-BR');
        }

        function formatarEstado(estado) {
            const estados = {
                'novo': 'Novo / Sem Uso',
                'usado_bom': 'Usado – Bom Estado',
                'usado_leve': 'Usado – Desgaste leve',
                'danificado_reparo': 'Danificado – Reparo Possível',
                'danificado_irrecuperavel': 'Danificado – Irrecuperável',
                'vencido': 'Vencido / Fora da Validade',
                'descarte': 'Descarte'
            };
            return estados[estado] || estado;
        }

        // ===== FUNÇÕES PARA ASSINATURA =====

        let canvas, ctx, isDrawing = false;

        // Inicializar canvas de assinatura
        function inicializarAssinatura() {
            canvas = document.getElementById('canvas-assinatura');
            ctx = canvas.getContext('2d');

            // Configurar canvas
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';

            // Eventos do mouse
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // Eventos touch para dispositivos móveis
            canvas.addEventListener('touchstart', handleTouch);
            canvas.addEventListener('touchmove', handleTouch);
            canvas.addEventListener('touchend', stopDrawing);
        }

        function startDrawing(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            ctx.beginPath();
            ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
        }

        function draw(e) {
            if (!isDrawing) return;
            const rect = canvas.getBoundingClientRect();
            ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
            ctx.stroke();
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        // Limpar assinatura
        function limparAssinatura() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Salvar assinatura
        function salvarAssinatura() {
            const assinaturaData = canvas.toDataURL('image/png');

            if (isCanvasEmpty()) {
                alert('Por favor, faça uma assinatura antes de salvar.');
                return;
            }

            const dados = {
                id: window.currentDevolucaoId,
                tipo: window.currentDevolucaoTipo,
                assinatura: assinaturaData.split(',')[1] // Remove o prefixo data:image/png;base64,
            };

            fetch('salvar-assinatura-devolucao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dados)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Assinatura salva com sucesso!');
                    carregarHistorico(); // Recarregar histórico
                } else {
                    alert('Erro ao salvar assinatura: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao salvar assinatura');
            });
        }

        // Verificar se canvas está vazio
        function isCanvasEmpty() {
            const blank = document.createElement('canvas');
            blank.width = canvas.width;
            blank.height = canvas.height;
            return canvas.toDataURL() === blank.toDataURL();
        }

        // Gerar PDF
        function gerarPDF() {
            const id = window.currentDevolucaoId;
            const tipo = window.currentDevolucaoTipo;

            if (!id || !tipo) {
                alert('Erro: Dados da devolução não encontrados');
                return;
            }

            // Abrir PDF em nova janela
            window.open(`gerar-pdf-devolucao.php?id=${id}&tipo=${tipo}`, '_blank');
        }

        // Busca de pessoas no popup
        document.getElementById('search-pessoa').addEventListener('input', function() {
            const termo = this.value.toLowerCase();
            const linhas = document.querySelectorAll('#pessoas-tbody tr');

            linhas.forEach(linha => {
                const nome = linha.cells[0].textContent.toLowerCase();
                const posto = linha.cells[1].textContent.toLowerCase();
                const setor = linha.cells[2].textContent.toLowerCase();

                if (nome.includes(termo) || posto.includes(termo) || setor.includes(termo)) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
        });

        // Busca de produtos no popup
        document.getElementById('search-produto').addEventListener('input', function() {
            const termo = this.value.toLowerCase();
            const linhas = document.querySelectorAll('#produtos-tbody tr');

            linhas.forEach(linha => {
                const codigo = linha.cells[0].textContent.toLowerCase();
                const nome = linha.cells[1].textContent.toLowerCase();
                const categoria = linha.cells[2].textContent.toLowerCase();

                if (codigo.includes(termo) || nome.includes(termo) || categoria.includes(termo)) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
        });

        // Busca no histórico
        document.getElementById('search-historico').addEventListener('input', function() {
            const termo = this.value.toLowerCase();
            const linhas = document.querySelectorAll('#historico-tbody tr');

            linhas.forEach(linha => {
                if (linha.cells.length < 8) return; // Pular linhas de mensagem

                const data = linha.cells[0].textContent.toLowerCase();
                const tipo = linha.cells[1].textContent.toLowerCase();
                const funcionario = linha.cells[2].textContent.toLowerCase();
                const produto = linha.cells[3].textContent.toLowerCase();
                const estado = linha.cells[5].textContent.toLowerCase();
                const usuario = linha.cells[6].textContent.toLowerCase();

                if (data.includes(termo) || tipo.includes(termo) || funcionario.includes(termo) ||
                    produto.includes(termo) || estado.includes(termo) || usuario.includes(termo)) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>

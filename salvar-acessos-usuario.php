<?php
session_start();
include 'conexao.php';
header('Content-Type: application/json');

if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'administrador') {
    http_response_code(403);
    echo json_encode(['erro' => 'Acesso negado']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$usuario_id = isset($input['usuario_id']) ? intval($input['usuario_id']) : 0;
$acessos = isset($input['acessos']) ? $input['acessos'] : [];
if (!$usuario_id) {
    echo json_encode(['erro' => 'Usuário inválido']);
    exit();
}
// Remove permissões antigas
$stmt = $conn->prepare('DELETE FROM usuarios_acessos WHERE usuario_id = ?');
$stmt->bind_param('i', $usuario_id);
$stmt->execute();
// Insere novas permissões
foreach ($acessos as $chave => $permitido) {
    $partes = explode('.', $chave);
    $menu = $partes[0];
    $item = isset($partes[1]) ? $partes[1] : null;
    $stmt2 = $conn->prepare('INSERT INTO usuarios_acessos (usuario_id, menu, item, permitido) VALUES (?, ?, ?, ?)');
    $stmt2->bind_param('issi', $usuario_id, $menu, $item, $permitido);
    $stmt2->execute();
}
echo json_encode(['ok' => true]);
$conn->close(); 
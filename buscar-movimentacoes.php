<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

include 'conexao.php';

// Receber dados JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['codigo']) || !isset($input['data_inicio']) || !isset($input['data_fim'])) {
    echo json_encode(['success' => false, 'message' => 'Dados inválidos']);
    exit();
}

$codigo = $input['codigo'];
$dataInicio = $input['data_inicio'];
$dataFim = $input['data_fim'];

try {
    $movimentacoes = [];
    
    // 1. ENTRADAS - da tabela produtos_entrada
    $sqlEntradas = "
        SELECT 
            pe.codigo,
            p.nome,
            ee.data_hora as data,
            pe.quantidade,
            'Entrada' as origem
        FROM produtos_entrada pe
        JOIN produtos p ON pe.codigo = p.codigo
        JOIN entradas_estoque ee ON pe.entrada_id = ee.id
        WHERE pe.codigo = ? 
        AND DATE(ee.data_hora) BETWEEN ? AND ?
        AND (ee.status IS NULL OR ee.status = 'ativa')
        ORDER BY ee.data_hora DESC
    ";
    
    $stmt = $conn->prepare($sqlEntradas);
    $stmt->bind_param('sss', $codigo, $dataInicio, $dataFim);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $movimentacoes[] = [
            'data' => date('d/m/Y H:i', strtotime($row['data'])),
            'codigo' => $row['codigo'],
            'nome' => $row['nome'],
            'origem' => $row['origem'],
            'quantidade' => $row['quantidade']
        ];
    }
    
    // 2. SAÍDAS DIRETAS - da tabela produtos_saida
    $sqlSaidas = "
        SELECT 
            ps.codigo,
            p.nome,
            se.data_saida as data,
            ps.quantidade,
            'Saída' as origem
        FROM produtos_saida ps
        JOIN produtos p ON ps.codigo = p.codigo
        JOIN saidas_estoque se ON ps.saida_id = se.id
        WHERE ps.codigo = ? 
        AND DATE(se.data_saida) BETWEEN ? AND ?
        AND (se.status IS NULL OR se.status = 'ativa')
        ORDER BY se.data_saida DESC
    ";
    
    $stmt = $conn->prepare($sqlSaidas);
    $stmt->bind_param('sss', $codigo, $dataInicio, $dataFim);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $movimentacoes[] = [
            'data' => date('d/m/Y H:i', strtotime($row['data'])),
            'codigo' => $row['codigo'],
            'nome' => $row['nome'],
            'origem' => $row['origem'],
            'quantidade' => $row['quantidade']
        ];
    }
    
    // 3. REQUISIÇÕES CONCLUÍDAS - da tabela itens_solicitacao
    $sqlRequisicoes = "
        SELECT 
            i.produto as codigo,
            p.nome,
            r.data_solicitacao as data,
            i.quantidade,
            'Saída' as origem
        FROM itens_solicitacao i
        JOIN produtos p ON i.produto = p.codigo
        JOIN requisicoes r ON i.codigo_solicitacao = r.codigo_solicitacao
        WHERE i.produto = ? 
        AND r.status = 'concluido'
        AND DATE(r.data_solicitacao) BETWEEN ? AND ?
        ORDER BY r.data_solicitacao DESC
    ";
    
    $stmt = $conn->prepare($sqlRequisicoes);
    $stmt->bind_param('sss', $codigo, $dataInicio, $dataFim);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $movimentacoes[] = [
            'data' => date('d/m/Y H:i', strtotime($row['data'])),
            'codigo' => $row['codigo'],
            'nome' => $row['nome'],
            'origem' => $row['origem'],
            'quantidade' => $row['quantidade']
        ];
    }
    
    // 4. PEDIDOS MENSAIS CONCLUÍDOS - da tabela itens_pedido_mensal
    // Primeiro, verificar se existe coluna de data na tabela pedidos_mensais
    $colunaData = 'NOW()';
    $checkData = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
    if ($checkData && $checkData->num_rows > 0) {
        $col = $checkData->fetch_assoc();
        $colunaData = $col['Field'];
    }
    
    $sqlPedidos = "
        SELECT 
            i.produto as codigo,
            p.nome,
            pm.$colunaData as data,
            i.quantidade,
            'Saída' as origem
        FROM itens_pedido_mensal i
        JOIN produtos p ON i.produto = p.codigo
        JOIN pedidos_mensais pm ON i.codigo_pedido = pm.codigo_pedido
        WHERE i.produto = ? 
        AND pm.status = 'concluido'
        AND DATE(pm.$colunaData) BETWEEN ? AND ?
        ORDER BY pm.$colunaData DESC
    ";
    
    $stmt = $conn->prepare($sqlPedidos);
    $stmt->bind_param('sss', $codigo, $dataInicio, $dataFim);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $movimentacoes[] = [
            'data' => date('d/m/Y H:i', strtotime($row['data'])),
            'codigo' => $row['codigo'],
            'nome' => $row['nome'],
            'origem' => $row['origem'],
            'quantidade' => $row['quantidade']
        ];
    }
    
    // Ordenar por data (mais recente primeiro)
    usort($movimentacoes, function($a, $b) {
        return strtotime(str_replace('/', '-', $b['data'])) - strtotime(str_replace('/', '-', $a['data']));
    });
    
    echo json_encode(['success' => true, 'movimentacoes' => $movimentacoes]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}
?>

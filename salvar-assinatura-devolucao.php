<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

// Obter dados JSON
$input = file_get_contents('php://input');
$dados = json_decode($input, true);

if (!$dados || !isset($dados['id']) || !isset($dados['tipo']) || !isset($dados['assinatura'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dados inválidos']);
    exit();
}

$id = intval($dados['id']);
$tipo = $dados['tipo'];
$assinatura = $dados['assinatura'];

try {
    // Verificar se a assinatura é válida (base64)
    if (!base64_decode($assinatura, true)) {
        echo json_encode(['success' => false, 'message' => 'Assinatura inválida']);
        exit();
    }
    
    // Adicionar campo de assinatura se não existir
    if ($tipo === 'funcionario') {
        $conn->query("ALTER TABLE devolucoes_epi ADD COLUMN IF NOT EXISTS assinatura LONGTEXT");
        $sql = "UPDATE devolucoes_epi SET assinatura = ? WHERE id = ?";
    } else if ($tipo === 'rapida') {
        $conn->query("ALTER TABLE devolucoes_rapidas ADD COLUMN IF NOT EXISTS assinatura LONGTEXT");
        $sql = "UPDATE devolucoes_rapidas SET assinatura = ? WHERE id = ?";
    } else {
        echo json_encode(['success' => false, 'message' => 'Tipo de devolução inválido']);
        exit();
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("si", $assinatura, $id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Assinatura salva com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Devolução não encontrada']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao salvar assinatura: ' . $stmt->error]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}

$conn->close();
?>

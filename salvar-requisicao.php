<?php
include 'conexao.php'; // Inclui a conexão com o banco de dados

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Obtém os dados do formulário
    $solicitante = $_POST['solicitante'];
    $empresa = $_POST['empresaSelecionada'];
    $contrato = $_POST['contrato'];
    $finalidade = $_POST['finalidade'];
    $funcionario = $_POST['id_funcionario'];
    $funcao = $_POST['funcao'];
    $setor = $_POST['setor'];
    $observacao = $_POST['observacao'];

    // Verificar se é requisição urgente
    $requisicao_urgente = isset($_POST['requisicao_urgente']) ? 1 : 0;
    $motivo_urgente = $_POST['motivo_urgente'] ?? '';

    // Insere a solicitação na tabela requisicoes
    $sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("sissssssis", $solicitante, $empresa, $contrato, $finalidade, $funcionario, $funcao, $setor, $observacao, $requisicao_urgente, $motivo_urgente);
        
        if ($stmt->execute()) {
            $codigo_solicitacao = $stmt->insert_id; // Obtém o ID da nova solicitação

            // Agora, insira os itens da solicitação
            if (isset($_POST['produto']) && isset($_POST['quantidade'])) {
                $produtos = $_POST['produto'];
                $quantidades = $_POST['quantidade'];

                foreach ($produtos as $index => $produto) {
                    $quantidade = $quantidades[$index];
                    
                    // Verificar se o produto é um código ou nome
                    if (is_numeric($produto)) {
                        // É um código, use diretamente
                        $codigo_produto = $produto;
                    } else {
                        // É um nome, busque o código correspondente
                        $stmt_prod = $conn->prepare("SELECT codigo FROM produtos WHERE nome = ?");
                        $stmt_prod->bind_param("s", $produto);
                        $stmt_prod->execute();
                        $result_prod = $stmt_prod->get_result();
                        if ($result_prod->num_rows > 0) {
                            $row_prod = $result_prod->fetch_assoc();
                            $codigo_produto = $row_prod['codigo'];
                        } else {
                            // Se não encontrar, use o texto como está
                            $codigo_produto = $produto;
                        }
                    }
                    
                    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, ?, ?)";
                    $stmt_item = $conn->prepare($sql_item);
                    $stmt_item->bind_param("isi", $codigo_solicitacao, $codigo_produto, $quantidade);
                    $stmt_item->execute();
                }
            }

            // Retorna sucesso em JSON
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Solicitação salva com sucesso!']);
            exit();
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Erro ao salvar a solicitação: ' . $stmt->error]);
            exit();
        }
        
        $stmt->close();
    } else {
        echo "Erro ao preparar a consulta: " . $conn->error;
    }
}

$conn->close();
?>

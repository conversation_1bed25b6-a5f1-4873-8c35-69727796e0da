<?php
include 'conexao.php';

// Criar tabela de limites de gastos
$sql = "CREATE TABLE IF NOT EXISTS limites_gastos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo ENUM('empresa', 'setor') NOT NULL,
    nome VARCHAR(255) NOT NULL,
    valor_limite DECIMAL(15,2) NOT NULL,
    periodo ENUM('mensal', 'bimestral', 'semestral', 'anual') NOT NULL,
    saldo_aviso DECIMAL(15,2) DEFAULT 0,
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Tabela limites_gastos criada com sucesso!<br>";
} else {
    echo "Erro ao criar tabela: " . $conn->error . "<br>";
}

// Criar tabela para registrar gastos realizados
$sql2 = "CREATE TABLE IF NOT EXISTS gastos_realizados (
    id INT AUTO_INCREMENT PRIMARY KEY,
    limite_id INT NOT NULL,
    tipo_operacao ENUM('saida_estoque', 'requisicao', 'pedido_mensal') NOT NULL,
    empresa VARCHAR(255) NOT NULL,
    setor VARCHAR(255),
    valor_total DECIMAL(15,2) NOT NULL,
    data_operacao DATE NOT NULL,
    referencia_id INT NOT NULL,
    data_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (limite_id) REFERENCES limites_gastos(id) ON DELETE CASCADE
)";

if ($conn->query($sql2) === TRUE) {
    echo "Tabela gastos_realizados criada com sucesso!<br>";
} else {
    echo "Erro ao criar tabela: " . $conn->error . "<br>";
}

$conn->close();
echo "Script executado com sucesso!";
?> 
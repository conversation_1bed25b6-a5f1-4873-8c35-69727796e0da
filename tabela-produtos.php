<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

$feedbackMessage = "";
$feedbackType = ""; // "success" ou "error"

// Verificar se precisamos atualizar o estoque mínimo ou status do produto
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['delete_id']) && !empty($_POST['delete_id'])) {
        $deleteId = $_POST['delete_id'];
        
        // Verificar se o produto está sendo usado em outras tabelas
        $conn->begin_transaction();
        
        try {
            // Primeiro, excluir registros relacionados na tabela produtos_entrada
            $sql = "DELETE FROM produtos_entrada WHERE codigo = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $deleteId);
            $stmt->execute();
            $stmt->close();
            
            // Agora podemos excluir o produto
            $sql = "DELETE FROM produtos WHERE codigo = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $deleteId);
            if ($stmt->execute()) {
                $conn->commit();
                header('Location: tabela-produtos.php?status=delete-success');
                exit;
            } else {
                $conn->rollback();
                // Mensagem amigável para erro de foreign key
                if (strpos($stmt->error, 'a foreign key constraint fails') !== false) {
                    $msg = urlencode('Não é possível excluir este produto porque ele está vinculado a um ou mais grupos de pedidos mensais. Inative o produto se não quiser mais utilizá-lo.');
                    header('Location: tabela-produtos.php?status=error&message=' . $msg);
                } else {
                    header('Location: tabela-produtos.php?status=error&message=' . urlencode($stmt->error));
                }
                exit;
            }
            $stmt->close();
        } catch (Exception $e) {
            $conn->rollback();
            $msgErro = $e->getMessage();
            if (strpos($msgErro, 'a foreign key constraint fails') !== false) {
                $msg = urlencode('Não é possível excluir este produto porque ele está vinculado a um ou mais grupos de pedidos mensais. Inative o produto se não quiser mais utilizá-lo.');
                header('Location: tabela-produtos.php?status=error&message=' . $msg);
            } else {
                header('Location: tabela-produtos.php?status=error&message=' . urlencode($msgErro));
            }
            exit;
        }
    } elseif (isset($_POST['produto_id']) && isset($_POST['estoque_minimo'])) {
        // Atualizar estoque mínimo
        $produtoId = $_POST['produto_id'];
        $estoqueMinimo = $_POST['estoque_minimo'];
        
        $sql = "UPDATE produtos SET estoque_minimo = ? WHERE codigo = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("is", $estoqueMinimo, $produtoId);
        
        if ($stmt->execute()) {
            header('Location: tabela-produtos.php?status=success&message=' . urlencode('Estoque mínimo definido com sucesso!'));
        } else {
            header('Location: tabela-produtos.php?status=error&message=' . urlencode('Erro ao definir estoque mínimo.'));
        }
        $stmt->close();
        exit;
    } elseif (isset($_POST['inativar_id']) && !empty($_POST['inativar_id'])) {
        // Inativar/ativar produto
        $produtoId = $_POST['inativar_id'];
        $status = $_POST['status']; // 'ativo' ou 'inativo'
        
        $sql = "UPDATE produtos SET status = ? WHERE codigo = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $status, $produtoId);
        
        if ($stmt->execute()) {
            header('Location: tabela-produtos.php?status=success&message=' . urlencode('Status do produto atualizado com sucesso!'));
        } else {
            header('Location: tabela-produtos.php?status=error&message=' . urlencode('Erro ao atualizar status do produto.'));
        }
        $stmt->close();
        exit;
    }
}

// Verificando o status da URL para exibir o feedback
if (isset($_GET['status'])) {
    if ($_GET['status'] == 'edit-success') {
        $feedbackMessage = "Edição realizada com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'edit-error') {
        $feedbackMessage = "Erro ao editar o registro.";
        $feedbackType = "error";
    } elseif ($_GET['status'] == 'delete-success') {
        $feedbackMessage = "Exclusão realizada com sucesso!";
        $feedbackType = "delete-success";
    }
}

// Verificar se a coluna estoque_minimo existe, se não, criar
$result = $conn->query("SHOW COLUMNS FROM produtos LIKE 'estoque_minimo'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE produtos ADD COLUMN estoque_minimo INT DEFAULT 0");
}

// Verificar se a coluna status existe, se não, criar
$result = $conn->query("SHOW COLUMNS FROM produtos LIKE 'status'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE produtos ADD COLUMN status VARCHAR(10) DEFAULT 'ativo'");
}

// Verificar se a coluna validade existe e está configurada corretamente
$result = $conn->query("SHOW COLUMNS FROM produtos WHERE Field = 'validade'");
if ($result->num_rows > 0) {
    $column = $result->fetch_assoc();
    // Se o tipo não for DATE, alterar para DATE
    if ($column['Type'] != 'date') {
        $conn->query("ALTER TABLE produtos MODIFY COLUMN validade DATE");
    }
} else {
    // Se a coluna não existir, criar
    $conn->query("ALTER TABLE produtos ADD COLUMN validade DATE");
}

// Verificar se a coluna validade_uso existe, se não, criar
$result = $conn->query("SHOW COLUMNS FROM produtos LIKE 'validade_uso'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE produtos ADD COLUMN validade_uso INT DEFAULT NULL");
}

// Contar produtos com estoque abaixo do mínimo
$alertaEstoqueQuery = "SELECT COUNT(*) as total FROM produtos WHERE quantidade <= estoque_minimo AND estoque_minimo > 0";
$alertaEstoqueResult = $conn->query($alertaEstoqueQuery);
$alertaEstoqueRow = $alertaEstoqueResult->fetch_assoc();
$totalAlertasEstoque = $alertaEstoqueRow['total'];

// Definir filtro de status padrão (ativo)
$statusFilter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'ativo';
$search = isset($_GET['search']) ? $_GET['search'] : '';
$categoriaFilter = isset($_GET['categoria_filter']) ? $_GET['categoria_filter'] : '';

// Construir a cláusula WHERE com base nos filtros
$whereClause = [];

if ($search) {
    $whereClause[] = "(nome LIKE '%$search%' OR categoria LIKE '%$search%' OR ca LIKE '%$search%')";
}
if ($statusFilter != 'todos') {
    $whereClause[] = "status = '$statusFilter'";
}
if ($categoriaFilter !== '') {
    $whereClause[] = "categoria = '" . $conn->real_escape_string($categoriaFilter) . "'";
}

$whereSQL = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";

$result = $conn->query("SELECT * FROM produtos $whereSQL ORDER BY codigo");
if (!$result) { echo $conn->error; }
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <title>Tabela de Produtos</title>
    <style>
        /* Estilos gerais */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        
        .content-container {
            background: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px;
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Estilos modernos para a tabela principal */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        
        /* Status badges minimalistas */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-ativo {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inativo {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
            margin: 2px;
        }
        
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        /* Filtros e busca */
        .filters-container {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .filters-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Alertas de estoque */
        .estoque-alerta {
            display: inline-flex;
            align-items: center;
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 24px;
            gap: 8px;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .modern-table {
                font-size: 12px;
            }
            
            .modern-table th,
            .modern-table td {
                padding: 12px 8px;
            }
        }
        
        /* Feedback messages */
        .feedback {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .feedback.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback.delete-success {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Menu dropdown */
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }
        
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-toggle {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 180px;
            z-index: 1000;
            display: none;
        }
        
        .dropdown-menu.show {
            display: block;
        }
        
        .dropdown-item {
            padding: 10px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #374151;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .dropdown-item:hover {
            background: #f9fafb;
        }
        
        .dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }
        
        .dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }
        
        /* Dropdown no canto superior direito da div principal */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .main-title {
            font-size: 28px;
            font-weight: 600;
            color: #111827;
        }
        
        .main-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .main-dropdown-toggle {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 14px;
            cursor: pointer;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .main-dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .main-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        
        .main-dropdown-menu.show {
            display: block;
        }
        
        .main-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .main-dropdown-item:hover {
            background: #f9fafb;
        }
        
        .main-dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }
        
        .main-dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }
        
        /* Popup de edição */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .popup {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .popup h3 {
            margin-top: 0;
            color: #111827;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        /* Abas do popup */
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .popup-tab.active {
            background: white;
            color: #2563eb;
            border-color: #2563eb;
        }
        
        .popup-tab:hover {
            background: #f3f4f6;
        }
        
        .popup-tab-content {
            display: none;
        }
        
        .popup-tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        /* Seção de ações */
        .actions-section {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .actions-section h4 {
            margin-top: 0;
            color: #111827;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        /* Popup de feedback */
        .feedback-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }
        
        .feedback-popup.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .feedback-popup.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback-popup.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback-popup.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Ordenação de colunas */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 25px !important;
        }
        .sortable:hover {
            background: #f3f4f6;
        }
        .sort-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #9ca3af;
            display: none;
        }
        .sort-icon.active {
            display: inline;
            color: #2563eb;
        }
        .sort-icon.asc::after {
            content: "▲";
        }
        .sort-icon.desc::after {
            content: "▼";
        }
        .confirm-overlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 20000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .confirm-popup {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            padding: 32px 24px;
            min-width: 280px;
            max-width: 90vw;
            text-align: center;
            z-index: 21000;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .confirm-popup .confirm-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .confirm-popup .confirm-message {
            font-size: 1.1em;
            margin-bottom: 12px;
        }
        .confirm-popup .confirm-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }
        .confirm-popup .btn-confirm {
            background: #22c55e;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-cancel {
            background: #e5e7eb;
            color: #111;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-danger {
            background: #ef4444;
            color: #fff;
        }
        .confirm-popup .btn-warning {
            background: #facc15;
            color: #92400e;
        }
        .quantidade-baixa {
            color: #dc2626;
            font-weight: bold;
        }
        .nome-alerta-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        .nome-alerta-icone {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            color: #f59e42;
            font-size: 1.1em;
            margin-left: 8px;
        }
        .search-bar-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 32px 0 18px 0;
            background: none;
            box-shadow: none;
            border: none;
            padding: 0;
        }
        .search-bar-wrapper {
            position: relative;
            width: 900px;
            max-width: 98vw;
            display: flex;
            align-items: center;
        }
        .search-bar-input {
            width: 100%;
            padding: 13px 44px 13px 14px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 1.08em;
            outline: none;
            background: transparent;
            color: #222;
            transition: border 0.2s;
            box-shadow: none;
            height: 51px;
            margin-right: 0;
        }
        .search-bar-input:focus {
            border-color: #2563eb;
        }
        .search-bar-btn-inside {
            position: absolute;
            right: 0px;
            top: 1px;
            height: 50px;
            width: 55px;
            background: none;
            color: #111;
            border: none;
            border-radius: 8px 8px 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 2;
        }
        
        .search-bar-btn-inside i {
            color: #fff;
            font-size: 1.3em;
        }
        .filter-btn {
            margin-left: 10px;
            margin-bottom: 10px;
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
            margin-top: -8px;
        }
        .filter-slide-menu {
            display: none;
            position: absolute;
            top: 45px;
            right: 0px;
            background: #fff;
            border: 1.5px solid #d1d5db;
            border-radius: 10px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.10);
            min-width: 26px;
            z-index: 100;
            padding: 16px 18px;
        }
        .filter-slide-menu.open {
            display: block;
        }
        .content-container {
            position: relative;
        }
        .filter-btn.active .filter-slide-menu {
            display: block;
        }
        .close-slide-menu {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 22px;
            height: 22px;
            background: none;
            border: none;
            color: #111;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            padding: 0;
            z-index: 10;
            border-radius: 50%;
            transition: background 0.2s;
        }
        .close-slide-menu:hover {
            background: none;
            color: #000;
        }
        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 18px;
        }
        /* Dropdown minimalista para filtros */
        .filter-slide-menu select {
            width: 100%;
            padding: 8px 12px;
            border: 1.2px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 400;
            background: #f8fafc;
            color: #222;
            outline: none;
            box-shadow: none;
            transition: border 0.2s;
            margin-bottom: 10px;
        }
        .filter-slide-menu select:focus {
            border-color: #2563eb;
            background: #fff;
        }
        .filter-slide-menu label {
            font-size: 13px;
            font-weight: 400;
            color: #374151;
            margin-bottom: 2px;
        }
        .filter-popup-overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.4);
            z-index: 1001;
        }
        .filter-popup {
            display: none;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
    <div class="main-header">
        <h1 class="main-title">Tabela de Produtos</h1>
        <div class="main-dropdown">
            <button class="btn-modern btn-cancel main-dropdown-toggle" onclick="toggleMainDropdown()">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="main-dropdown-menu" id="mainExportDropdown">
                <div class="main-dropdown-item" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Exportar para Excel
                </div>
                <div class="main-dropdown-item" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Exportar para PDF
                </div>
            </div>
        </div>
    </div>

    <?php if ($feedbackMessage): ?>
        <!-- Removido aviso fixo, feedback só via popup -->
    <?php endif; ?>

    <!-- Popup de feedback -->
    <div id="feedbackPopup" class="feedback-popup"></div>

    <!-- Barra de busca e filtro -->
    <div class="search-bar-container">
        <form method="GET" id="searchForm" style="display: none;">
                            <input type="text" class="search-bar-input" name="search" id="search" value="<?= htmlspecialchars($search) ?>" placeholder="Pesquisar...">
            <button type="submit" class="search-bar-btn" title="Buscar">
                <i class="fas fa-search"></i>
            </button>
        </form>
        <div style="display: flex; align-items: center; gap: 20px; justify-content: center;">
            <div class="search-bar-wrapper">
                <input type="text" class="search-bar-input" id="searchVisible" placeholder="Pesquisar..." value="<?= htmlspecialchars($search) ?>">
                <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar">
                    <i class="fas fa-search" style="color: #111;"></i>
                </button>
            </div>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;"><i class="fas fa-filter"></i></button>
        </div>
    </div>
    <!-- Popup de filtros moderno -->
    <div id="filterPopupOverlay" class="filter-popup-overlay"></div>
    <div id="filterPopup" class="filter-popup">
        <div class="filter-popup-header">
            <span>Filtrar por:</span>
            <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
        </div>
        <form method="get" class="filter-popup-form" style="display: flex; flex-direction: column; gap: 18px;">
            <div class="filter-popup-group">
                <label for="status_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Status:</label>
                <select id="status_filter" name="status_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                    <option value="ativo" <?= $statusFilter == 'ativo' ? 'selected' : '' ?>>Ativo</option>
                    <option value="inativo" <?= $statusFilter == 'inativo' ? 'selected' : '' ?>>Inativo</option>
                    <option value="todos" <?= $statusFilter == 'todos' ? 'selected' : '' ?>>Todos</option>
                </select>
            </div>
            <div class="filter-popup-group">
                <label for="categoria_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Categoria:</label>
                <select id="categoria_filter" name="categoria_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                    <option value="">Todas</option>
                    <?php
                    $resCat = $conn->query("SELECT DISTINCT categoria FROM produtos ORDER BY categoria");
                    while ($cat = $resCat->fetch_assoc()): ?>
                        <option value="<?= htmlspecialchars($cat['categoria']) ?>" <?= (isset($_GET['categoria_filter']) && $_GET['categoria_filter'] == $cat['categoria']) ? 'selected' : '' ?>><?= htmlspecialchars($cat['categoria']) ?></option>
                    <?php endwhile; ?>
                </select>
            </div>
            <div class="filter-popup-actions" style="display: flex; gap: 12px; margin-top: 10px;">
                <a href="tabela-produtos.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
            </div>
        </form>
    </div>
    <!-- Fim da barra de busca/filtro -->

    <!-- Alerta de estoque baixo -->
    <?php if ($totalAlertasEstoque > 0): ?>
        <div class="estoque-alerta">
            <i class="fas fa-exclamation-triangle"></i>
                            <?= $totalAlertasEstoque ?> produto(s) atingiram o estoque mínimo
        </div>
    <?php endif; ?>

    <div class="modern-table-container">
        <table class="modern-table" id="produtosTable">
            <thead>
                <tr>
                    <th class="sortable" data-sort="codigo">
                        Código
                    </th>
                    <th class="sortable" data-sort="unidade_medida">Unidade</th>
                    <th class="sortable" data-sort="nome">
                        Nome
                    </th>
                    <th class="sortable" data-sort="categoria">
                        Categoria
                    </th>
                    <th class="sortable" data-sort="ca">
                        CA
                    </th>
                    <th class="sortable" data-sort="valor">Valor</th>
                    <th class="sortable" data-sort="valor_bruto">Valor Bruto</th>
                    <th class="sortable" data-sort="quantidade">
                        Quantidade
                    </th>
                    <th class="sortable" data-sort="estoque_minimo">
                        Estoque Mínimo
                    </th>
                    <th class="sortable" data-sort="validade">
                        Validade
                    </th>
                    <th class="sortable" data-sort="status">
                        Status
                    </th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dados as $row): ?>
                    <?php 
                        $status = isset($row['status']) ? $row['status'] : 'ativo';
                        $statusClass = $status === 'ativo' ? 'status-ativo' : 'status-inativo';
                        $isEstoqueBaixo = $row['quantidade'] <= $row['estoque_minimo'] && $row['estoque_minimo'] > 0;
                    ?>
                    <tr>
                        <td><?= $row['codigo'] ?></td>
                        <td><?= $row['unidade_medida'] ?></td>
                        <td>
                            <div class="nome-alerta-container">
                                <?= $row['nome'] ?>
                                <?php if ($isEstoqueBaixo): ?>
                                    <span class="nome-alerta-icone">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?= $row['categoria'] ?></td>
                        <td><?= $row['ca'] ?></td>
                        <td><?= number_format($row['valor'], 2, ',', '.') ?></td>
                        <td><?= number_format($row['valor_bruto'], 2, ',', '.') ?></td>
                        <td>
                            <?php if ($isEstoqueBaixo): ?>
                                <span class="quantidade-baixa"><?= $row['quantidade'] ?></span>
                            <?php else: ?>
                                <?= $row['quantidade'] ?>
                            <?php endif; ?>
                        </td>
                        <td><?= $row['estoque_minimo'] ?></td>
                        <td><?= $row['validade'] ? date('d/m/Y', strtotime($row['validade'])) : '-' ?></td>
                        <td><span class="status-badge <?= $statusClass ?>"><?= ucfirst($status) ?></span></td>
                        <td>
                            <button class="btn-modern btn-confirm" onclick="openEditPopup(<?= htmlspecialchars(json_encode($row)) ?>)">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a href="cadastro-produto.php" class="btn-modern btn-confirm">
            <i class="fas fa-plus"></i> Adicionar Novo Produto
        </a>
    </div>
</div>

<!-- Popup de edição -->
<div id="editPopup" class="popup-overlay">
    <div class="popup">
        <h3>Editar Produto</h3>
        
        <div class="popup-tabs">
            <div class="popup-tab active" onclick="switchTab('info')">Informações</div>
            <div class="popup-tab" onclick="switchTab('actions')">Ações</div>
            <div class="popup-tab" onclick="switchTab('movimentacoes')">Movimentações</div>
        </div>
        
        <div id="info-tab" class="popup-tab-content active">
            <form id="editForm" action="editar-produto.php" method="POST">
                <input type="hidden" id="edit_codigo" name="codigo">
                
                <div class="form-group">
                    <label for="edit_nome">Nome:</label>
                    <input type="text" id="edit_nome" name="nome" required>
                </div>
                <div class="form-group">
                    <label for="edit_unidade_medida">Unidade</label>
                    <input type="text" id="edit_unidade_medida" name="unidade_medida" maxlength="5" placeholder="Ex: UN, DZ, ML, LT, GL" style="text-transform: uppercase;" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_categoria">Categoria:</label>
                    <select id="edit_categoria" name="categoria" required>
                        <option value="">Selecione uma categoria</option>
                        <?php
                        $categorias = [
                            "Equipamentos de Proteção Individual (EPIs)",
                            "Materiais de Escritório",
                            "Materiais de Limpeza",
                            "Ferramentas e Equipamentos Manuais",
                            "Uniformes e Roupas de Trabalho",
                            "Materiais Elétricos",
                            "Peças de Reposição e Componentes Mecânicos",
                            "Produtos de Informática",
                            "Materiais de Construção",
                            "Itens de Consumo Rápido (Descartáveis)",
                            "Materiais de Higiene Pessoal",
                            "Produtos Químicos Industriais",
                            "Equipamentos Eletrônicos",
                            "Materiais de Embalagem",
                            "Suprimentos Médicos",
                            "Alimentos e Bebidas para Consumo Interno"
                        ];
                        foreach ($categorias as $cat) {
                            echo "<option value=\"$cat\">$cat</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_ca">CA:</label>
                    <input type="text" id="edit_ca" name="ca">
                </div>
                
                <div class="form-group">
                    <label for="edit_valor">Valor:</label>
                    <input type="number" step="0.01" id="edit_valor" name="valor" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_valor_bruto">Valor Bruto:</label>
                    <input type="number" step="0.01" id="edit_valor_bruto" name="valor_bruto" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_quantidade">Quantidade:</label>
                    <input type="number" id="edit_quantidade" name="quantidade" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_estoque_minimo">Estoque Mínimo:</label>
                    <input type="number" id="edit_estoque_minimo" name="estoque_minimo" min="0">
                </div>
                
                <div class="form-group">
                    <label for="edit_validade">Data de Validade:</label>
                    <input type="date" id="edit_validade" name="validade">
                </div>
                
                <div class="form-group" id="edit_validade_uso_group" style="display:none;">
                    <label for="edit_validade_uso">Validade de Uso (meses):</label>
                    <input type="number" id="edit_validade_uso" name="validade_uso" min="1" step="1">
                </div>
                
                <div class="form-buttons">
                    <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Cancelar</button>
                    <button type="submit" class="btn-modern btn-confirm">Salvar</button>
                </div>
            </form>
        </div>
        
        <div id="actions-tab" class="popup-tab-content">
            <div class="actions-section">
                <h4>Ações do Produto</h4>
                <p>Selecione uma ação para executar no produto <strong id="produto-nome-acoes"></strong>:</p>
                
                <div class="action-buttons">
                    <button class="btn-modern btn-warning" onclick="inativarProdutoFromPopup()">
                        <i class="fas fa-pause"></i> Inativar Produto
                    </button>
                    <button class="btn-modern btn-confirm" onclick="ativarProdutoFromPopup()" id="btn-ativar-popup" style="display: none;">
                        <i class="fas fa-play"></i> Ativar Produto
                    </button>
                    <button class="btn-modern btn-danger" onclick="excluirProdutoFromPopup()">
                        <i class="fas fa-trash"></i> Excluir Produto
                    </button>
                </div>
            </div>
            
            <div class="form-buttons">
                <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Fechar</button>
            </div>
        </div>

        <div id="movimentacoes-tab" class="popup-tab-content">
            <div class="movimentacoes-section">
                <h4>Movimentações do Produto</h4>
                <p>Consulte o histórico de movimentações do produto <strong id="produto-nome-movimentacoes"></strong>:</p>

                <div class="filtros-movimentacoes" style="display: flex; gap: 15px; margin-bottom: 20px; align-items: end;">
                    <div class="form-group" style="margin: 0;">
                        <label for="data_inicio_mov">Data Início:</label>
                        <input type="date" id="data_inicio_mov" name="data_inicio_mov" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <div class="form-group" style="margin: 0;">
                        <label for="data_fim_mov">Data Fim:</label>
                        <input type="date" id="data_fim_mov" name="data_fim_mov" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    <button type="button" class="btn-modern btn-confirm" onclick="buscarMovimentacoes()" style="height: 36px;">
                        <i class="fas fa-search"></i> Buscar
                    </button>
                    <div class="export-dropdown" style="position: relative;">
                        <button class="btn-modern" onclick="toggleExportDropdown(event, 'dropdown-movimentacoes')" title="Opções de exportação" style="height: 36px;">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <div class="export-dropdown-content" id="dropdown-movimentacoes" style="display: none; position: absolute; right: 0; top: 100%; background: white; min-width: 160px; box-shadow: 0 8px 16px rgba(0,0,0,0.2); border-radius: 4px; z-index: 1000; margin-top: 4px;">
                            <button class="export-dropdown-item" onclick="exportarMovimentacoes('excel')" style="display: flex; align-items: center; gap: 8px; padding: 12px 16px; border: none; background: none; width: 100%; text-align: left; cursor: pointer;">
                                <i class="fas fa-file-excel" style="color: #10b981;"></i>
                                Exportar Excel
                            </button>
                            <button class="export-dropdown-item" onclick="exportarMovimentacoes('pdf')" style="display: flex; align-items: center; gap: 8px; padding: 12px 16px; border: none; background: none; width: 100%; text-align: left; cursor: pointer;">
                                <i class="fas fa-file-pdf" style="color: #ef4444;"></i>
                                Exportar PDF
                            </button>
                        </div>
                    </div>
                </div>

                <div id="movimentacoes-loading" style="display: none; text-align: center; padding: 20px;">
                    <i class="fas fa-spinner fa-spin"></i> Carregando movimentações...
                </div>

                <div id="movimentacoes-container" style="max-height: 400px; overflow-y: auto;">
                    <table id="tabela-movimentacoes" class="modern-table" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Data</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Código</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Nome do Produto</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Origem</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">Quantidade</th>
                            </tr>
                        </thead>
                        <tbody id="movimentacoes-tbody">
                            <tr>
                                <td colspan="5" style="text-align: center; padding: 40px; color: #6c757d;">
                                    Selecione um período e clique em "Buscar" para ver as movimentações
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="form-buttons">
                <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Formulário oculto para ações -->
<form id="actionForm" method="POST" action="tabela-produtos.php" style="display: none;">
    <input type="hidden" id="delete_id" name="delete_id">
    <input type="hidden" id="inativar_id" name="inativar_id">
    <input type="hidden" id="status" name="status">
</form>

<!-- Overlay e Popup de Confirmação -->
<div class="confirm-overlay" id="confirmOverlay">
    <div class="confirm-popup" id="confirmPopup">
        <div class="confirm-title" id="confirmTitle"></div>
        <div class="confirm-message" id="confirmMessage"></div>
        <div class="confirm-actions">
            <button class="btn-confirm" id="btnConfirmYes">Confirmar</button>
            <button class="btn-cancel" id="btnConfirmNo">Cancelar</button>
        </div>
    </div>
</div>

<!-- Bibliotecas para exportação -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.7.0/jspdf.plugin.autotable.min.js"></script>

<script>
    let currentProduto = null;
    let currentSort = { column: null, direction: 'asc' };
    
    // Função para mostrar popup de feedback
    function showFeedback(message, type = 'success') {
        const popup = document.getElementById('feedbackPopup');
        popup.textContent = message;
        popup.className = `feedback-popup ${type}`;
        popup.classList.add('show');
        
        setTimeout(() => {
            popup.classList.remove('show');
        }, 5000);
    }
    
    // Verificar se há mensagem de feedback na URL
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    if (status) {
        let message = '';
        let type = 'success';
        
        switch(status) {
            case 'edit-success':
                message = 'Edição realizada com sucesso!';
                type = 'success'; // verde
                break;
            case 'edit-error':
                message = 'Erro ao editar o registro.';
                type = 'error'; // vermelho
                break;
            case 'delete-success':
                message = 'Exclusão realizada com sucesso!';
                type = 'error'; // vermelho
                break;
            case 'success':
                message = urlParams.get('message') || 'Operação realizada com sucesso!';
                type = 'warning'; // amarelo para ativar/inativar
                break;
            case 'error':
                message = urlParams.get('message') || 'Erro ao realizar a operação.';
                type = 'error'; // vermelho
                break;
        }
        
        if (message) {
            showFeedback(message, type);
        }
    }
    
    // Fechar dropdown quando clicar fora
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('.main-dropdown');
        const dropdownMenu = document.getElementById('mainExportDropdown');
        
        if (!dropdown.contains(event.target)) {
            dropdownMenu.classList.remove('show');
        }
    });

    // --- Popup de Confirmação Global ---
    let confirmAction = null;
    function showConfirmPopup({title, message, onConfirm, type = 'default'}) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        const popup = document.getElementById('confirmPopup');
        popup.classList.remove('btn-danger', 'btn-warning');
        if (type === 'danger') {
            document.getElementById('btnConfirmYes').classList.add('btn-danger');
            document.getElementById('btnConfirmYes').classList.remove('btn-warning');
        } else if (type === 'warning') {
            document.getElementById('btnConfirmYes').classList.add('btn-warning');
            document.getElementById('btnConfirmYes').classList.remove('btn-danger');
        } else {
            document.getElementById('btnConfirmYes').classList.remove('btn-danger', 'btn-warning');
        }
        document.getElementById('confirmOverlay').style.display = 'flex';
        confirmAction = onConfirm;
    }
    document.getElementById('btnConfirmYes').onclick = function() {
        document.getElementById('confirmOverlay').style.display = 'none';
        if (typeof confirmAction === 'function') confirmAction();
    };
    document.getElementById('btnConfirmNo').onclick = function() {
        document.getElementById('confirmOverlay').style.display = 'none';
        confirmAction = null;
    };

    // Substituir ações por popups de confirmação
    function excluirProduto(codigo, nome) {
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir o produto "${nome}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = codigo;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarProduto(codigo) {
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar este produto?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = codigo;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarProduto(codigo) {
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar este produto?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = codigo;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function excluirProdutoFromPopup() {
        if (!currentProduto) return;
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir o produto "${currentProduto.nome}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = currentProduto.codigo;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarProdutoFromPopup() {
        if (!currentProduto) return;
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar este produto?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentProduto.codigo;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarProdutoFromPopup() {
        if (!currentProduto) return;
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar este produto?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentProduto.codigo;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    // Confirmação ao salvar edição
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.onsubmit = function(e) {
            e.preventDefault();
            showConfirmPopup({
                title: 'Confirmar Edição',
                message: 'Deseja salvar as alterações deste produto?',
                type: 'confirm',
                onConfirm: function() {
                    editForm.onsubmit = null;
                    editForm.submit();
                }
            });
            return false;
        };
    }

    function openEditPopup(rowData) {
        currentProduto = rowData;
        document.getElementById('edit_codigo').value = rowData.codigo;
        document.getElementById('edit_nome').value = rowData.nome;
        document.getElementById('edit_unidade_medida').value = rowData.unidade_medida || '';
        document.getElementById('edit_categoria').value = rowData.categoria;
        document.getElementById('edit_ca').value = rowData.ca;
        document.getElementById('edit_valor').value = rowData.valor;
        document.getElementById('edit_valor_bruto').value = rowData.valor_bruto;
        document.getElementById('edit_quantidade').value = rowData.quantidade;
        document.getElementById('edit_estoque_minimo').value = rowData.estoque_minimo;
        document.getElementById('edit_validade').value = rowData.validade ? rowData.validade.split('T')[0] : '';
        document.getElementById('edit_validade_uso').value = rowData.validade_uso || '';
        
        // Gerenciar campo de validade de uso baseado na categoria
        toggleValidadeUsoField(rowData.categoria);
        
        // Atualizar nome do produto na aba de ações
        document.getElementById('produto-nome-acoes').textContent = rowData.nome;

        // Atualizar nome do produto na aba de movimentações
        document.getElementById('produto-nome-movimentacoes').textContent = rowData.nome;
        
        // Configurar botões de ativar/inativar baseado no status
        const status = rowData.status || 'ativo';
        if (status === 'ativo') {
            document.querySelector('[onclick="inativarProdutoFromPopup()"]').style.display = 'inline-flex';
            document.getElementById('btn-ativar-popup').style.display = 'none';
        } else {
            document.querySelector('[onclick="inativarProdutoFromPopup()"]').style.display = 'none';
            document.getElementById('btn-ativar-popup').style.display = 'inline-flex';
        }
        
        document.getElementById('editPopup').style.display = 'flex';
    }

    function toggleValidadeUsoField(categoria) {
        const validadeUsoGroup = document.getElementById('edit_validade_uso_group');
        const validadeUsoInput = document.getElementById('edit_validade_uso');
        
        if (categoria === 'Equipamentos de Proteção Individual (EPIs)' || categoria === 'Uniformes e Roupas de Trabalho') {
            validadeUsoGroup.style.display = 'block';
            validadeUsoInput.required = true;
        } else {
            validadeUsoGroup.style.display = 'none';
            validadeUsoInput.required = false;
            validadeUsoInput.value = '';
        }
    }

    // Adicionar listener global para mudança de categoria
    document.addEventListener('change', function(event) {
        if (event.target.id === 'edit_categoria') {
            toggleValidadeUsoField(event.target.value);
        }
    });

    function closeEditPopup() {
        document.getElementById('editPopup').style.display = 'none';
        currentProduto = null;
    }

    function switchTab(tabName) {
        // Desativar todas as abas
        document.querySelectorAll('.popup-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.popup-tab-content').forEach(content => content.classList.remove('active'));
        
        // Ativar a aba selecionada
        document.querySelector(`.popup-tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    function toggleMainDropdown() {
        document.getElementById('mainExportDropdown').classList.toggle('show');
    }

    // Função para ordenar a tabela
    function sortTable(column) {
        const table = document.getElementById('produtosTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        // Determinar direção da ordenação
        if (currentSort.column === column) {
            currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.column = column;
            currentSort.direction = 'asc';
        }
        
        // Remover todos os sort-icons
        document.querySelectorAll('.sortable .sort-icon').forEach(icon => icon.remove());

        // Adicionar sort-icon apenas na coluna ordenada
        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        const icon = document.createElement('span');
        icon.className = `sort-icon active ${currentSort.direction}`;
        currentHeader.appendChild(icon);
        
        // Ordenar as linhas
        rows.sort((a, b) => {
            let aValue = a.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            let bValue = b.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            
            // Converter para número se for numérico
            if (['codigo', 'quantidade', 'estoque_minimo'].includes(column)) {
                aValue = parseFloat(aValue) || 0;
                bValue = parseFloat(bValue) || 0;
            }
            
            // Converter data se for coluna de validade
            if (column === 'validade') {
                aValue = aValue === '-' ? '1900-01-01' : convertDateToISO(aValue);
                bValue = bValue === '-' ? '1900-01-01' : convertDateToISO(bValue);
            }
            
            if (currentSort.direction === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        // Reordenar as linhas na tabela
        rows.forEach(row => tbody.appendChild(row));
    }
    
    function getColumnIndex(column) {
        const columnMap = {
            'codigo': 1,
            'nome': 2,
            'categoria': 3,
            'ca': 4,
            'quantidade': 5,
            'estoque_minimo': 6,
            'validade': 7,
            'status': 8
        };
        return columnMap[column];
    }
    
    function convertDateToISO(dateStr) {
        if (dateStr === '-') return '1900-01-01';
        const parts = dateStr.split('/');
        return `${parts[2]}-${parts[1]}-${parts[0]}`;
    }
    
    // Adicionar listeners para ordenação
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                sortTable(column);
            });
        });
    });

    function exportToExcel() {
        const table = document.getElementById('produtosTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const wb = XLSX.utils.table_to_book(clonedTable, {sheet: "Produtos"});
        XLSX.writeFile(wb, 'produtos.xlsx');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    function exportToPDF() {
        const table = document.getElementById('produtosTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const doc = new jspdf.jsPDF('l', 'pt', 'a4');
        doc.autoTable({ html: clonedTable });
        doc.save('produtos.pdf');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    // Sincronizar barra de pesquisa visível com o form invisível
    const searchVisible = document.getElementById('searchVisible');
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('search');
    const searchVisibleBtn = document.getElementById('searchVisibleBtn');
    searchVisible.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchInput.value = searchVisible.value;
            searchForm.submit();
        }
    });
    searchVisibleBtn.addEventListener('click', function() {
        searchInput.value = searchVisible.value;
        searchForm.submit();
    });
    // Popup de filtros moderno
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const filterPopupOverlay = document.getElementById('filterPopupOverlay');
    const closeFilterPopup = document.getElementById('closeFilterPopup');
    filterBtn.addEventListener('click', function(e) {
        filterPopup.style.display = 'block';
        filterPopupOverlay.style.display = 'block';
        e.stopPropagation();
    });
    closeFilterPopup.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
    filterPopupOverlay.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });

    // Funções para movimentações
    function buscarMovimentacoes() {
        if (!currentProduto) return;

        const dataInicio = document.getElementById('data_inicio_mov').value;
        const dataFim = document.getElementById('data_fim_mov').value;

        if (!dataInicio || !dataFim) {
            alert('Por favor, selecione as datas de início e fim.');
            return;
        }

        if (dataInicio > dataFim) {
            alert('A data de início não pode ser maior que a data de fim.');
            return;
        }

        document.getElementById('movimentacoes-loading').style.display = 'block';
        document.getElementById('movimentacoes-tbody').innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px;">Carregando...</td></tr>';

        fetch('buscar-movimentacoes.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                codigo: currentProduto.codigo,
                data_inicio: dataInicio,
                data_fim: dataFim
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('movimentacoes-loading').style.display = 'none';

            if (data.success) {
                preencherTabelaMovimentacoes(data.movimentacoes);
            } else {
                document.getElementById('movimentacoes-tbody').innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: #dc3545;">Erro ao buscar movimentações: ' + data.message + '</td></tr>';
            }
        })
        .catch(error => {
            document.getElementById('movimentacoes-loading').style.display = 'none';
            document.getElementById('movimentacoes-tbody').innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 20px; color: #dc3545;">Erro ao buscar movimentações</td></tr>';
            console.error('Erro:', error);
        });
    }

    function preencherTabelaMovimentacoes(movimentacoes) {
        const tbody = document.getElementById('movimentacoes-tbody');

        if (movimentacoes.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px; color: #6c757d;">Nenhuma movimentação encontrada no período selecionado</td></tr>';
            return;
        }

        let html = '';
        movimentacoes.forEach(mov => {
            const corOrigem = mov.origem === 'Entrada' ? '#10b981' : '#ef4444';
            html += `
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px;">${mov.data}</td>
                    <td style="padding: 12px;">${mov.codigo}</td>
                    <td style="padding: 12px;">${mov.nome}</td>
                    <td style="padding: 12px; color: ${corOrigem}; font-weight: 600;">${mov.origem}</td>
                    <td style="padding: 12px;">${mov.quantidade}</td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }

    function toggleExportDropdown(event, dropdownId) {
        event.stopPropagation();
        const dropdown = document.getElementById(dropdownId);
        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';

        // Fechar outros dropdowns
        document.querySelectorAll('.export-dropdown-content').forEach(d => {
            if (d.id !== dropdownId) d.style.display = 'none';
        });
    }

    function exportarMovimentacoes(tipo) {
        const tabela = document.getElementById('tabela-movimentacoes');
        const tbody = document.getElementById('movimentacoes-tbody');

        if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
            alert('Não há dados para exportar. Faça uma busca primeiro.');
            return;
        }

        if (tipo === 'excel') {
            const wb = XLSX.utils.table_to_book(tabela, {sheet: "Movimentações"});
            XLSX.writeFile(wb, `movimentacoes_${currentProduto.codigo}.xlsx`);
        } else if (tipo === 'pdf') {
            const doc = new jspdf.jsPDF('l', 'pt', 'a4');
            doc.text(`Movimentações do Produto: ${currentProduto.nome}`, 40, 40);
            doc.autoTable({
                html: tabela,
                startY: 60,
                styles: { fontSize: 8 }
            });
            doc.save(`movimentacoes_${currentProduto.codigo}.pdf`);
        }

        // Fechar dropdown
        document.getElementById('dropdown-movimentacoes').style.display = 'none';
    }

    // Fechar dropdowns ao clicar fora
    document.addEventListener('click', function() {
        document.querySelectorAll('.export-dropdown-content').forEach(d => {
            d.style.display = 'none';
        });
    });
</script>

</body>
</html>

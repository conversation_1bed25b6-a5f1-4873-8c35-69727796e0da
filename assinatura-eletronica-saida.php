<?php
session_start();
include 'conexao.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if (!$id) { die('ID inválido.'); }

// Buscar dados da saída
$stmt = $conn->prepare('SELECT * FROM saidas_estoque WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();
$saida = $stmt->get_result()->fetch_assoc();
if (!$saida) { die('Saída não encontrada.'); }

// Buscar produtos da saída (ajuste para a tabela correta)
$produtos = [];
$res = $conn->query("SELECT ps.codigo, COALESCE(ps.nome, p.nome, 'Produto removido') as nome, ps.quantidade FROM produtos_saida ps LEFT JOIN produtos p ON ps.codigo = p.codigo WHERE ps.saida_id = $id");
while ($row = $res->fetch_assoc()) $produtos[] = $row;

$assinatura = $saida['assinatura_eletronica'];
$data_assinatura = $saida['data_assinatura_eletronica'];

// Detectar tipo de assinatura
$tipo_assinatura = null;
$assinatura_url = null;
if ($assinatura) {
    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mime = $finfo->buffer($assinatura);
    if (strpos($mime, 'pdf') !== false) {
        $tipo_assinatura = 'pdf';
        $assinatura_url = 'ver-assinatura-eletronica.php?id=' . $id;
    } else {
        $tipo_assinatura = 'imagem';
        $assinatura_url = null;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Assinatura Eletrônica - Saída #<?= htmlspecialchars($id) ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { background: #f3f4f6; font-family: Arial, sans-serif; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 40px auto; background: #fff; border-radius: 12px; box-shadow: 0 4px 24px rgba(0,0,0,0.10); padding: 32px; }
        h2 { text-align: center; margin-bottom: 24px; }
        .info p { margin: 4px 0; }
        table { width: 100%; border-collapse: collapse; margin: 24px 0; }
        th, td { border: 1px solid #bbb; padding: 8px 12px; text-align: left; }
        th { background: #f3f4f6; }
        .assinatura-area { margin-top: 40px; text-align: center; }
        .assinatura-label { margin-bottom: 16px; font-size: 16px; }
        .assinatura-canvas { border: 1.5px solid #222; border-radius: 8px; background: #fff; }
        .linha-assinatura { margin: 24px auto 0 auto; border-top: 1.5px solid #222; width: 320px; height: 2px; }
        .assinatura-nome { margin-top: 8px; font-size: 14px; color: #555; }
        .btn { background: #2563eb; color: #fff; border: none; border-radius: 8px; padding: 12px 24px; font-size: 16px; font-weight: 600; cursor: pointer; margin: 18px 0 0 0; transition: background 0.18s; }
        .btn:hover { background: #1d4ed8; }
        .btn-clear { background: #e5e7eb; color: #222; margin-left: 12px; }
        .btn-clear:hover { background: #d1d5db; }
        .assinatura-img { margin: 0 auto 12px auto; display: block; border: 1.5px solid #222; border-radius: 8px; max-width: 320px; max-height: 120px; background: #fff; }
        .assinatura-info { text-align: center; margin-top: 12px; color: #2563eb; font-size: 15px; }
        .share-btn { position: absolute; top: 32px; right: 32px; background: #f3f4f6; border: 1px solid #e5e7eb; border-radius: 8px; color: #222; font-size: 15px; padding: 8px 18px; cursor: pointer; transition: background 0.18s; }
        .share-btn:hover { background: #e5e7eb; }
        @media (max-width: 700px) { .container { margin: 0; border-radius: 0; box-shadow: none; } .share-btn { right: 12px; top: 12px; } }
        .btn-top {
            background: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            color: #222;
            font-size: 15px;
            padding: 8px 18px;
            cursor: pointer;
            margin: 0;
            transition: background 0.18s;
            font-weight: 500;
        }
        .btn-top:hover {
            background: #e5e7eb;
        }
        @media print {
            .btn-top { display: none !important; }
        }
    </style>
</head>
<body>
<div class="container">
    <div style="display:flex; justify-content:flex-end; align-items:center; gap:10px; position:relative; margin-bottom: 8px;">
        <button class="btn-top" onclick="gerarPDF()">Gerar PDF</button>
        <button class="btn-top" onclick="compartilharLink()">Compartilhar Link</button>
    </div>
    <h2>Assinatura Eletrônica<br>Saída #<?= htmlspecialchars($id) ?></h2>
    <div class="info">
        <p><strong>Data:</strong> <?= htmlspecialchars(date('d/m/Y', strtotime($saida['data_saida']))) ?></p>
        <p><strong>Responsável:</strong> <?= htmlspecialchars($saida['responsavel']) ?></p>
        <p><strong>Empresa Destino:</strong> <?= htmlspecialchars($saida['empresa_destino']) ?></p>
        <p><strong>Destinatário:</strong> <?= htmlspecialchars($saida['destinatario']) ?></p>
        <p><strong>Setor do Destinatário:</strong> <?= htmlspecialchars($saida['setor_destinatario']) ?></p>
        <p><strong>Motivo:</strong> <?= htmlspecialchars($saida['motivo']) ?></p>
        <p><strong>Observações:</strong> <?= htmlspecialchars($saida['observacoes']) ?></p>
        <p><strong>Status:</strong> <?= htmlspecialchars($saida['status']) ?></p>
    </div>
    <table><thead><tr><th>Código</th><th>Nome</th><th>Quantidade</th></tr></thead><tbody>
        <?php foreach ($produtos as $p): ?>
            <tr><td><?= htmlspecialchars($p['codigo']) ?></td><td><?= htmlspecialchars($p['nome']) ?></td><td><?= htmlspecialchars($p['quantidade']) ?></td></tr>
        <?php endforeach; ?>
    </tbody></table>
    <div class="assinatura-area">
        <div class="assinatura-label">Assinatura do Recebedor:</div>
        <?php if ($assinatura): ?>
            <?php if ($tipo_assinatura === 'imagem'): ?>
                <img src="data:image/png;base64,<?= base64_encode($assinatura) ?>" class="assinatura-img" alt="Assinatura">
                <div class="linha-assinatura"></div>
                <div class="assinatura-nome">Assinado em: <?= date('d/m/Y H:i', strtotime($data_assinatura)) ?></div>
            <?php elseif ($tipo_assinatura === 'pdf'): ?>
                <div style="margin: 18px 0 12px 0; text-align:center;">
                    <a href="<?= $assinatura_url ?>" target="_blank" style="color:#2563eb; text-decoration:underline; font-size:16px;">Visualizar PDF da Assinatura</a>
                </div>
                <div class="linha-assinatura"></div>
                <div class="assinatura-nome">Assinado em: <?= date('d/m/Y H:i', strtotime($data_assinatura)) ?></div>
            <?php endif; ?>
        <?php else: ?>
            <div style="display:flex; flex-direction:column; align-items:center; gap:10px;">
                <div style="border:1.5px solid #222; border-radius:8px; background:#fff; width:340px; max-width:100%; padding:12px 8px 0 8px; position:relative;">
                    <canvas id="signature-pad" width="320" height="120" style="display:block; background:#fff; border-radius:8px;"></canvas>
                    <div style="position:absolute; left:16px; right:16px; bottom:18px; height:0; border-bottom:1.5px solid #222;"></div>
                    <div style="position:absolute; left:0; right:0; bottom:0; text-align:center; color:#bbb; font-size:13px;">Assine acima da linha</div>
                </div>
                <div style="display:flex; gap:10px; margin-top:8px; flex-direction:row; justify-content:center; width:100%; max-width:340px;">
                    <button class="btn btn-clear" id="clear-signature" type="button" style="padding:10px 24px; font-size:16px;">Limpar</button>
                    <button class="btn" id="save-signature" style="padding:10px 24px; font-size:16px;">Salvar Assinatura</button>
                </div>
                <div class="assinatura-info" id="assinatura-info"></div>
            </div>
        <?php endif; ?>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
function gerarPDF() {
    window.print();
}
function compartilharLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        alert('Link copiado para a área de transferência!');
    });
}
<?php if (!$assinatura): ?>
const canvas = document.getElementById('signature-pad');
const signaturePad = new SignaturePad(canvas, {
    backgroundColor: 'rgb(255,255,255)',
    penColor: '#222',
    minWidth: 1.5,
    maxWidth: 2.5,
    throttle: 10,
    velocityFilterWeight: 0.7
});
window.addEventListener('resize', resizeCanvas);
function resizeCanvas() {
    const ratio =  Math.max(window.devicePixelRatio || 1, 1);
    canvas.width = 320 * ratio;
    canvas.height = 120 * ratio;
    canvas.getContext('2d').scale(ratio, ratio);
    signaturePad.clear();
}
resizeCanvas();
document.getElementById('clear-signature').onclick = function() {
    signaturePad.clear();
};
document.getElementById('save-signature').onclick = function() {
    if (signaturePad.isEmpty()) {
        document.getElementById('assinatura-info').textContent = 'Por favor, forneça uma assinatura.';
        return;
    }
    const dataURL = signaturePad.toDataURL('image/png');
    fetch('salvar-assinatura-eletronica.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: <?= $id ?>, assinatura: dataURL })
    })
    .then(r => r.json())
    .then(resp => {
        if (resp.success) {
            document.getElementById('assinatura-info').textContent = 'Assinatura salva! Recarregando...';
            setTimeout(() => location.reload(), 1200);
        } else {
            document.getElementById('assinatura-info').textContent = 'Erro ao salvar assinatura.';
        }
    })
    .catch(() => {
        document.getElementById('assinatura-info').textContent = 'Erro ao salvar assinatura.';
    });
};
<?php endif; ?>
</script>
</body>
</html> 
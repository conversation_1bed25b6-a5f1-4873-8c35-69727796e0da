<?php
require_once 'conexao.php';

echo "<h2>Teste - Produtos Vinculados vs Todos os Produtos</h2>";

// 1. Buscar uma pessoa com EPIs
$sql_pessoa = "
    SELECT DISTINCT p.id, p.nome 
    FROM pessoas p
    INNER JOIN pessoa_epi pe ON p.id = pe.pessoa_id
    WHERE p.status = 'ativo'
    LIMIT 1
";

$result = $conn->query($sql_pessoa);

if ($result && $result->num_rows > 0) {
    $pessoa = $result->fetch_assoc();
    $pessoa_id = $pessoa['id'];
    $pessoa_nome = $pessoa['nome'];
    
    echo "<h3>👤 Pessoa de Teste: {$pessoa_nome} (ID: {$pessoa_id})</h3>";
    
    // 2. Testar endpoint de produtos vinculados
    echo "<h4>🔗 Produtos Vinculados (obter-produtos-pessoa.php):</h4>";
    echo "<p><strong>Endpoint:</strong> <a href='obter-produtos-pessoa.php?pessoa_id={$pessoa_id}' target='_blank'>obter-produtos-pessoa.php?pessoa_id={$pessoa_id}</a></p>";
    
    // Simular chamada do endpoint
    $sql_vinculados = "
        SELECT 
            pe.produto_id,
            p.nome,
            p.codigo,
            SUM(pe.quantidade) - COALESCE(SUM(pe.quantidade_devolvida), 0) as quantidade_disponivel
        FROM 
            pessoa_epi pe
        JOIN 
            produtos p ON pe.produto_id = p.codigo
        WHERE 
            pe.pessoa_id = ?
        GROUP BY 
            pe.produto_id, p.nome, p.codigo
        HAVING 
            quantidade_disponivel > 0
        ORDER BY 
            p.nome ASC
    ";
    
    $stmt = $conn->prepare($sql_vinculados);
    $stmt->bind_param("i", $pessoa_id);
    $stmt->execute();
    $result_vinculados = $stmt->get_result();
    
    if ($result_vinculados && $result_vinculados->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; background: #e8f5e8;'>";
        echo "<tr><th>Código</th><th>Nome</th><th>Qtd Disponível</th></tr>";
        while ($row = $result_vinculados->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['codigo']}</td>";
            echo "<td>{$row['nome']}</td>";
            echo "<td>{$row['quantidade_disponivel']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'><strong>✅ Estes são os produtos que DEVEM aparecer na aba 'Devolução por Funcionário'</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Nenhum produto vinculado encontrado</p>";
    }
    
    // 3. Testar endpoint de todos os produtos
    echo "<h4>📦 Todos os Produtos (obter-todos-produtos.php):</h4>";
    echo "<p><strong>Endpoint:</strong> <a href='obter-todos-produtos.php' target='_blank'>obter-todos-produtos.php</a></p>";
    
    $sql_todos = "SELECT codigo, nome, categoria, quantidade FROM produtos ORDER BY nome ASC";
    $result_todos = $conn->query($sql_todos);
    
    if ($result_todos && $result_todos->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; background: #fff3cd;'>";
        echo "<tr><th>Código</th><th>Nome</th><th>Categoria</th><th>Estoque</th></tr>";
        while ($row = $result_todos->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['codigo']}</td>";
            echo "<td>{$row['nome']}</td>";
            echo "<td>{$row['categoria']}</td>";
            echo "<td>{$row['quantidade']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: orange;'><strong>⚠️ Estes produtos devem aparecer APENAS na aba 'Devolução Rápida' (popup)</strong></p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Nenhuma pessoa com EPIs encontrada</p>";
    echo "<p><a href='criar-dados-teste-devolucao.php'>Criar dados de teste</a></p>";
}

echo "<h3>🔧 Correção Aplicada:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Problema identificado:</strong> Duas funções JavaScript com o mesmo nome <code>renderizarTabelaProdutos()</code></p>";
echo "<ul>";
echo "<li>✅ <strong>Função 1 (linha 648):</strong> <code>renderizarTabelaProdutos()</code> - Para produtos vinculados ao funcionário</li>";
echo "<li>✅ <strong>Função 2 (linha 805):</strong> <code>renderizarTabelaProdutosPopup()</code> - Para todos os produtos no popup</li>";
echo "</ul>";
echo "<p><strong>Solução:</strong> Renomeada a segunda função para evitar conflito</p>";
echo "</div>";

echo "<h3>🧪 Como Testar:</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Aba 'Devolução por Funcionário':</strong>";
echo "<ul>";
echo "<li>Selecionar funcionário: {$pessoa_nome}</li>";
echo "<li>Deve mostrar APENAS os produtos vinculados (tabela verde acima)</li>";
echo "<li>NÃO deve mostrar todos os produtos</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Aba 'Devolução Rápida':</strong>";
echo "<ul>";
echo "<li>Clicar na lupa do produto</li>";
echo "<li>Deve mostrar TODOS os produtos (tabela amarela acima)</li>";
echo "<li>Permite selecionar qualquer produto</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Testar Correção</a>";
echo "</p>";

$conn->close();
?>

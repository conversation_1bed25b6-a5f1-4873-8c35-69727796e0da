<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Verificar e criar colunas de data se necessário
$check_requisicoes_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE 'data_solicitacao'");
if ($check_requisicoes_data->num_rows === 0) {
    $conn->query("ALTER TABLE requisicoes ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP");
}

$check_pedidos_mensais_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE 'data_solicitacao'");
if ($check_pedidos_mensais_data->num_rows === 0) {
    $conn->query("ALTER TABLE pedidos_mensais ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP");
}

$check_pedidos_especiais_data = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE 'data_solicitacao'");
if ($check_pedidos_especiais_data->num_rows === 0) {
    $conn->query("ALTER TABLE pedidos_especiais ADD COLUMN data_solicitacao DATETIME DEFAULT CURRENT_TIMESTAMP");
}

// Atualizar registros existentes que não têm data
$conn->query("UPDATE requisicoes SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL");
$conn->query("UPDATE pedidos_mensais SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL");
$conn->query("UPDATE pedidos_especiais SET data_solicitacao = NOW() WHERE data_solicitacao IS NULL");

// Inicializar variáveis de feedback
$feedbackMessage = "";
$feedbackType = "";

// Verificar se há mensagens de status na URL
if (isset($_GET['status'])) {
    if ($_GET['status'] == 'delete-success') {
        $feedbackMessage = "Solicitação cancelada com sucesso!";
        $feedbackType = "delete-success";
    } elseif ($_GET['status'] == 'delete-error') {
        $feedbackMessage = "Erro ao cancelar a solicitação.";
        if (isset($_GET['message'])) {
            $feedbackMessage .= " " . $_GET['message'];
        }
        $feedbackType = "error";
    } elseif ($_GET['status'] == 'concluido') {
        $feedbackMessage = "Solicitação concluída com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'update-success') {
        $feedbackMessage = "Solicitação atualizada com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'update-error') {
        $feedbackMessage = "Erro ao atualizar a solicitação.";
        if (isset($_GET['message'])) {
            $feedbackMessage .= " " . $_GET['message'];
        }
        $feedbackType = "error";
    }
}

// Verificar se as tabelas têm a coluna status
$check_requisicoes = $conn->query("SHOW COLUMNS FROM requisicoes WHERE Field = 'status'");
$check_pedidos_mensais = $conn->query("SHOW COLUMNS FROM pedidos_mensais WHERE Field = 'status'");
$check_pedidos_especiais = $conn->query("SHOW COLUMNS FROM pedidos_especiais WHERE Field = 'status'");

// Definir colunas de status padrão
$status_requisicoes = "'pendente' as status";
$status_pedidos_mensais = "'pendente' as status";
$status_pedidos_especiais = "'pendente' as status";

// Verificar se encontramos colunas de status nas tabelas
if ($check_requisicoes && $check_requisicoes->num_rows > 0) {
    $status_requisicoes = "r.status as status";
}

if ($check_pedidos_mensais && $check_pedidos_mensais->num_rows > 0) {
    $status_pedidos_mensais = "p.status as status";
}

if ($check_pedidos_especiais && $check_pedidos_especiais->num_rows > 0) {
    $status_pedidos_especiais = "p.status as status";
}

// Obter filtros de data
$data_inicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : '';
$data_fim = isset($_GET['data_fim']) ? $_GET['data_fim'] : '';
// Obter filtro de status
$status_filter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'pendente';

// Filtrar por solicitante se não for administrador
$usuario_tipo = $_SESSION['tipo_usuario'] ?? '';
$usuario_nome = $_SESSION['nome_usuario'] ?? '';

$where_solicitante_requisicoes = '';
$where_solicitante_pedidos_mensais = '';
$where_solicitante_pedidos_especiais = '';
if ($usuario_tipo !== 'administrador') {
    $where_solicitante_requisicoes = (empty($where_data_requisicoes) ? ' WHERE ' : ' AND ') . "solicitante = '" . $conn->real_escape_string($usuario_nome) . "'";
    $where_solicitante_pedidos_mensais = (empty($where_data_pedidos_mensais) ? ' WHERE ' : ' AND ') . "solicitante = '" . $conn->real_escape_string($usuario_nome) . "'";
    $where_solicitante_pedidos_especiais = (empty($where_data_pedidos_especiais) ? ' WHERE ' : ' AND ') . "solicitante = '" . $conn->real_escape_string($usuario_nome) . "'";
}

// Construir condições de data
$where_data_requisicoes = "";
$where_data_pedidos_mensais = "";
$where_data_pedidos_especiais = "";

if (!empty($data_inicio) && !empty($data_fim)) {
    $where_data_requisicoes = " WHERE data_solicitacao BETWEEN '$data_inicio 00:00:00' AND '$data_fim 23:59:59'";
    $where_data_pedidos_mensais = " WHERE data_solicitacao BETWEEN '$data_inicio 00:00:00' AND '$data_fim 23:59:59'";
    $where_data_pedidos_especiais = " WHERE data_solicitacao BETWEEN '$data_inicio 00:00:00' AND '$data_fim 23:59:59'";
} elseif (!empty($data_inicio)) {
    $where_data_requisicoes = " WHERE data_solicitacao >= '$data_inicio 00:00:00'";
    $where_data_pedidos_mensais = " WHERE data_solicitacao >= '$data_inicio 00:00:00'";
    $where_data_pedidos_especiais = " WHERE data_solicitacao >= '$data_inicio 00:00:00'";
} elseif (!empty($data_fim)) {
    $where_data_requisicoes = " WHERE data_solicitacao <= '$data_fim 23:59:59'";
    $where_data_pedidos_mensais = " WHERE data_solicitacao <= '$data_fim 23:59:59'";
    $where_data_pedidos_especiais = " WHERE data_solicitacao <= '$data_fim 23:59:59'";
}

// Construir condições de status
$where_status_requisicoes = '';
$where_status_pedidos_mensais = '';
$where_status_pedidos_especiais = '';
if (!empty($status_filter) && $status_filter !== 'todos') {
    $where_status_requisicoes = (empty($where_data_requisicoes) && empty($where_solicitante_requisicoes) ? ' WHERE ' : (empty($where_solicitante_requisicoes) ? ' AND ' : ' AND ')) . "r.status = '" . $conn->real_escape_string($status_filter) . "'";
    $where_status_pedidos_mensais = (empty($where_data_pedidos_mensais) && empty($where_solicitante_pedidos_mensais) ? ' WHERE ' : (empty($where_solicitante_pedidos_mensais) ? ' AND ' : ' AND ')) . "p.status = '" . $conn->real_escape_string($status_filter) . "'";
    $where_status_pedidos_especiais = (empty($where_data_pedidos_especiais) && empty($where_solicitante_pedidos_especiais) ? ' WHERE ' : (empty($where_solicitante_pedidos_especiais) ? ' AND ' : ' AND ')) . "p.status = '" . $conn->real_escape_string($status_filter) . "'";
}

// Consulta para obter requisições normais
$sql_requisicoes = "SELECT
    r.codigo_solicitacao as codigo,
    'requisicao' as tipo,
    r.solicitante,
    r.empresa,
    e.nome_empresa,
    r.contrato,
    r.finalidade,
    r.funcionario,
    r.funcao,
    r.observacao,
    r.data_solicitacao,
    r.requisicao_urgente,
    r.motivo_urgente,
    $status_requisicoes
FROM requisicoes r
LEFT JOIN empresas e ON r.empresa = e.codigo_empresa" . $where_data_requisicoes . $where_solicitante_requisicoes . $where_status_requisicoes;

// Consulta para obter pedidos mensais
$sql_pedidos_mensais = "SELECT
    p.codigo_pedido as codigo,
    'pedido_mensal' as tipo,
    p.solicitante,
    p.empresa,
    e.nome_empresa,
    p.contrato,
    p.finalidade,
    p.destinatario as funcionario,
    p.funcao,
    p.observacao,
    p.data_solicitacao,
    p.pedido_urgente as requisicao_urgente,
    p.motivo_urgente,
    $status_pedidos_mensais
FROM pedidos_mensais p
LEFT JOIN empresas e ON p.empresa = e.codigo_empresa" . $where_data_pedidos_mensais . $where_solicitante_pedidos_mensais . $where_status_pedidos_mensais;

// Consulta para obter pedidos especiais
$sql_pedidos_especiais = "SELECT
    p.codigo_pedido as codigo,
    'pedido_especial' as tipo,
    p.solicitante,
    p.empresa,
    e.nome_empresa,
    p.contrato,
    p.finalidade,
    '' as funcionario,
    '' as funcao,
    p.detalhes as observacao,
    p.data_solicitacao,
    0 as requisicao_urgente,
    '' as motivo_urgente,
    $status_pedidos_especiais
FROM pedidos_especiais p
LEFT JOIN empresas e ON p.empresa = e.codigo_empresa" . $where_data_pedidos_especiais . $where_solicitante_pedidos_especiais . $where_status_pedidos_especiais;

// Combinar as consultas com UNION
$sql = "($sql_requisicoes) UNION ($sql_pedidos_mensais) UNION ($sql_pedidos_especiais) ORDER BY data_solicitacao DESC";

$result = $conn->query($sql);

// Verificar se a consulta foi bem-sucedida
if (!$result) {
    die("Erro na consulta: " . $conn->error);
}

// Separar solicitações pendentes e concluídas
$solicitacoes_pendentes = [];
$solicitacoes_concluidas = [];

while ($row = $result->fetch_assoc()) {
    if (isset($row['status']) && $row['status'] == 'concluido') {
        $solicitacoes_concluidas[] = $row;
    } else {
        $solicitacoes_pendentes[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitações Realizadas</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.12);
            position: relative;
            z-index: 1;
        }
        h1, h2.section-title {
            font-size: 1.7rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 28px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 15px;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            border: 1px solid #e5e7eb;
        }
        th, td {
            border-bottom: 1px solid #e5e7eb;
            padding: 14px 16px;
            text-align: left;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 15px;
            border: none;
        }
        tr:last-child td {
            border-bottom: none;
        }
        tr:hover {
            background: #f9fafb;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.5px;
            vertical-align: middle;
            background: #d1fae5;
            color: #047857;
            border: none;
            box-shadow: none;
            text-transform: uppercase;
            transition: background 0.2s, color 0.2s;
        }
        .status-pendente {
            background: #fef3c7 !important;
            color: #b45309 !important;
        }
        .status-concluido, .status-ativo {
            background: #d1fae5 !important;
            color: #047857 !important;
        }
        .status-cancelada, .status-inativo, .status-cancelado {
            background: #fee2e2 !important;
            color: #b91c1c !important;
        }
        /* Estilo para solicitações urgentes (limite excedido) */
        .solicitacao-urgente {
            background-color: #fee2e2 !important;
        }
        .solicitacao-urgente:hover {
            background-color: #fecaca !important;
        }
        /* Estilo para textarea com informação de urgência */
        .observacao-urgente {
            background-color: #fef2f2 !important;
            border: 2px solid #fca5a5 !important;
            color: #991b1b !important;
            font-weight: 500 !important;
        }
        .btn, .btn-info, .btn-primary, .btn-delete {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 10px 18px;
            font-size: 15px;
            font-weight: 600;
            margin-top: 0;
            margin-right: 5px;
            cursor: pointer;
            transition: background 0.2s;
            box-shadow: none;
        }
        .btn-info { background: #2563eb; }
        .btn-info:hover { background: #1d4ed8; }
        .btn-primary { background: #22c55e; }
        .btn-primary:hover { background: #16a34a; }
        .btn-delete { background: #e53935; }
        .btn-delete:hover { background: #b71c1c; }
        .btn-cancel, .btn-cancelar {
            background: #e5e7eb;
            color: #222;
        }
        .btn-cancel:hover, .btn-cancelar:hover {
            background: #d1d5db;
        }
        /* Popup de detalhes */
        .popup-overlay, .confirm-overlay, .quantidade-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 99999;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .popup, .confirm-popup, .quantidade-popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            max-width: 600px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100001;
            display: none;
            flex-direction: column;
        }
        .popup.active, .popup[style*="display: flex"],
        .confirm-popup.active, .confirm-popup[style*="display: flex"],
        .quantidade-popup.active, .quantidade-popup[style*="display: flex"] {
            display: flex !important;
        }
        .popup h3 {
            margin: 0 0 18px 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
            text-align: left;
        }
        .popup .form-group label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .popup input, .popup textarea {
            width: 100%;
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            margin-bottom: 10px;
        }
        .popup input[readonly], .popup textarea[readonly] {
            background: #f8fafc;
            color: #6b7280;
        }
        .popup .form-group {
            margin-bottom: 15px;
        }
        .popup .form-group:last-child {
            margin-bottom: 0;
        }
        .popup .form-group span.status-badge {
            margin-left: 0;
        }
        .popup .form-group strong {
            color: #2563eb;
        }
        .popup .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }
        .popup .btn {
            margin-top: 0;
        }
        /* Ajuste para preview de imagem */
        .imagem-preview {
            margin-top: 10px;
            border: 1.5px solid #e5e7eb;
            padding: 10px;
            border-radius: 4px;
            background-color: #f9f9f9;
            text-align: center;
        }
        #imagem_pedido {
            max-width: 100%;
            max-height: 400px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .sem-imagem {
            padding: 20px;
            text-align: center;
            color: #777;
            font-style: italic;
        }
        /* Tabela de itens no popup */
        .itens-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background: #fafbfc;
            border-radius: 8px;
            overflow: hidden;
        }
        .itens-table th, .itens-table td {
            border: none;
            padding: 10px 8px;
            text-align: left;
            font-size: 14px;
        }
        .itens-table th {
            background: #f4f6fa;
            color: #2563eb;
            font-weight: 600;
        }
        .itens-table tr {
            border-bottom: 1px solid #f0f1f3;
        }
        .itens-table tr:last-child {
            border-bottom: none;
        }
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
            .popup {
                padding: 18px 6px;
            }
        }
        #confirmOverlay, #quantidadeOverlay, #produtosPopupOverlay, #produtosPopup {
            /* display: none !important; */
        }
        #confirmOverlay.active, #quantidadeOverlay.active, #produtosPopupOverlay.active, #produtosPopup.active {
            display: flex !important;
        }
        /* Estilos para filtros */
        .filtros-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .filtro-grupo {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .filtro-linha-datas {
            display: flex;
            align-items: flex-end;
            gap: 24px;
        }
        .filtro-linha-datas .filtro-grupo {
            margin-bottom: 0;
            min-width: 170px;
            max-width: 200px;
        }
        .filtro-linha-datas .filtro-grupo input[type="date"] {
            width: 100%;
            min-width: 140px;
            max-width: 200px;
        }
        .filtro-linha-datas button.btn-cancel {
            height: 38px;
            align-self: flex-start;
            margin-bottom: 0;
            min-width: 120px;
            max-width: 160px;
        }
        .filtro-grupo label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }
        .filtro-grupo select,
        .filtro-grupo input[type="date"] {
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            min-width: 150px;
        }
        .filtro-grupo select:focus,
        .filtro-grupo input[type="date"]:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        /* Pop-up de feedback centralizado */
        #feedback-popup-modal {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 110000;
            justify-content: center;
            align-items: center;
        }
        #feedback-popup-modal[style*="display: flex"] {
            display: flex !important;
        }
        #feedback-popup-modal .popup {
            position: static;
            margin: 0 auto;
            top: auto;
            left: auto;
            transform: none;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
        }
        /* Copiado de requisicoes.php para o popup de produtos */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active {
            display: flex;
        }
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
            margin-top: 0;
        }
        .popup-header h3 {
            margin: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        .popup-close {
            position: absolute;
            right: 18px;
            top: 8px;
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            transition: background 0.2s, color 0.2s;
        }
        .popup-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .popup-content {
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: flex-start !important;
        }
        .popup-search {
            width: 100%;
            max-width: 320px;
            margin: 0 auto 18px auto;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 15px;
            background: #f8fafc;
            color: #222;
            outline: none;
            transition: border 0.2s;
            display: block;
        }
        .popup-search:focus {
            border-color: #2563eb;
            background: #fff;
        }
        .popup-table-container {
            width: 100%;
            overflow-x: auto;
        }
        .popup-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        .popup-table th, .popup-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #f3f4f6;
            text-align: left;
        }
        .popup-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
        }
        .popup-table tr:last-child td {
            border-bottom: none;
        }
        .popup-table tr:hover {
            background: #f9fafb;
        }
        .popup-table button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 6px 14px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .popup-table button:hover {
            background: #1d4ed8;
        }
        /* Centralização absoluta apenas para o popup de produtos e detalhes */
        #popup {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 100002;
        }
        #popupProdutosOverlay {
            z-index: 120000;
        }
        #popupProdutos {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            z-index: 120001;
            max-width: 500px;
            width: 98vw;
            max-height: 600px !important;
            overflow-y: auto;
        }
        @media (max-width: 768px) {
            #popupProdutos {
                padding: 10px 2vw !important;
                max-width: 99vw;
                max-height: 98vh;
            }
        }
        /* Garante altura do popup de selecionar produto */
        #popupProdutos {
            max-height: 600px !important;
        }
        /* Estilos para o popup de filtros */
        .filter-btn {
            background: none;
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #222;
            cursor: pointer;
            transition: background 0.2s;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
        }
        .filter-btn:hover {
            background: #f3f4f6;
        }
        .filter-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99998;
            display: none;
        }
        .filter-popup-overlay.active {
            display: flex;
        }
        .filter-popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            max-width: 600px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 99999;
            display: none;
            flex-direction: column;
        }
        .filter-popup.active, .filter-popup[style*="display: flex"] {
            display: flex !important;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .filter-popup-header span {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            transition: background 0.2s, color 0.2s;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .filter-popup-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .filter-popup-group {
            display: flex;
            flex-direction: column;
        }
        .filter-popup-group label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .filter-popup-input {
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            min-width: 200px;
        }
        .filter-popup-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .filter-popup-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .btn-modern {
            padding: 10px 18px;
            font-size: 14px;
            font-weight: 600;
            border-radius: 6px;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .btn-confirm {
            background: #2563eb;
            color: #fff;
            border: none;
        }
        .btn-confirm:hover {
            background: #1d4ed8;
        }
        .btn-cancel {
            background: #f44336;
            color: #fff;
            border: none;
        }
        .btn-cancel:hover {
            background: #d32f2f;
        }
        .filter-btn i.fas {
            color: #222 !important;
            font-size: 20px !important;
        }
        .section-title {
            text-align: left !important;
            font-size: 1.15rem !important;
            font-weight: 600;
            color: #111827;
            margin-bottom: 18px;
            margin-top: 56px !important;
        }
        .main-title {
            font-size: 1.65rem !important;
            font-weight: 700;
            color: #111827;
            text-align: center;
            margin-bottom: 28px;
        }

        /* Estilos para aviso de pedido urgente */
        .aviso-urgente {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
        }

        .aviso-urgente-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .aviso-urgente-icon {
            font-size: 18px;
            color: #dc2626;
        }

        .aviso-urgente-texto {
            color: #dc2626;
            font-weight: 600;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <!-- Pop-up de feedback -->
        <!-- BLOCO REMOVIDO: feedback-popup antigo -->
        
        <h1 class="main-title" style="display: flex; align-items: center; justify-content: center; gap: 12px; position: relative;">
            <span style="flex:1; text-align:center;">Meus Pedidos</span>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;">
                <i class="fas fa-filter"></i>
            </button>
        </h1>

        <!-- Popup de filtros moderno -->
        <div id="filterPopupOverlay" class="filter-popup-overlay" style="display:none;"></div>
        <div id="filterPopup" class="filter-popup" style="display:none;">
            <div class="filter-popup-header">
                <span>Filtrar por:</span>
                <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
            </div>
            <form method="get" class="filter-popup-form">
                <div class="filter-popup-group">
                    <label for="status_filter">Status:</label>
                    <select id="status_filter" name="status_filter" class="filter-popup-input">
                        <option value="todos" <?= ($status_filter == 'todos') ? 'selected' : '' ?>>Todos</option>
                        <option value="pendente" <?= ($status_filter == 'pendente' || $status_filter == '' ? 'selected' : '') ?>>Pendente</option>
                        <option value="concluido" <?= ($status_filter == 'concluido') ? 'selected' : '' ?>>Concluído</option>
                        <option value="cancelada" <?= ($status_filter == 'cancelada') ? 'selected' : '' ?>>Cancelada</option>
                    </select>
                </div>
                <div class="filter-popup-group">
                    <label for="tipo_filter">Tipo:</label>
                    <select id="tipo_filter" name="tipo_filter" class="filter-popup-input">
                        <option value="todos" selected>Todos</option>
                        <option value="requisicao">Requisição</option>
                        <option value="pedido_mensal">Pedido Mensal</option>
                        <option value="pedido_especial">Pedido Especial</option>
                    </select>
                </div>
                <div class="filter-popup-group">
                    <label for="data_inicio_popup">Data de início:</label>
                    <input type="date" id="data_inicio_popup" name="data_inicio" class="filter-popup-input" value="<?= htmlspecialchars($data_inicio) ?>">
                </div>
                <div class="filter-popup-group">
                    <label for="data_fim_popup">Data de fim:</label>
                    <input type="date" id="data_fim_popup" name="data_fim" class="filter-popup-input" value="<?= htmlspecialchars($data_fim) ?>">
                </div>
                <div class="filter-popup-actions">
                    <a href="requisicoes-feitas.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                    <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
                </div>
            </form>
        </div>

        <!-- Seção de Solicitações Pendentes -->
        <?php if ($status_filter === 'pendente' || $status_filter === 'todos' || $status_filter === ''): ?>
            <h2 class="section-title">Solicitações Pendentes</h2>
            <table>
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Tipo</th>
                        <th>Solicitante</th>
                        <th>Empresa</th>
                        <th>Contrato</th>
                        <th>Data</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="pendentes-tbody">
                    <?php if (empty($solicitacoes_pendentes)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center;">Nenhuma solicitação pendente encontrada</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($solicitacoes_pendentes as $row):
                            $tipo = $row['tipo'];
                            $tipoClass = 'tipo-' . $tipo;
                            $status = isset($row['status']) ? $row['status'] : 'pendente';
                            $statusClass = 'status-' . $status;
                            $tipoTexto = '';
                            switch($tipo) {
                                case 'requisicao': $tipoTexto = 'Requisição'; break;
                                case 'pedido_mensal': $tipoTexto = 'Pedido Mensal'; break;
                                case 'pedido_especial': $tipoTexto = 'Pedido Especial'; break;
                            }
                            $statusTexto = ucfirst($status);
                            $dataFormatada = isset($row['data_solicitacao']) ? date('d/m/Y H:i', strtotime($row['data_solicitacao'])) : 'N/A';

                            // Verificar se é urgente (limite excedido)
                            $isUrgente = false;
                            if (isset($row['requisicao_urgente']) && $row['requisicao_urgente'] == 1) {
                                $isUrgente = true;
                            }
                            $classeUrgente = $isUrgente ? 'solicitacao-urgente' : '';
                        ?>
                            <tr class="item-<?= $tipo ?> <?= $classeUrgente ?>" data-tipo="<?= $tipo ?>" data-status="<?= $status ?>">
                                <td><?= $row['codigo'] ?></td>
                                <td><span class="<?= $tipoClass ?>"><?= $tipoTexto ?></span></td>
                                <td><?= $row['solicitante'] ?></td>
                                <td><?= $row['empresa'] ?></td>
                                <td><?= $row['contrato'] ?></td>
                                <td><?= $dataFormatada ?></td>
                                <td><span class="status-badge <?= $statusClass ?>"><?= $statusTexto ?></span></td>
                                <td>
                                    <button class="btn btn-info" onclick="mostrarDetalhes('<?= $tipo ?>', <?= $row['codigo'] ?>)">Informações</button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        <?php endif; ?>

        <!-- Divisor entre seções -->
        <div class="section-divider"></div>
        
        <!-- Seção de Solicitações Concluídas -->
        <?php if ($status_filter === 'concluido' || $status_filter === 'todos' || $status_filter === ''): ?>
            <h2 class="section-title">Solicitações Concluídas</h2>
            <table>
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Tipo</th>
                        <th>Solicitante</th>
                        <th>Empresa</th>
                        <th>Contrato</th>
                        <th>Data</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody id="concluidas-tbody">
                    <?php if (empty($solicitacoes_concluidas)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center;">Nenhuma solicitação concluída encontrada</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($solicitacoes_concluidas as $row):
                            $tipo = $row['tipo'];
                            $tipoClass = 'tipo-' . $tipo;
                            $status = isset($row['status']) ? $row['status'] : 'concluido';
                            $statusClass = 'status-' . $status;
                            $tipoTexto = '';
                            switch($tipo) {
                                case 'requisicao': $tipoTexto = 'Requisição'; break;
                                case 'pedido_mensal': $tipoTexto = 'Pedido Mensal'; break;
                                case 'pedido_especial': $tipoTexto = 'Pedido Especial'; break;
                            }
                            $statusTexto = ucfirst($status);
                            $dataFormatada = isset($row['data_solicitacao']) ? date('d/m/Y H:i', strtotime($row['data_solicitacao'])) : 'N/A';

                            // Verificar se é urgente (limite excedido)
                            $isUrgente = false;
                            if (isset($row['requisicao_urgente']) && $row['requisicao_urgente'] == 1) {
                                $isUrgente = true;
                            }
                            $classeUrgente = $isUrgente ? 'solicitacao-urgente' : '';
                        ?>
                            <tr class="item-<?= $tipo ?> <?= $classeUrgente ?>" data-tipo="<?= $tipo ?>" data-status="<?= $status ?>">
                                <td><?= $row['codigo'] ?></td>
                                <td><span class="<?= $tipoClass ?>"><?= $tipoTexto ?></span></td>
                                <td><?= $row['solicitante'] ?></td>
                                <td><?= $row['empresa'] ?></td>
                                <td><?= $row['contrato'] ?></td>
                                <td><?= $dataFormatada ?></td>
                                <td><span class="status-badge <?= $statusClass ?>"><?= $statusTexto ?></span></td>
                                <td>
                                    <button class="btn btn-info" onclick="mostrarDetalhes('<?= $tipo ?>', <?= $row['codigo'] ?>)">Informações</button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        <?php endif; ?>

        <!-- Pop-up de detalhes da solicitação -->
        <div id="popupOverlay" class="popup-overlay"></div>
        <div id="popup" class="popup">
            <div style="display:flex; justify-content:flex-end; align-items:center; height:32px; margin-top:0;">
                <button class="popup-close" onclick="fecharPopup()" style="background:none; border:none; font-size:24px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; transition:background 0.2s, color 0.2s;">&times;</button>
            </div>
            <div class="popup-header" style="display:flex; align-items:center; justify-content:space-between; border-bottom:1px solid #e5e7eb; padding-bottom:10px; margin-bottom:18px;">
                <h2 id="popup-title" style="margin:0; font-size:22px; font-weight:600; color:#222; flex:1 1 auto;">Detalhes da Solicitação</h2>
                <button id="btn-editar-popup" type="button" title="Editar" style="background:none; border:none; padding:2px; border-radius:4px; cursor:pointer; display:flex; align-items:center; justify-content:center; margin-left:16px; width:40px; height:40px;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#374151" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 1 1 3 3L7 19.5 3 21l1.5-4L16.5 3.5z"/></svg>
                </button>
            </div>

            <!-- Aviso de Pedido Urgente -->
            <div id="aviso-pedido-urgente" class="aviso-urgente" style="display: none;">
                <div class="aviso-urgente-content">
                    <span class="aviso-urgente-icon">🚨</span>
                    <span class="aviso-urgente-texto">Pedido urgente após valor limite excedido</span>
                </div>
            </div>

            <form id="formDetalhes" action="atualizar-solicitacao.php" method="POST" autocomplete="off">
                <input type="hidden" id="tipo_solicitacao" name="tipo_solicitacao">
                <input type="hidden" id="codigo_solicitacao" name="codigo_solicitacao">
                <input type="hidden" id="modo_edicao" name="modo_edicao" value="0">
                <div style="display:flex; gap:18px; margin-bottom:18px;">
                    <div style="flex:0.6; display:flex; flex-direction:column; margin-right:16px;">
                        <label for="codigo">Código:</label>
                        <input type="text" id="codigo" name="codigo" readonly style="width:100%;">
                    </div>
                    <div style="flex:1.2; display:flex; flex-direction:column;">
                        <label for="solicitante">Solicitante:</label>
                        <input type="text" id="solicitante" name="solicitante" readonly style="width:90%;">
                    </div>
                </div>
                <div class="form-group">
                    <label for="empresa">Empresa:</label>
                    <input type="text" id="empresa" name="empresa" readonly style="max-width: 375px;">
                </div>
                <div class="form-group">
                    <label for="contrato">Contrato:</label>
                    <input type="text" id="contrato" name="contrato" readonly style="max-width: 375px;">
                </div>
                <div class="form-group">
                    <label for="finalidade">Finalidade:</label>
                    <input type="text" id="finalidade" name="finalidade" readonly style="max-width: 375px;">
                </div>
                <div class="form-group" id="funcionario_group">
                    <label for="funcionario">Funcionário/Destinatário:</label>
                    <input type="text" id="funcionario" name="funcionario" readonly style="max-width: 375px;">
                </div>
                <div class="form-group" id="funcao_group">
                    <label for="funcao">Função:</label>
                    <input type="text" id="funcao" name="funcao" readonly style="max-width: 375px;">
                </div>
                <div class="form-group">
                    <label for="observacao">Observação/Detalhes:</label>
                    <textarea id="observacao" name="observacao" rows="3" readonly style="max-width: 375px;"></textarea>
                </div>
                <div class="form-group" id="status_group">
                    <label>Status:</label>
                    <span id="status_badge" class="status-badge"></span>
                </div>
                <div class="form-group" id="imagem_container" style="display: none;">
                    <label>Imagem:</label>
                    <div class="imagem-preview">
                        <img id="imagem_pedido" src="" alt="Imagem do pedido especial" style="max-width: 100%; max-height: 400px;">
                    </div>
                </div>
                <div id="itens_container">
                    <h4>Itens da Solicitação</h4>
                    <button type="button" id="btn-adicionar-item" style="display: none; background:#22c55e; color:#fff; margin-top:24px; margin-bottom:10px;" onclick="adicionarNovoItem()">Adicionar Item</button>
                    <table id="itensTable" class="itens-table">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th id="acoes_header" style="display: none;">Ações</th>
                            </tr>
                        </thead>
                        <tbody id="itensTableBody">
                            <!-- Itens serão adicionados aqui via JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div id="cancelar_solicitacao_container" style="margin-top: 18px; text-align: right;">
                    <button type="button" id="btn-cancelar-solicitacao" style="background:#e53935; color:#fff; font-weight:600; padding:12px 28px; border-radius:8px; font-size:16px; border:none; margin-top:10px; display:none;" onclick="abrirConfirmarCancelamento()">Cancelar Solicitação</button>
                </div>
                <div style="margin-top: 30px; text-align: right; display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" id="btn-salvar" style="display: none; background:#22c55e; color:#fff;" class="btn btn-primary" onclick="salvarAlteracoes()">Salvar Alterações</button>
                    <button type="button" id="btn-cancelar-edicao" style="display: none; background:#e53935; color:#fff;" class="btn btn-cancel" onclick="cancelarEdicao()">Cancelar</button>
                </div>
            </form>
        </div>

        <!-- Pop-up de edição de produto -->
        <div class="popup-overlay" id="popupProdutosOverlay">
            <div class="popup" id="popupProdutos">
                <div class="popup-header">
                    <h3>Selecionar Produto</h3>
                    <button class="popup-close" onclick="fecharPopupProdutos()">&times;</button>
                </div>
                <div class="popup-content">
                    <input type="text" class="popup-search" id="pesquisaProduto" placeholder="Pesquisar produto..." oninput="filtrarProdutosPopup()">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="listaProdutosPopup">
                                <!-- Conteúdo dinâmico via JS -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pop-up de confirmação de cancelamento -->
        <div id="confirmOverlay" class="confirm-overlay" style="z-index:300000 !important;">
            <div class="confirm-popup" style="z-index:300001 !important;">
                <h3>Confirmar Cancelamento</h3>
                <p>Tem certeza que deseja cancelar esta solicitação? Esta ação não pode ser desfeita.</p>
                <div class="confirm-buttons">
                    <button id="confirmDelete" class="btn btn-confirm">Sim, Cancelar</button>
                    <button id="cancelDelete" class="btn btn-cancel">Não, Voltar</button>
                </div>
            </div>
        </div>

        <!-- Formulário oculto para cancelamento -->
        <form id="deleteForm" action="cancelar-solicitacao.php" method="POST" style="display: none;">
            <input type="hidden" id="delete_tipo" name="delete_tipo" value="">
            <input type="hidden" id="delete_id" name="delete_id" value="">
        </form>

        <!-- Pop-up para inserir quantidade -->
        <div id="quantidadeOverlay" class="quantidade-overlay">
            <div class="quantidade-popup">
                <h3>Informe a Quantidade</h3>
                <input type="number" id="inputQuantidade" min="1" value="1">
                <div class="quantidade-buttons">
                    <button id="btnConfirmarQuantidade" class="btn-confirmar">Confirmar</button>
                    <button id="btnCancelarQuantidade" class="btn-cancelar">Cancelar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pop-up de feedback centralizado (deve ser o último elemento do body) -->
    <div id="feedback-popup-modal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(17,17,17,0.5); z-index:110000; justify-content:center; align-items:center;">
        <div class="popup" style="max-width:340px; text-align:center; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); display:flex; flex-direction:column; align-items:center; box-shadow:0 8px 32px rgba(0,0,0,0.18); background:#fff; padding:32px; border-radius:12px;">
            <h3 id="feedback-popup-title" style="margin-bottom:18px;"></h3>
            <p id="feedback-popup-message" style="margin-bottom:0; color:#444;"></p>
        </div>
    </div>

    <script>
        // Variáveis globais
        let itensSolicitacao = [];
        let itemEditIndex = -1;
        let solicitacaoAtual = null;
        let produtoSelecionado = null;
        
        // --- CONTROLE DE EDIÇÃO DO POPUP ---
        let backupCampos = {};
        document.getElementById('btn-editar-popup').addEventListener('click', function() {
            if (solicitacaoAtual && solicitacaoAtual.status === 'concluido') return;
            // Salvar backup dos campos
            backupCampos = {
                solicitante: document.getElementById('solicitante').value,
                empresa: document.getElementById('empresa').value,
                contrato: document.getElementById('contrato').value,
                finalidade: document.getElementById('finalidade').value,
                funcionario: document.getElementById('funcionario').value,
                funcao: document.getElementById('funcao').value,
                observacao: document.getElementById('observacao').value,
                itens: JSON.parse(JSON.stringify(itensSolicitacao))
            };
            document.getElementById('modo_edicao').value = '1';
            // Habilitar campos
            ['solicitante','empresa','contrato','finalidade','funcionario','funcao','observacao'].forEach(id => {
                const el = document.getElementById(id);
                if (el && id !== 'solicitante') el.readOnly = false;
                if (el && id === 'solicitante') el.readOnly = true;
            });
            // Mostrar botões de edição
            document.getElementById('btn-adicionar-item').style.display = 'block';
            document.getElementById('btn-salvar').style.display = 'inline-block';
            document.getElementById('btn-cancelar-edicao').style.display = 'inline-block';
            document.getElementById('acoes_header').style.display = 'table-cell';
            atualizarTabelaItensEdicao();
        });
        function cancelarEdicao() {
            document.getElementById('modo_edicao').value = '0';
            // Restaurar campos
            document.getElementById('solicitante').value = backupCampos.solicitante;
            document.getElementById('empresa').value = backupCampos.empresa;
            document.getElementById('contrato').value = backupCampos.contrato;
            document.getElementById('finalidade').value = backupCampos.finalidade;
            document.getElementById('funcionario').value = backupCampos.funcionario;
            document.getElementById('funcao').value = backupCampos.funcao;
            document.getElementById('observacao').value = backupCampos.observacao;
            itensSolicitacao = JSON.parse(JSON.stringify(backupCampos.itens));
            // Voltar para modo visual
            ['solicitante','empresa','contrato','finalidade','funcionario','funcao','observacao'].forEach(id => {
                const el = document.getElementById(id);
                if (el && id !== 'solicitante') el.readOnly = true;
                if (el && id === 'solicitante') el.readOnly = true;
            });
            document.getElementById('btn-adicionar-item').style.display = 'none';
            document.getElementById('btn-salvar').style.display = 'none';
            document.getElementById('btn-cancelar-edicao').style.display = 'none';
            document.getElementById('acoes_header').style.display = 'none';
            atualizarTabelaItensEdicao();
        }
        // Ao salvar, volta para modo visual (já está implementado em salvarAlteracoes)
        // Ajustar mostrarDetalhes para garantir modo visual ao abrir
        // Função para filtrar solicitações
        function filtrarSolicitacoes() {
            const tipo = document.getElementById('filtroTipo').value;
            
            // Filtrar solicitações pendentes
            const linhasPendentes = document.querySelectorAll('#pendentes-tbody tr');
            linhasPendentes.forEach(linha => {
                if (linha.classList.contains('no-results')) return;
                
                const linhaTipo = linha.getAttribute('data-tipo');
                if (tipo === 'todos' || linhaTipo === tipo) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
            
            // Verificar se há linhas visíveis na tabela de pendentes
            let temPendentesVisiveis = false;
            linhasPendentes.forEach(linha => {
                if (linha.style.display !== 'none' && !linha.classList.contains('no-results')) {
                    temPendentesVisiveis = true;
                }
            });
            
            // Se não houver linhas visíveis, mostrar mensagem
            const tbodyPendentes = document.getElementById('pendentes-tbody');
            const msgPendentes = tbodyPendentes.querySelector('.no-results');
            
            if (!temPendentesVisiveis) {
                if (!msgPendentes) {
                    const tr = document.createElement('tr');
                    tr.className = 'no-results';
                    tr.innerHTML = '<td colspan="8" style="text-align: center;">Nenhuma solicitação pendente encontrada para este filtro</td>';
                    tbodyPendentes.appendChild(tr);
                } else {
                    msgPendentes.style.display = '';
                }
            } else if (msgPendentes) {
                msgPendentes.style.display = 'none';
            }
            
            // Filtrar solicitações concluídas
            const linhasConcluidas = document.querySelectorAll('#concluidas-tbody tr');
            linhasConcluidas.forEach(linha => {
                if (linha.classList.contains('no-results')) return;
                
                const linhaTipo = linha.getAttribute('data-tipo');
                if (tipo === 'todos' || linhaTipo === tipo) {
                    linha.style.display = '';
                } else {
                    linha.style.display = 'none';
                }
            });
            
            // Verificar se há linhas visíveis na tabela de concluídas
            let temConcluidasVisiveis = false;
            linhasConcluidas.forEach(linha => {
                if (linha.style.display !== 'none' && !linha.classList.contains('no-results')) {
                    temConcluidasVisiveis = true;
                }
            });
            
            // Se não houver linhas visíveis, mostrar mensagem
            const tbodyConcluidas = document.getElementById('concluidas-tbody');
            const msgConcluidas = tbodyConcluidas.querySelector('.no-results');
            
            if (!temConcluidasVisiveis) {
                if (!msgConcluidas) {
                    const tr = document.createElement('tr');
                    tr.className = 'no-results';
                    tr.innerHTML = '<td colspan="8" style="text-align: center;">Nenhuma solicitação concluída encontrada para este filtro</td>';
                    tbodyConcluidas.appendChild(tr);
                } else {
                    msgConcluidas.style.display = '';
                }
            } else if (msgConcluidas) {
                msgConcluidas.style.display = 'none';
            }
        }
        
        // Função para aplicar filtros (incluindo data)
        function aplicarFiltros() {
            const dataInicio = document.getElementById('dataInicio').value;
            const dataFim = document.getElementById('dataFim').value;
            
            // Construir URL com parâmetros
            let url = 'requisicoes-feitas.php?';
            const params = [];
            
            if (dataInicio) {
                params.push('data_inicio=' + encodeURIComponent(dataInicio));
            }
            if (dataFim) {
                params.push('data_fim=' + encodeURIComponent(dataFim));
            }
            
            // Adicionar status se existir na URL atual
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('status')) {
                params.push('status=' + urlParams.get('status'));
            }
            if (urlParams.has('message')) {
                params.push('message=' + urlParams.get('message'));
            }
            
            if (params.length > 0) {
                url += params.join('&');
            }
            
            // Redirecionar para a página com os filtros
            window.location.href = url;
        }
        
        // Função para limpar filtros
        function limparFiltros() {
            // Limpar campos
            document.getElementById('filtroTipo').value = 'todos';
            document.getElementById('dataInicio').value = '';
            document.getElementById('dataFim').value = '';
            
            // Redirecionar para a página sem filtros
            const urlParams = new URLSearchParams(window.location.search);
            let url = 'requisicoes-feitas.php?';
            const params = [];
            
            // Manter apenas status e message se existirem
            if (urlParams.has('status')) {
                params.push('status=' + urlParams.get('status'));
            }
            if (urlParams.has('message')) {
                params.push('message=' + urlParams.get('message'));
            }
            
            if (params.length > 0) {
                url += params.join('&');
            }
            
            window.location.href = url;
        }
        
        // Função para mostrar detalhes
        function mostrarDetalhes(tipo, codigo) {
            // Resetar modo de edição
            document.getElementById('modo_edicao').value = '0';
            
            // Esconder botões de edição
            document.getElementById('btn-adicionar-item').style.display = 'none';
            document.getElementById('btn-salvar').style.display = 'none';
            document.getElementById('acoes_header').style.display = 'none';
            
            // Definir campos como somente leitura
            const campos = ['solicitante', 'empresa', 'contrato', 'finalidade', 'funcionario', 'funcao', 'observacao'];
            campos.forEach(campo => {
                const el = document.getElementById(campo);
                if (el) el.readOnly = true;
            });
            
            // Definir o endpoint correto com base no tipo
            let endpoint = '';
            switch(tipo) {
                case 'requisicao':
                    endpoint = 'obter-detalhes-requisicao.php';
                    break;
                case 'pedido_mensal':
                    endpoint = 'obter-detalhes-pedido-mensal.php';
                    break;
                case 'pedido_especial':
                    endpoint = 'obter-detalhes-pedido-especial.php';
                    break;
            }
            
            // Fazer uma requisição AJAX para obter os detalhes da solicitação
            fetch(`${endpoint}?codigo=${codigo}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Armazenar dados da solicitação atual
                        solicitacaoAtual = {
                            tipo: tipo,
                            codigo: codigo,
                            status: data.requisicao?.status || data.pedido?.status || 'pendente'
                        };
                        
                        // Preencher o formulário com os dados
                        document.getElementById('tipo_solicitacao').value = tipo;
                        document.getElementById('codigo_solicitacao').value = tipo === 'requisicao' ? data.requisicao.codigo_solicitacao : data.pedido.codigo_pedido;
                        document.getElementById('codigo').value = tipo === 'requisicao' ? data.requisicao.codigo_solicitacao : data.pedido.codigo_pedido;
                        
                        const detalhes = tipo === 'requisicao' ? data.requisicao : data.pedido;
                        
                        document.getElementById('solicitante').value = detalhes.solicitante;
                        document.getElementById('empresa').value = detalhes.empresa;
                        document.getElementById('contrato').value = detalhes.contrato;
                        document.getElementById('finalidade').value = detalhes.finalidade;
                        
                        // Mostrar status
                        const statusBadge = document.getElementById('status_badge');
                        statusBadge.textContent = solicitacaoAtual.status === 'concluido' ? 'Concluído' : 'Pendente';
                        statusBadge.className = 'status-badge status-' + solicitacaoAtual.status;
                        
                        // Esconder o container de imagem por padrão
                        document.getElementById('imagem_container').style.display = 'none';
                        
                        // Ajustar campos específicos por tipo
                        if (tipo === 'pedido_especial') {
                            document.getElementById('funcionario_group').style.display = 'none';
                            document.getElementById('funcao_group').style.display = 'none';
                            document.getElementById('observacao').value = detalhes.detalhes || '';
                            document.getElementById('itens_container').style.display = 'none';
                            
                            // Verificar se existe uma imagem e exibi-la
                            if (detalhes.imagem_path && detalhes.imagem_path !== 'null' && detalhes.imagem_path !== '') {
                                document.getElementById('imagem_container').style.display = 'flex';
                                document.getElementById('imagem_pedido').src = detalhes.imagem_path;
                            }
                        } else {
                            document.getElementById('funcionario_group').style.display = 'block';
                            document.getElementById('funcao_group').style.display = 'block';
                            document.getElementById('itens_container').style.display = 'block';
                            
                            if (tipo === 'requisicao') {
                                document.getElementById('funcionario').value = detalhes.funcionario;
                                let observacaoTexto = detalhes.observacao || '';

                                // Adicionar informação de urgência se for urgente
                                if (data.requisicao_urgente == 1) {
                                    const motivoUrgente = data.motivo_urgente || 'Limite de gastos excedido';
                                    observacaoTexto = `🚨 PEDIDO URGENTE APÓS VALOR LIMITE EXCEDIDO\nMotivo: ${motivoUrgente}\n\n${observacaoTexto}`;
                                }

                                document.getElementById('observacao').value = observacaoTexto;
                            } else { // pedido_mensal
                                document.getElementById('funcionario').value = detalhes.destinatario;
                                let observacaoTexto = detalhes.observacao || '';

                                // Adicionar informação de urgência se for urgente
                                if (data.requisicao_urgente == 1) {
                                    const motivoUrgente = data.motivo_urgente || 'Limite de gastos excedido';
                                    observacaoTexto = `🚨 PEDIDO URGENTE APÓS VALOR LIMITE EXCEDIDO\nMotivo: ${motivoUrgente}\n\n${observacaoTexto}`;
                                }

                                document.getElementById('observacao').value = observacaoTexto;
                            }

                            // Aplicar estilo especial se for urgente
                            if (data.requisicao_urgente == 1) {
                                document.getElementById('observacao').classList.add('observacao-urgente');
                            } else {
                                document.getElementById('observacao').classList.remove('observacao-urgente');
                            }
                            
                            document.getElementById('funcao').value = detalhes.funcao;
                            
                            // Limpar e preencher a tabela de itens
                            const itensTableBody = document.getElementById('itensTableBody');
                            itensTableBody.innerHTML = '';
                            
                            // Armazenar itens para possível edição posterior
                            itensSolicitacao = [];
                            
                            if (data.itens && data.itens.length > 0) {
                                data.itens.forEach(item => {
                                    // Adicionar item ao array global
                                    itensSolicitacao.push({
                                        produto: item.produto,
                                        nome_produto: item.nome_produto || 'Nome não disponível',
                                        quantidade: parseInt(item.quantidade)
                                    });
                                    
                                    // Adicionar linha à tabela
                                    const row = document.createElement('tr');
                                    row.innerHTML = `
                                        <td>${item.produto}</td>
                                        <td>${item.nome_produto || 'Nome não disponível'}</td>
                                        <td>${item.quantidade}</td>
                                    `;
                                    itensTableBody.appendChild(row);
                                });
                            } else {
                                const row = document.createElement('tr');
                                row.innerHTML = '<td colspan="3">Nenhum item encontrado</td>';
                                itensTableBody.appendChild(row);
                            }
                        }
                        
                        // Verificar se é pedido urgente e mostrar aviso
                        const avisoUrgente = document.getElementById('aviso-pedido-urgente');

                        // Verificar se é urgente baseado no tipo de solicitação
                        let isUrgente = false;

                        if (tipo === 'requisicao') {
                            // Para requisições, verificar requisicao_urgente
                            isUrgente = (data.requisicao && data.requisicao.requisicao_urgente == 1);
                        } else if (tipo === 'pedido_mensal') {
                            // Para pedidos mensais, verificar pedido_urgente
                            isUrgente = (data.pedido && data.pedido.pedido_urgente == 1);
                        }

                        if (isUrgente) {
                            avisoUrgente.style.display = 'block';
                        } else {
                            avisoUrgente.style.display = 'none';
                        }

                        document.getElementById('popup-title').textContent = 'Detalhes da Solicitação';
                        
                        // Se for concluído, esconder botão de edição e mostrar botão fechar
                        if (solicitacaoAtual.status === 'concluido') {
                            document.getElementById('btn-editar-popup').style.display = 'none';
                            document.getElementById('btn-cancelar-edicao').textContent = 'Fechar';
                            document.getElementById('btn-cancelar-edicao').onclick = fecharPopup;
                            document.getElementById('btn-cancelar-edicao').style.display = 'inline-block';
                        } else {
                            document.getElementById('btn-editar-popup').style.display = 'inline-block';
                            document.getElementById('btn-cancelar-edicao').textContent = 'Cancelar';
                            document.getElementById('btn-cancelar-edicao').onclick = cancelarEdicao;
                            document.getElementById('btn-cancelar-edicao').style.display = 'none';
                        }
                        
                        // Exibir o popup
                        document.getElementById('popup').style.display = 'flex';
                        document.getElementById('popupOverlay').style.display = 'flex';
                        document.getElementById('popup').classList.add('active');
                        document.getElementById('popupOverlay').classList.add('active');
                        atualizarBotaoCancelarSolicitacao();
                    } else {
                        alert('Erro ao carregar os detalhes da solicitação');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao carregar os detalhes da solicitação');
                });
        }
        
        // Função para adicionar novo item
        function adicionarNovoItem() {
            // Garante que está no modo de edição
            document.getElementById('modo_edicao').value = '1';
            abrirPopupProdutos();
        }
        
        // Função para abrir popup de produtos
        function abrirPopupProdutos() {
            // Carregar lista de produtos via AJAX
            fetch('obter-produtos.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const listaProdutos = document.getElementById('listaProdutosPopup');
                        listaProdutos.innerHTML = '';
                        data.produtos.forEach(produto => {
                            const tr = document.createElement('tr');
                            tr.innerHTML = `
                                <td>${produto.codigo}</td>
                                <td>${produto.nome}</td>
                                <td><button type="button" onclick="selecionarProdutoPopup('${produto.codigo}', '${produto.nome}')">Selecionar</button></td>
                            `;
                            listaProdutos.appendChild(tr);
                        });
                        // Ajustar o thead para remover a coluna de estoque
                        const thead = listaProdutos.parentElement.querySelector('thead tr');
                        if (thead && thead.children.length === 4) {
                            thead.removeChild(thead.children[2]);
                        }
                        document.getElementById('popupProdutosOverlay').classList.add('active');
                        document.getElementById('popupProdutos').style.display = 'flex';
                        document.getElementById('pesquisaProduto').value = '';
                        document.getElementById('pesquisaProduto').focus();
                    } else {
                        alert('Erro ao carregar produtos');
                    }
                })
                .catch(error => {
                    alert('Erro ao carregar produtos');
                });
        }
        
        // Função para filtrar produtos
        function filtrarProdutosPopup() {
            const termo = document.getElementById('pesquisaProduto').value.toLowerCase();
            const linhas = document.querySelectorAll('#listaProdutosPopup tr');
            linhas.forEach(tr => {
                const nome = tr.children[1].textContent.toLowerCase();
                const codigo = tr.children[0].textContent.toLowerCase();
                if (nome.includes(termo) || codigo.includes(termo)) {
                    tr.style.display = '';
                } else {
                    tr.style.display = 'none';
                }
            });
        }
        
        // Função para selecionar produto do popup (adiciona ao array de itens)
        function selecionarProdutoPopup(codigo, nome) {
            // Adiciona imediatamente à tabela com quantidade 1
            if (document.getElementById('modo_edicao').value === '1') {
                // Verifica se já existe
                const idx = itensSolicitacao.findIndex(item => item.produto === codigo);
                if (idx !== -1) {
                    itensSolicitacao[idx].quantidade += 1;
                } else {
                    itensSolicitacao.push({
                        produto: codigo,
                        nome_produto: nome,
                        quantidade: 1
                    });
                }
                atualizarTabelaItensEdicao();
            }
            fecharPopupProdutos();
        }
        
        // Função para fechar popup de produtos
        function fecharPopupProdutos() {
            document.getElementById('popupProdutosOverlay').classList.remove('active');
            document.getElementById('popupProdutos').style.display = 'none';
        }
        
        // Função para atualizar quantidade
        function atualizarQuantidade(index, novaQuantidade) {
            novaQuantidade = parseInt(novaQuantidade);
            if (novaQuantidade > 0) {
                itensSolicitacao[index].quantidade = novaQuantidade;
            } else {
                alert('A quantidade deve ser maior que zero!');
                atualizarTabelaItensEdicao(); // Recarregar a tabela para restaurar o valor anterior
            }
        }
        
        // Função para remover item
        function removerItem(index) {
            if (confirm('Tem certeza que deseja remover este item?')) {
                itensSolicitacao.splice(index, 1);
                atualizarTabelaItensEdicao();
            }
        }
        
        // Função para salvar alterações
        function salvarAlteracoes() {
            if (itensSolicitacao.length === 0) {
                alert('Adicione pelo menos um item à solicitação!');
                return;
            }
            // Preparar dados para envio
            const formData = new FormData();
            formData.append('tipo', solicitacaoAtual.tipo);
            formData.append('codigo', solicitacaoAtual.codigo);
            formData.append('itens', JSON.stringify(itensSolicitacao));
            // Enviar requisição
            fetch('atualizar-solicitacao.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Exibir popup modal de feedback
                    document.getElementById('feedback-popup-title').textContent = 'Sucesso';
                    document.getElementById('feedback-popup-message').textContent = 'Solicitação atualizada com sucesso!';
                    document.getElementById('feedback-popup-modal').style.display = 'flex';
                    setTimeout(() => {
                        document.getElementById('feedback-popup-modal').style.display = 'none';
                        window.location.href = 'requisicoes-feitas.php';
                    }, 2000);
                } else {
                    document.getElementById('feedback-popup-title').textContent = 'Erro';
                    document.getElementById('feedback-popup-message').textContent = 'Erro ao atualizar solicitação: ' + data.message;
                    document.getElementById('feedback-popup-modal').style.display = 'flex';
                    setTimeout(() => {
                        document.getElementById('feedback-popup-modal').style.display = 'none';
                    }, 3500);
                }
            })
            .catch(error => {
                document.getElementById('feedback-popup-title').textContent = 'Erro';
                document.getElementById('feedback-popup-message').textContent = 'Erro ao processar a solicitação.';
                document.getElementById('feedback-popup-modal').style.display = 'flex';
                setTimeout(() => {
                    document.getElementById('feedback-popup-modal').style.display = 'none';
                }, 3500);
            });
        }

        function fecharPopup() {
            document.getElementById('popup').style.display = 'none';
            document.getElementById('popupOverlay').style.display = 'none';
            document.getElementById('popup').classList.remove('active');
            document.getElementById('popupOverlay').classList.remove('active');
        }
        
        // Função para confirmar cancelamento
        function confirmarCancelamento(tipo, id) {
            document.getElementById('delete_tipo').value = tipo;
            document.getElementById('delete_id').value = id;
            document.getElementById('confirmOverlay').style.display = 'flex';
            document.getElementById('confirmOverlay').classList.add('active');
        }
        
        // Configurar os botões de confirmação
        document.getElementById('confirmDelete').addEventListener('click', function() {
            // Notificar outras abas/páginas
            try { localStorage.setItem('sol-cancelada', Date.now()); } catch(e){}
            document.getElementById('deleteForm').submit();
        });
        
        document.getElementById('cancelDelete').addEventListener('click', function() {
            var overlay = document.getElementById('confirmOverlay');
            var popup = overlay.querySelector('.confirm-popup');
            overlay.style.display = 'none';
            overlay.classList.remove('active');
            if (popup) popup.style.display = 'none';
        });

        // Função para atualizar tabela de itens no modo de edição
        function atualizarTabelaItensEdicao() {
            const itensTableBody = document.getElementById('itensTableBody');
            itensTableBody.innerHTML = '';
            const modoEdicao = document.getElementById('modo_edicao').value === '1';
            // Mostrar/ocultar coluna de ações
            document.getElementById('acoes_header').style.display = modoEdicao ? 'table-cell' : 'none';
            if (itensSolicitacao.length > 0) {
                itensSolicitacao.forEach((item, index) => {
                    const row = document.createElement('tr');
                    if (modoEdicao) {
                        row.innerHTML = `
                            <td>${item.produto}</td>
                            <td>${item.nome_produto || 'Nome não disponível'}</td>
                            <td><input type="number" min="1" value="${item.quantidade}" style="width:70px;" onchange="atualizarQuantidade(${index}, this.value)"></td>
                            <td style="text-align:center;">
                                <button type="button" class="btn btn-delete" title="Remover" onclick="removerItem(${index})" style="padding:6px 10px;">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#b91c1c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"/><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2"/><line x1="10" y1="11" x2="10" y2="17"/><line x1="14" y1="11" x2="14" y2="17"/></svg>
                                </button>
                            </td>
                        `;
                    } else {
                        row.innerHTML = `
                            <td>${item.produto}</td>
                            <td>${item.nome_produto || 'Nome não disponível'}</td>
                            <td>${item.quantidade}</td>
                        `;
                    }
                    itensTableBody.appendChild(row);
                });
            } else {
                const row = document.createElement('tr');
                row.innerHTML = modoEdicao ? '<td colspan="4">Nenhum item encontrado</td>' : '<td colspan="3">Nenhum item encontrado</td>';
                itensTableBody.appendChild(row);
            }
        }

        // Função para exibir o popup de confirmação de cancelamento
        function abrirConfirmarCancelamento() {
            if (!solicitacaoAtual) return;
            document.getElementById('delete_tipo').value = solicitacaoAtual.tipo;
            document.getElementById('delete_id').value = solicitacaoAtual.codigo;
            var overlay = document.getElementById('confirmOverlay');
            var popup = overlay.querySelector('.confirm-popup');
            overlay.style.display = 'flex';
            overlay.classList.add('active');
            if (popup) popup.style.display = 'flex';
        }

        // Exibir botão de cancelar solicitação se não estiver concluída
        function atualizarBotaoCancelarSolicitacao() {
            const btn = document.getElementById('btn-cancelar-solicitacao');
            if (!solicitacaoAtual || solicitacaoAtual.status === 'concluido') {
                btn.style.display = 'none';
            } else {
                btn.style.display = 'inline-block';
            }
        }

        // Garante que o popup de detalhes sempre inicie oculto
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('popup').style.display = 'none';
            document.getElementById('popupOverlay').style.display = 'none';
        });

        // --- Adição: ao confirmar quantidade, adicionar item ---
        document.getElementById('btnConfirmarQuantidade').onclick = function() {
            const qtd = parseInt(document.getElementById('inputQuantidade').value);
            if (!produtoSelecionado || !qtd || qtd < 1) return;
            if (document.getElementById('modo_edicao').value === '1') {
                itensSolicitacao.push({
                    produto: produtoSelecionado.codigo,
                    nome_produto: produtoSelecionado.nome,
                    quantidade: qtd
                });
                console.log('Item adicionado:', itensSolicitacao);
                atualizarTabelaItensEdicao();
            }
            document.getElementById('quantidadeOverlay').style.display = 'none';
            document.getElementById('quantidadeOverlay').classList.remove('active');
            produtoSelecionado = null;
        };

        // Abrir popup de filtro
        const filterBtn = document.getElementById('filterBtn');
        const filterPopup = document.getElementById('filterPopup');
        const filterPopupOverlay = document.getElementById('filterPopupOverlay');
        const closeFilterPopupBtn = document.getElementById('closeFilterPopup');

        filterBtn.addEventListener('click', function() {
            filterPopup.style.display = 'block';
            filterPopupOverlay.style.display = 'block';
        });
        filterPopupOverlay.addEventListener('click', function() {
            filterPopup.style.display = 'none';
            filterPopupOverlay.style.display = 'none';
        });
        closeFilterPopupBtn.addEventListener('click', function() {
            filterPopup.style.display = 'none';
            filterPopupOverlay.style.display = 'none';
        });
    </script>
</body>
</html>

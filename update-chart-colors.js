// Paleta de cores moderna e suave para gráficos
const modernColors = {
  primary: '#6366f1',
  primaryLight: '#a5b4fc',
  secondary: '#64748b',
  success: '#10b981',
  successLight: '#6ee7b7',
  warning: '#f59e0b',
  warningLight: '#fbbf24',
  danger: '#ef4444',
  dangerLight: '#f87171',
  info: '#06b6d4',
  infoLight: '#67e8f9',
  purple: '#8b5cf6',
  purpleLight: '#c4b5fd',
  pink: '#ec4899',
  pinkLight: '#f9a8d4',
  indigo: '#6366f1',
  indigoLight: '#a5b4fc'
};

// Paleta de cores suaves para gráficos de pizza/donut
const softPalette = [
  '#6366f1', // Primary
  '#10b981', // Success
  '#f59e0b', // Warning
  '#ef4444', // Danger
  '#06b6d4', // Info
  '#8b5cf6', // Purple
  '#ec4899', // Pink
  '#64748b', // Secondary
  '#a5b4fc', // Primary Light
  '#6ee7b7', // Success Light
  '#fbbf24', // Warning Light
  '#f87171'  // Danger Light
];

// Configurações padrão para todos os gráficos
const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom',
      labels: {
        padding: 20,
        usePointStyle: true,
        font: {
          family: 'Inter, sans-serif',
          size: 12,
          weight: '500'
        },
        color: '#1e293b'
      }
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      titleColor: '#1e293b',
      bodyColor: '#64748b',
      borderColor: '#e2e8f0',
      borderWidth: 1,
      cornerRadius: 8,
      padding: 12,
      titleFont: {
        family: 'Inter, sans-serif',
        size: 14,
        weight: '600'
      },
      bodyFont: {
        family: 'Inter, sans-serif',
        size: 13,
        weight: '400'
      }
    }
  }
};

// Configurações específicas para gráficos de linha
const lineChartOptions = {
  ...defaultChartOptions,
  scales: {
    x: {
      grid: {
        color: '#f1f5f9',
        borderColor: '#e2e8f0'
      },
      ticks: {
        color: '#64748b',
        font: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '500'
        }
      }
    },
    y: {
      grid: {
        color: '#f1f5f9',
        borderColor: '#e2e8f0'
      },
      ticks: {
        color: '#64748b',
        font: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '500'
        }
      }
    }
  },
  elements: {
    line: {
      tension: 0.4,
      borderWidth: 3
    },
    point: {
      radius: 4,
      hoverRadius: 6,
      borderWidth: 2,
      backgroundColor: '#ffffff'
    }
  }
};

// Configurações específicas para gráficos de barra
const barChartOptions = {
  ...defaultChartOptions,
  scales: {
    x: {
      grid: {
        display: false
      },
      ticks: {
        color: '#64748b',
        font: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '500'
        }
      }
    },
    y: {
      grid: {
        color: '#f1f5f9',
        borderColor: '#e2e8f0'
      },
      ticks: {
        color: '#64748b',
        font: {
          family: 'Inter, sans-serif',
          size: 11,
          weight: '500'
        }
      }
    }
  },
  elements: {
    bar: {
      borderRadius: 6,
      borderSkipped: false
    }
  }
};

// Configurações específicas para gráficos de pizza/donut
const pieChartOptions = {
  ...defaultChartOptions,
  cutout: '60%', // Para donut charts
  elements: {
    arc: {
      borderWidth: 2,
      borderColor: '#ffffff'
    }
  }
};

// Função para aplicar cores modernas aos datasets
function applyModernColors(datasets, type = 'mixed') {
  datasets.forEach((dataset, index) => {
    if (type === 'pie' || type === 'doughnut') {
      dataset.backgroundColor = softPalette.slice(0, dataset.data.length);
      dataset.borderColor = '#ffffff';
      dataset.borderWidth = 2;
    } else if (type === 'line') {
      const color = softPalette[index % softPalette.length];
      dataset.borderColor = color;
      dataset.backgroundColor = color + '20'; // 20% opacity
      dataset.pointBackgroundColor = '#ffffff';
      dataset.pointBorderColor = color;
    } else if (type === 'bar') {
      const color = softPalette[index % softPalette.length];
      dataset.backgroundColor = color;
      dataset.borderColor = color;
      dataset.hoverBackgroundColor = color + 'dd'; // Slightly darker on hover
    }
  });
}

// Função para criar gradientes suaves
function createGradient(ctx, color1, color2) {
  const gradient = ctx.createLinearGradient(0, 0, 0, 400);
  gradient.addColorStop(0, color1);
  gradient.addColorStop(1, color2);
  return gradient;
}

// Exportar para uso global
window.modernChartConfig = {
  colors: modernColors,
  palette: softPalette,
  options: {
    default: defaultChartOptions,
    line: lineChartOptions,
    bar: barChartOptions,
    pie: pieChartOptions
  },
  applyColors: applyModernColors,
  createGradient: createGradient
};

console.log('🎨 Modern chart colors loaded successfully!');

// Sistema de Dropdown para Exportação
function toggleExportDropdown(event, dropdownId) {
  event.stopPropagation();

  // Fechar todos os outros dropdowns
  document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
    if (dropdown.id !== dropdownId) {
      dropdown.classList.remove('show');
    }
  });

  // Toggle do dropdown atual
  const dropdown = document.getElementById(dropdownId);
  dropdown.classList.toggle('show');
}

// Fechar dropdown ao clicar fora
document.addEventListener('click', function(event) {
  if (!event.target.closest('.export-dropdown')) {
    document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  }
});

// Fechar dropdown ao pressionar ESC
document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
      dropdown.classList.remove('show');
    });
  }
});

// Disponibilizar globalmente
window.toggleExportDropdown = toggleExportDropdown;

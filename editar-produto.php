<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
include 'conexao.php';

// Verificar se o código do produto foi fornecido
if (isset($_GET['codigo'])) {
    $codigo = $_GET['codigo'];
    
    // Buscar os dados do produto
    $sql = "SELECT * FROM produtos WHERE codigo = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $produto = $result->fetch_assoc();
        
        // Formatar a data para o formato HTML5 date input (YYYY-MM-DD)
        if (!empty($produto['validade']) && $produto['validade'] != '0000-00-00') {
            // Converter a data para o formato correto
            $date = date_create_from_format('Y-m-d', $produto['validade']);
            if ($date) {
                $produto['validade'] = date_format($date, 'Y-m-d');
            } else {
                // Tentar outro formato comum
                $date = date_create_from_format('d/m/Y', $produto['validade']);
                if ($date) {
                    $produto['validade'] = date_format($date, 'Y-m-d');
                }
            }
        } else {
            $produto['validade'] = '';
        }
    } else {
        // Produto não encontrado, redirecionar para a tabela
        header('Location: tabela-produtos.php?status=not-found');
        exit;
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $codigo = $_POST['codigo'];
    $nome = $_POST['nome'];
    $categoria = $_POST['categoria'];
    $ca = $_POST['ca'];
    $valor = $_POST['valor'];
    $valor_bruto = $_POST['valor_bruto'];
    $quantidade = $_POST['quantidade'];
    $unidade_medida = $_POST['unidade_medida'];
    $estoque_minimo = isset($_POST['estoque_minimo']) ? $_POST['estoque_minimo'] : 0;
    
    // Tratar a data de validade
    $validade = !empty($_POST['validade']) ? $_POST['validade'] : null;
    
    // Tratar o campo de validade de uso
    $validade_uso = isset($_POST['validade_uso']) ? $_POST['validade_uso'] : null;
    
    // Se a data estiver vazia, definir como NULL para o banco de dados
    if (empty($validade)) {
        $sql = "UPDATE produtos SET nome=?, unidade_medida=?, categoria=?, ca=?, valor=?, valor_bruto=?, quantidade=?, estoque_minimo=?, validade=NULL, validade_uso=? WHERE codigo=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssddiiss", $nome, $unidade_medida, $categoria, $ca, $valor, $valor_bruto, $quantidade, $estoque_minimo, $validade_uso, $codigo);
    } else {
        // Se a data não estiver vazia, usar o formato correto
        $sql = "UPDATE produtos SET nome=?, unidade_medida=?, categoria=?, ca=?, valor=?, valor_bruto=?, quantidade=?, estoque_minimo=?, validade=?, validade_uso=? WHERE codigo=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssssddiisss", $nome, $unidade_medida, $categoria, $ca, $valor, $valor_bruto, $quantidade, $estoque_minimo, $validade, $validade_uso, $codigo);
    }
    
    if ($stmt->execute()) {
        // Redireciona com sucesso
        header('Location: tabela-produtos.php?status=edit-success');
    } else {
        // Redireciona com erro
        header('Location: tabela-produtos.php?status=edit-error&error=' . urlencode($stmt->error));
    }
    exit;
} else {
    // Nenhum código fornecido, redirecionar para a tabela
    header('Location: tabela-produtos.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <title>Editar Produto</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #4a89dc;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3a70b9;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e5e5e5;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c0392b;
        }
        
        .btn-warning {
            background-color: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #e67e22;
        }
        
        .btn-success {
            background-color: #2ecc71;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #27ae60;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
            background-color: #f8f9fa;
        }
        
        .tab.active {
            background-color: #fff;
            border-color: #ddd;
            border-bottom-color: #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .action-buttons .btn-group {
            display: flex;
            gap: 10px;
        }
        
        /* Estilos para o popup de confirmação */
        .confirm-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .confirm-popup {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            width: 90%;
            max-width: 400px;
            text-align: center;
        }
        
        .confirm-popup h3 {
            margin-top: 0;
            color: #333;
        }
        
        .confirm-popup p {
            margin-bottom: 25px;
            color: #666;
        }
        
        .confirm-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        /* Estilos para botões */
        .btn-container {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: #4a89dc;
            color: white;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        
        .btn-warning {
            background-color: #f39c12;
            color: white;
        }
        
        .btn-success {
            background-color: #2ecc71;
            color: white;
        }
        
        /* Estilos para ações */
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    
    <div class="container">
        <h1>Editar Produto</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('info-basicas')">Informações Básicas</div>
            <div class="tab" onclick="switchTab('estoque')">Estoque</div>
            <div class="tab" onclick="switchTab('acoes')">Ações</div>
        </div>
        
        <div id="info-basicas" class="tab-content active">
            <form id="editForm" action="editar-produto.php" method="POST">
                <input type="hidden" name="codigo" value="<?= $produto['codigo'] ?>">
                
                <div class="form-group">
                    <label for="nome">Nome:</label>
                    <input type="text" id="nome" name="nome" value="<?php echo htmlspecialchars($produto['nome']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="categoria">Categoria:</label>
                    <select id="categoria" name="categoria" required>
                        <option value="">Selecione uma categoria</option>
                        <option value="Equipamentos de Proteção Individual (EPIs)" <?php echo ($produto['categoria'] == 'Equipamentos de Proteção Individual (EPIs)') ? 'selected' : ''; ?>>Equipamentos de Proteção Individual (EPIs)</option>
                        <option value="Materiais de Escritório" <?php echo ($produto['categoria'] == 'Materiais de Escritório') ? 'selected' : ''; ?>>Materiais de Escritório</option>
                        <option value="Materiais de Limpeza" <?php echo ($produto['categoria'] == 'Materiais de Limpeza') ? 'selected' : ''; ?>>Materiais de Limpeza</option>
                        <option value="Ferramentas e Equipamentos Manuais" <?php echo ($produto['categoria'] == 'Ferramentas e Equipamentos Manuais') ? 'selected' : ''; ?>>Ferramentas e Equipamentos Manuais</option>
                        <option value="Uniformes e Roupas de Trabalho" <?php echo ($produto['categoria'] == 'Uniformes e Roupas de Trabalho') ? 'selected' : ''; ?>>Uniformes e Roupas de Trabalho</option>
                        <option value="Materiais Elétricos" <?php echo ($produto['categoria'] == 'Materiais Elétricos') ? 'selected' : ''; ?>>Materiais Elétricos</option>
                        <option value="Peças de Reposição e Componentes Mecânicos" <?php echo ($produto['categoria'] == 'Peças de Reposição e Componentes Mecânicos') ? 'selected' : ''; ?>>Peças de Reposição e Componentes Mecânicos</option>
                        <option value="Produtos de Informática" <?php echo ($produto['categoria'] == 'Produtos de Informática') ? 'selected' : ''; ?>>Produtos de Informática</option>
                        <option value="Materiais de Construção" <?php echo ($produto['categoria'] == 'Materiais de Construção') ? 'selected' : ''; ?>>Materiais de Construção</option>
                        <option value="Itens de Consumo Rápido (Descartáveis)" <?php echo ($produto['categoria'] == 'Itens de Consumo Rápido (Descartáveis)') ? 'selected' : ''; ?>>Itens de Consumo Rápido (Descartáveis)</option>
                        <option value="Materiais de Higiene Pessoal" <?php echo ($produto['categoria'] == 'Materiais de Higiene Pessoal') ? 'selected' : ''; ?>>Materiais de Higiene Pessoal</option>
                        <option value="Produtos Químicos Industriais" <?php echo ($produto['categoria'] == 'Produtos Químicos Industriais') ? 'selected' : ''; ?>>Produtos Químicos Industriais</option>
                        <option value="Equipamentos Eletrônicos" <?php echo ($produto['categoria'] == 'Equipamentos Eletrônicos') ? 'selected' : ''; ?>>Equipamentos Eletrônicos</option>
                        <option value="Materiais de Embalagem" <?php echo ($produto['categoria'] == 'Materiais de Embalagem') ? 'selected' : ''; ?>>Materiais de Embalagem</option>
                        <option value="Suprimentos Médicos" <?php echo ($produto['categoria'] == 'Suprimentos Médicos') ? 'selected' : ''; ?>>Suprimentos Médicos</option>
                        <option value="Alimentos e Bebidas para Consumo Interno" <?php echo ($produto['categoria'] == 'Alimentos e Bebidas para Consumo Interno') ? 'selected' : ''; ?>>Alimentos e Bebidas para Consumo Interno</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="ca">CA:</label>
                    <input type="text" id="ca" name="ca" value="<?= htmlspecialchars($produto['ca']) ?>">
                </div>
                
                <div class="form-group">
                    <label for="valor">Valor:</label>
                    <input type="number" id="valor" name="valor" step="0.01" value="<?= $produto['valor'] ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="valor_bruto">Valor Bruto:</label>
                    <input type="number" id="valor_bruto" name="valor_bruto" step="0.01" value="<?= $produto['valor_bruto'] ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="quantidade">Quantidade:</label>
                    <input type="number" id="quantidade" name="quantidade" value="<?= $produto['quantidade'] ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="validade">Data de Validade:</label>
                    <input type="date" id="validade" name="validade" value="<?= htmlspecialchars($produto['validade']) ?>">
                </div>
                <div class="form-group" id="validade_uso_group" style="display:none;">
                    <label for="validade_uso">Validade de Uso (em meses):</label>
                    <input type="number" id="validade_uso" name="validade_uso" min="1" step="1" value="<?= isset($produto['validade_uso']) ? htmlspecialchars($produto['validade_uso']) : '' ?>">
                </div>
                
                <div class="form-group">
                    <label for="unidade_medida">Unidade</label>
                    <input type="text" id="unidade_medida" name="unidade_medida" value="<?= htmlspecialchars($produto['unidade_medida'] ?? '') ?>">
                </div>
                
                <div class="btn-container">
                    <a href="tabela-produtos.php" class="btn btn-secondary">Cancelar</a>
                    <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                </div>
            </form>
        </div>
        
        <div id="estoque" class="tab-content">
            <form id="estoqueMinForm" action="tabela-produtos.php" method="POST">
                <input type="hidden" name="produto_id" value="<?= $produto['codigo'] ?>">
                
                <div class="form-group">
                    <label for="estoque_minimo">Estoque Mínimo:</label>
                    <input type="number" id="estoque_minimo" name="estoque_minimo" min="0" value="<?= $produto['estoque_minimo'] ?>" required>
                </div>
                
                <p>Defina o valor mínimo de estoque para o produto. Quando o estoque ficar abaixo deste valor, você receberá alertas.</p>
                
                <div class="btn-container">
                    <a href="tabela-produtos.php" class="btn btn-secondary">Cancelar</a>
                    <button type="submit" class="btn btn-primary">Salvar Estoque Mínimo</button>
                </div>
            </form>
        </div>
        
        <div id="acoes" class="tab-content">
            <h4>Ações do Produto</h4>
            <p>Aqui você pode realizar ações como inativar ou excluir o produto.</p>
            
            <div class="action-buttons">
                <div class="btn-group">
                    <?php if (isset($produto['status']) && $produto['status'] == 'inativo'): ?>
                        <button id="btnAtivar" class="btn btn-success" onclick="confirmarAtivacao()">
                            <i class="fas fa-power-off"></i> Ativar Produto
                        </button>
                    <?php else: ?>
                        <button id="btnInativar" class="btn btn-warning" onclick="confirmarInativacao()">
                            <i class="fas fa-power-off"></i> Inativar Produto
                        </button>
                    <?php endif; ?>
                </div>
                
                <button class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> Excluir Produto
                </button>
            </div>
            
            <div class="btn-container" style="margin-top: 30px;">
                <a href="tabela-produtos.php" class="btn btn-secondary">Voltar para Tabela</a>
            </div>
        </div>

        <!-- Pop-up de confirmação de exclusão -->
        <div id="confirmOverlay" class="confirm-overlay">
            <div class="confirm-popup">
                <h3>Confirmar Exclusão</h3>
                <p>Tem certeza que deseja excluir este produto? Esta ação não pode ser desfeita.</p>
                <div class="confirm-buttons">
                    <form action="tabela-produtos.php" method="POST">
                        <input type="hidden" name="delete_id" value="<?= $produto['codigo'] ?>">
                        <button type="button" class="btn btn-secondary" onclick="cancelDelete()">Cancelar</button>
                        <button type="submit" class="btn btn-danger">Sim, Excluir</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Pop-up de confirmação de inativação/ativação -->
        <div id="inativarOverlay" class="confirm-overlay">
            <div class="confirm-popup">
                <h3 id="inativarTitle">Confirmar Inativação</h3>
                <p id="inativarMessage">Tem certeza que deseja inativar este produto?</p>
                <div class="confirm-buttons">
                    <form action="tabela-produtos.php" method="POST">
                        <input type="hidden" name="inativar_id" value="<?= $produto['codigo'] ?>">
                        <input type="hidden" id="status" name="status" value="inativo">
                        <button type="button" class="btn btn-secondary" onclick="fecharInativarPopup()">Cancelar</button>
                        <button type="submit" id="confirmInativar" class="btn btn-warning">Sim, Inativar</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<script>
    // Função para trocar entre abas
    function switchTab(tabId) {
        // Desativar todas as abas
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        
        // Desativar todos os conteúdos
        const contents = document.querySelectorAll('.tab-content');
        contents.forEach(content => content.classList.remove('active'));
        
        // Ativar a aba selecionada
        const selectedTab = document.querySelector(`.tab[onclick="switchTab('${tabId}')"]`);
        if (selectedTab) selectedTab.classList.add('active');
        
        // Ativar o conteúdo selecionado
        const selectedContent = document.getElementById(tabId);
        if (selectedContent) selectedContent.classList.add('active');
    }

    // Função para confirmar exclusão
    function confirmDelete() {
        document.getElementById('confirmOverlay').style.display = 'flex';
    }

    // Função para cancelar exclusão
    function cancelDelete() {
        document.getElementById('confirmOverlay').style.display = 'none';
    }

    // Função para confirmar inativação
    function confirmarInativacao() {
        document.getElementById('inativarTitle').textContent = 'Confirmar Inativação';
        document.getElementById('inativarMessage').textContent = 'Tem certeza que deseja inativar este produto?';
        document.getElementById('confirmInativar').textContent = 'Sim, Inativar';
        document.getElementById('status').value = 'inativo';
        document.getElementById('inativarOverlay').style.display = 'flex';
    }

    // Função para confirmar ativação
    function confirmarAtivacao() {
        document.getElementById('inativarTitle').textContent = 'Confirmar Ativação';
        document.getElementById('inativarMessage').textContent = 'Tem certeza que deseja ativar este produto?';
        document.getElementById('confirmInativar').textContent = 'Sim, Ativar';
        document.getElementById('status').value = 'ativo';
        document.getElementById('inativarOverlay').style.display = 'flex';
    }

    // Função para fechar popup de inativação
    function fecharInativarPopup() {
        document.getElementById('inativarOverlay').style.display = 'none';
    }

    // Verificar se a data está no formato correto
    document.addEventListener('DOMContentLoaded', function() {
        const validadeInput = document.getElementById('validade');
        
        // Verificar se o campo existe e tem um valor
        if (validadeInput) {
            console.log("Valor inicial da data:", validadeInput.value);
            
            // Adicionar um evento para verificar o valor antes do envio
            document.getElementById('editForm').addEventListener('submit', function(e) {
                console.log("Valor da data no envio:", validadeInput.value);
            });
        }
    });

    // Exibir campo validade_uso para categorias específicas
    const categoriaSelect = document.getElementById('categoria');
    const validadeUsoGroup = document.getElementById('validade_uso_group');
    function toggleValidadeUso() {
        if (categoriaSelect.value === 'Equipamentos de Proteção Individual (EPIs)' || categoriaSelect.value === 'Uniformes e Roupas de Trabalho') {
            validadeUsoGroup.style.display = 'block';
            document.getElementById('validade_uso').required = true;
        } else {
            validadeUsoGroup.style.display = 'none';
            document.getElementById('validade_uso').required = false;
            document.getElementById('validade_uso').value = '';
        }
    }
    categoriaSelect.addEventListener('change', toggleValidadeUso);
    // Inicializar ao carregar
    toggleValidadeUso();
</script>

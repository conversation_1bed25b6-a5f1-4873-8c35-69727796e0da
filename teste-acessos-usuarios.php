<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
}

require_once 'conexao.php';

echo "<h2>Teste - Acessos de Usuários Atualizados</h2>";

// Verificar se existem usuários
$sql_usuarios = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'";
$result = $conn->query($sql_usuarios);

if ($result && $result->fetch_assoc()['count'] > 0) {
    $count_usuarios = $conn->query("SELECT COUNT(*) as total FROM usuarios")->fetch_assoc()['total'];
    echo "<p>✅ Tabela 'usuarios' existe com $count_usuarios usuários</p>";
    
    if ($count_usuarios == 0) {
        echo "<p>⚠️ Criando usuário de teste...</p>";
        $sql_insert = "INSERT INTO usuarios (nome, email, senha, nivel) VALUES ('Administrador', '<EMAIL>', MD5('123456'), 'admin')";
        if ($conn->query($sql_insert)) {
            echo "<p>✅ Usuário administrador criado</p>";
        }
    }
} else {
    echo "<p>❌ Tabela 'usuarios' não existe</p>";
    echo "<p>Criando tabela...</p>";
    
    $sql_create = "
        CREATE TABLE usuarios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            senha VARCHAR(255) NOT NULL,
            nivel VARCHAR(50) DEFAULT 'user',
            status VARCHAR(20) DEFAULT 'ativo',
            data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ";
    
    if ($conn->query($sql_create)) {
        echo "<p>✅ Tabela 'usuarios' criada</p>";
        
        $sql_insert = "INSERT INTO usuarios (nome, email, senha, nivel) VALUES ('Administrador', '<EMAIL>', MD5('123456'), 'admin')";
        if ($conn->query($sql_insert)) {
            echo "<p>✅ Usuário administrador criado</p>";
        }
    }
}

echo "<h3>📋 Novas Abas Adicionadas ao Sistema de Acessos:</h3>";

echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px;'>";
echo "<h4>💰 Limites de Gastos</h4>";
echo "<ul>";
echo "<li><strong>Configurar Limites</strong> - Definir limites de gastos por usuário/setor</li>";
echo "<li><strong>Relatório de Gastos</strong> - Visualizar gastos atuais vs limites</li>";
echo "<li><strong>Alertas de Limites</strong> - Notificações quando próximo do limite</li>";
echo "</ul>";
echo "<p><em>Controle financeiro e orçamentário do sistema</em></p>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px;'>";
echo "<h4>🔄 Devolução</h4>";
echo "<ul>";
echo "<li><strong>Processar Devoluções</strong> - Interface principal de devolução</li>";
echo "<li><strong>Histórico de Devoluções</strong> - Consultar devoluções anteriores</li>";
echo "<li><strong>Relatório de Devoluções</strong> - Análises e estatísticas</li>";
echo "</ul>";
echo "<p><em>Gestão completa do processo de devolução de EPIs</em></p>";
echo "</div>";

echo "</div>";

echo "<h3>🎯 Estrutura Completa de Menus Atualizada:</h3>";

$menus = [
    'Cadastros' => ['Cadastro de Produto', 'Cadastro de Pessoas', 'Cadastro de Empresas'],
    'Tabelas' => ['Tabela de Produtos', 'Tabela de Pessoas', 'Tabela de Empresas'],
    'Entradas' => ['Entrada de Estoque', 'Registros de Entrada'],
    'Saída' => ['Saída de Estoque', 'Registro de Saídas'],
    'Solicitações' => ['Todas solicitações'],
    'Fichas EPI' => ['Gerenciar Fichas EPI'],
    'Limites de Gastos' => ['Configurar Limites', 'Relatório de Gastos', 'Alertas de Limites'],
    'Devolução' => ['Processar Devoluções', 'Histórico de Devoluções', 'Relatório de Devoluções'],
    'Fazer Pedido' => ['Requisições', 'Pedidos mensais', 'Produtos Pedido Mensal', 'Pedidos especiais'],
    'Meus Pedidos' => ['Pedidos'],
    'Usuários' => ['Cadastrar Usuário', 'Gerenciar Usuários']
];

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
foreach ($menus as $menu => $itens) {
    $cor = in_array($menu, ['Limites de Gastos', 'Devolução']) ? 'color: #007cba; font-weight: bold;' : '';
    echo "<div style='margin-bottom: 10px;'>";
    echo "<strong style='$cor'>📁 $menu</strong>";
    echo "<ul style='margin: 5px 0 0 20px;'>";
    foreach ($itens as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
    echo "</div>";
}
echo "</div>";

echo "<h3>🧪 Como Testar:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Acesse:</strong> <a href='gerenciar-usuarios.php' target='_blank'>gerenciar-usuarios.php</a></li>";
echo "<li><strong>Clique</strong> no botão 'Gerenciar Acessos' de qualquer usuário</li>";
echo "<li><strong>Verifique</strong> se aparecem as novas seções:</li>";
echo "<ul>";
echo "<li>✅ <strong>Limites de Gastos</strong> com 3 sub-itens</li>";
echo "<li>✅ <strong>Devolução</strong> com 3 sub-itens</li>";
echo "</ul>";
echo "<li><strong>Teste</strong> marcar/desmarcar as permissões</li>";
echo "<li><strong>Salve</strong> as alterações</li>";
echo "</ol>";
echo "</div>";

echo "<h3>💡 Benefícios das Novas Abas:</h3>";
echo "<ul>";
echo "<li><strong>Controle Granular:</strong> Permissões específicas para cada funcionalidade</li>";
echo "<li><strong>Segurança:</strong> Usuários só acessam o que foi autorizado</li>";
echo "<li><strong>Organização:</strong> Funcionalidades agrupadas logicamente</li>";
echo "<li><strong>Flexibilidade:</strong> Diferentes níveis de acesso por usuário</li>";
echo "</ul>";

echo "<p style='margin-top: 30px; text-align: center;'>";
echo "<a href='gerenciar-usuarios.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px;'>🔗 Testar Sistema de Acessos</a>";
echo "</p>";

$conn->close();
?>

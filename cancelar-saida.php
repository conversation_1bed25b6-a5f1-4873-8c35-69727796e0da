<?php
include 'conexao.php';

// Receber dados do POST
$data = json_decode(file_get_contents('php://input'), true);
$saida_id = $data['saida_id'];

// Iniciar transação
$conn->begin_transaction();

try {
    // Verificar se a coluna 'status' existe na tabela 'saidas_estoque'
    $result = $conn->query("SHOW COLUMNS FROM saidas_estoque LIKE 'status'");
    if ($result->num_rows === 0) {
        // A coluna não existe, vamos criá-la
        $conn->query("ALTER TABLE saidas_estoque ADD COLUMN status VARCHAR(20) DEFAULT 'ativa'");
    }
    
    // Verificar se a saída existe e está ativa
    $stmt = $conn->prepare("SELECT status FROM saidas_estoque WHERE id = ?");
    $stmt->bind_param("i", $saida_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Saída não encontrada");
    }
    
    $row = $result->fetch_assoc();
    if (isset($row['status']) && $row['status'] === 'cancelada') {
        throw new Exception("Esta saída já foi cancelada");
    }
    
    // Obter produtos da saída
    $stmt = $conn->prepare("SELECT codigo, quantidade FROM produtos_saida WHERE saida_id = ?");
    $stmt->bind_param("i", $saida_id);
    $stmt->execute();
    $produtos = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Devolver quantidades ao estoque
    foreach ($produtos as $produto) {
        $stmt = $conn->prepare("UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?");
        $stmt->bind_param("is", $produto['quantidade'], $produto['codigo']);
        $stmt->execute();
        
        // Verificar se a atualização foi bem-sucedida
        if ($stmt->affected_rows === 0) {
            throw new Exception("Erro ao atualizar estoque do produto {$produto['codigo']}");
        }
    }
    
    // Marcar a saída como cancelada
    $stmt = $conn->prepare("UPDATE saidas_estoque SET status = 'cancelada' WHERE id = ?");
    $stmt->bind_param("i", $saida_id);
    $stmt->execute();
    
    // Commit da transação
    $conn->commit();
    
    echo json_encode(['success' => true, 'message' => 'Saída cancelada com sucesso']);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => 'Erro ao cancelar saída: ' . $e->getMessage()]);
}
?>


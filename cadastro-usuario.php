<?php
session_start();

// Verificar se o usuário está logado e é administrador
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'administrador') {
    header("Location: login.php");
    exit();
}

$mensagem = "";
$tipo_mensagem = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    include 'conexao.php';
    
    $nome_usuario = $_POST['nome_usuario'];
    $senha = $_POST['senha'];
    $confirmar_senha = $_POST['confirmar_senha'];
    $tipo_usuario = $_POST['tipo_usuario'];
    
    // Validações
    if (strlen($nome_usuario) < 3) {
        $mensagem = "Nome de usuário deve ter pelo menos 3 caracteres!";
        $tipo_mensagem = "erro";
    } elseif (strlen($senha) < 6) {
        $mensagem = "Senha deve ter pelo menos 6 caracteres!";
        $tipo_mensagem = "erro";
    } elseif ($senha !== $confirmar_senha) {
        $mensagem = "As senhas não coincidem!";
        $tipo_mensagem = "erro";
    } else {
        // Verificar se o nome de usuário já existe
        $sql_verificar = "SELECT id FROM usuarios WHERE nome_usuario = ?";
        $stmt = $conn->prepare($sql_verificar);
        $stmt->bind_param("s", $nome_usuario);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $mensagem = "Nome de usuário já existe!";
            $tipo_mensagem = "erro";
        } else {
            // Criar hash da senha
            $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
            
            // Inserir usuário
            $sql_inserir = "INSERT INTO usuarios (nome_usuario, senha, tipo_usuario) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql_inserir);
            $stmt->bind_param("sss", $nome_usuario, $senha_hash, $tipo_usuario);
            
            if ($stmt->execute()) {
                $mensagem = "Usuário cadastrado com sucesso!";
                $tipo_mensagem = "sucesso";
                
                // Limpar formulário
                $_POST = array();
            } else {
                $mensagem = "Erro ao cadastrar usuário: " . $conn->error;
                $tipo_mensagem = "erro";
            }
        }
    }
    
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Usuário - Sistema de Estoque</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(120deg, #111 0%, #162447 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-submit {
            width: 100%;
            padding: 12px;
            background: #162447;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn-submit:hover {
            transform: translateY(-2px);
        }
        
        .btn-voltar {
            display: inline-block;
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: background 0.3s;
        }
        
        .btn-voltar:hover {
            background: #5a6268;
        }
        
        .mensagem {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .mensagem.sucesso {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .mensagem.erro {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .tipo-usuario-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .tipo-usuario-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .tipo-usuario-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .tipo-usuario-info li {
            margin-bottom: 5px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Cadastro de Usuário</h1>
            <p>Crie uma nova conta de usuário no sistema</p>
        </div>
        
        <?php if ($mensagem): ?>
            <div class="mensagem <?php echo $tipo_mensagem; ?>">
                <?php echo $mensagem; ?>
            </div>
        <?php endif; ?>
        
        <div class="tipo-usuario-info">
            <h3>Tipos de Usuário:</h3>
            <ul>
                <li><strong>Administrador:</strong> Acesso total ao sistema, pode gerenciar usuários</li>
                <li><strong>Supervisor:</strong> Pode visualizar relatórios e gerenciar estoque</li>
                <li><strong>Almoxarife:</strong> Pode fazer entradas e saídas de estoque</li>
                <li><strong>Personalizado:</strong> Permissões definidas manualmente pelo administrador</li>
            </ul>
        </div>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="nome_usuario">Nome de Usuário:</label>
                <input type="text" id="nome_usuario" name="nome_usuario" value="<?php echo isset($_POST['nome_usuario']) ? htmlspecialchars($_POST['nome_usuario']) : ''; ?>" required>
            </div>
            
            <div class="form-group">
                <label for="senha">Senha:</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            
            <div class="form-group">
                <label for="confirmar_senha">Confirmar Senha:</label>
                <input type="password" id="confirmar_senha" name="confirmar_senha" required>
            </div>
            
            <div class="form-group">
                <label for="tipo_usuario">Tipo de Usuário:</label>
                <select id="tipo_usuario" name="tipo_usuario" required>
                    <option value="">Selecione o tipo</option>
                    <option value="administrador" <?php echo (isset($_POST['tipo_usuario']) && $_POST['tipo_usuario'] == 'administrador') ? 'selected' : ''; ?>>Administrador</option>
                    <option value="supervisor" <?php echo (isset($_POST['tipo_usuario']) && $_POST['tipo_usuario'] == 'supervisor') ? 'selected' : ''; ?>>Supervisor</option>
                    <option value="almoxarife" <?php echo (isset($_POST['tipo_usuario']) && $_POST['tipo_usuario'] == 'almoxarife') ? 'selected' : ''; ?>>Almoxarife</option>
                    <option value="personalizado" <?php echo (isset($_POST['tipo_usuario']) && $_POST['tipo_usuario'] == 'personalizado') ? 'selected' : ''; ?>>Personalizado</option>
                </select>
            </div>
            
            <button type="submit" class="btn-submit">Cadastrar Usuário</button>
        </form>
        
        <a href="gerenciar-usuarios.php" class="btn-voltar">Gerenciar Usuários</a>
        <a href="index.php" class="btn-voltar" style="margin-left: 10px;">Voltar ao Início</a>
    </div>
</body>
</html> 
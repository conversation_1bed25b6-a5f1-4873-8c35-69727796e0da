<?php
include 'conexao.php';
if (!isset($_GET['id'])) {
    http_response_code(400);
    echo 'ID não fornecido.';
    exit;
}
$id = intval($_GET['id']);
$stmt = $conn->prepare('SELECT assinatura_eletronica FROM saidas_estoque WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();
$stmt->store_result();
if ($stmt->num_rows === 0) {
    http_response_code(404);
    echo 'Assinatura não encontrada.';
    exit;
}
$stmt->bind_result($assinatura);
$stmt->fetch();
if (empty($assinatura)) {
    http_response_code(404);
    echo 'Assinatura não encontrada.';
    exit;
}
// Detectar tipo
$finfo = new finfo(FILEINFO_MIME_TYPE);
$mime = $finfo->buffer($assinatura);
header('Content-Type: ' . $mime);
header('Content-Disposition: inline; filename="assinatura.' . (strpos($mime, 'pdf') !== false ? 'pdf' : 'png') . '"');
echo $assinatura; 
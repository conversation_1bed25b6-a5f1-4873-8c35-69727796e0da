<?php
include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: requisicoes-feitas.php');
    exit;
}

// Obter dados do formulário
$codigo_solicitacao = $_POST['codigo_solicitacao'];
$solicitante = $_POST['solicitante'];
$empresa = $_POST['empresa'];
$contrato = $_POST['contrato'];
$finalidade = $_POST['finalidade'];
$funcionario = $_POST['funcionario'];
$funcao = $_POST['funcao'];
$observacao = $_POST['observacao'];

// Atualizar a requisição
$stmt = $conn->prepare("UPDATE requisicoes SET 
    solicitante = ?, 
    empresa = ?, 
    contrato = ?, 
    finalidade = ?, 
    funcionario = ?, 
    funcao = ?, 
    observacao = ? 
    WHERE codigo_solicitacao = ?");

$stmt->bind_param("sssssssi", 
    $solicitante, 
    $empresa, 
    $contrato, 
    $finalidade, 
    $funcionario, 
    $funcao, 
    $observacao, 
    $codigo_solicitacao
);

$success = $stmt->execute();

// Atualizar os itens da requisição
if ($success && isset($_POST['produto']) && isset($_POST['quantidade'])) {
    $produtos = $_POST['produto'];
    $quantidades = $_POST['quantidade'];
    $item_ids = isset($_POST['item_id']) ? $_POST['item_id'] : [];
    
    // Primeiro, remover todos os itens existentes
    $stmt = $conn->prepare("DELETE FROM itens_solicitacao WHERE codigo_solicitacao = ?");
    $stmt->bind_param("i", $codigo_solicitacao);
    $stmt->execute();
    
    // Depois, inserir os itens atualizados
    $stmt = $conn->prepare("INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, ?, ?)");
    
    foreach ($produtos as $index => $produto) {
        if (!empty($produto)) {
            $quantidade = $quantidades[$index];
            $stmt->bind_param("isi", $codigo_solicitacao, $produto, $quantidade);
            $stmt->execute();
        }
    }
}

// Redirecionar de volta para a página de requisições
header('Location: requisicoes-feitas.php?status=' . ($success ? 'success' : 'error'));
exit;
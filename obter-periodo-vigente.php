<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';
include_once 'periodos-limite-helper.php';

$id_limite = isset($_GET['id_limite']) ? intval($_GET['id_limite']) : (isset($_POST['id_limite']) ? intval($_POST['id_limite']) : 0);
if (!$id_limite) {
    echo json_encode(['success' => false, 'message' => 'id_limite não informado']);
    exit();
}

$sql = "SELECT criado_em, periodicidade FROM limites_gastos WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $id_limite);
$stmt->execute();
$res = $stmt->get_result();
if ($row = $res->fetch_assoc()) {
    $periodo = calcularPeriodoVigente($row['criado_em'], $row['periodicidade']);
    echo json_encode(['success' => true, 'data_inicio' => $periodo['data_inicio'], 'data_fim' => $periodo['data_fim']]);
} else {
    echo json_encode(['success' => false, 'message' => 'Limite não encontrado']);
}
$stmt->close(); 
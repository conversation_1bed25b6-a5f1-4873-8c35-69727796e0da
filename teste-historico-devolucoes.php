<?php
require_once 'conexao.php';

echo "<h2>Teste - Histórico de Devoluções</h2>";

// 1. Verificar se existem devoluções registradas
echo "<h3>📊 Verificação das Tabelas de Devolução:</h3>";

// Tabela devolucoes_epi (por funcionário)
$result_func = $conn->query("SHOW TABLES LIKE 'devolucoes_epi'");
if ($result_func->num_rows > 0) {
    $count_func = $conn->query("SELECT COUNT(*) as total FROM devolucoes_epi")->fetch_assoc()['total'];
    echo "<p>✅ <strong>devolucoes_epi:</strong> $count_func registros</p>";
    
    if ($count_func > 0) {
        echo "<h4>Últimas 3 devoluções por funcionário:</h4>";
        $result = $conn->query("SELECT * FROM devolucoes_epi ORDER BY data_devolucao DESC LIMIT 3");
        echo "<table border='1' style='border-collapse: collapse; background: #e8f5e8;'>";
        echo "<tr><th>ID</th><th>Pessoa</th><th>Produto</th><th>Qtd</th><th>Estado</th><th>Data</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['pessoa_nome']}</td>";
            echo "<td>{$row['produto_nome']}</td>";
            echo "<td>{$row['quantidade']}</td>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($row['data_devolucao'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>⚠️ <strong>devolucoes_epi:</strong> Tabela não existe</p>";
}

// Tabela devolucoes_rapidas
$result_rapida = $conn->query("SHOW TABLES LIKE 'devolucoes_rapidas'");
if ($result_rapida->num_rows > 0) {
    $count_rapida = $conn->query("SELECT COUNT(*) as total FROM devolucoes_rapidas")->fetch_assoc()['total'];
    echo "<p>✅ <strong>devolucoes_rapidas:</strong> $count_rapida registros</p>";
    
    if ($count_rapida > 0) {
        echo "<h4>Últimas 3 devoluções rápidas:</h4>";
        $result = $conn->query("SELECT * FROM devolucoes_rapidas ORDER BY data_devolucao DESC LIMIT 3");
        echo "<table border='1' style='border-collapse: collapse; background: #fff3cd;'>";
        echo "<tr><th>ID</th><th>Produto</th><th>Qtd</th><th>Estado</th><th>Data</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['produto_nome']}</td>";
            echo "<td>{$row['quantidade']}</td>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($row['data_devolucao'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>⚠️ <strong>devolucoes_rapidas:</strong> Tabela não existe</p>";
}

// 2. Testar endpoints
echo "<h3>🌐 Teste dos Endpoints:</h3>";
echo "<ul>";
echo "<li><a href='obter-historico-devolucoes.php' target='_blank'>obter-historico-devolucoes.php</a> - Lista histórico completo</li>";

// Se existir pelo menos uma devolução, testar detalhes
if ($result_func->num_rows > 0 && $count_func > 0) {
    $primeira_dev = $conn->query("SELECT id FROM devolucoes_epi ORDER BY data_devolucao DESC LIMIT 1")->fetch_assoc();
    if ($primeira_dev) {
        echo "<li><a href='obter-detalhes-devolucao.php?id={$primeira_dev['id']}&tipo=funcionario' target='_blank'>obter-detalhes-devolucao.php</a> - Detalhes de devolução</li>";
        echo "<li><a href='gerar-pdf-devolucao.php?id={$primeira_dev['id']}&tipo=funcionario' target='_blank'>gerar-pdf-devolucao.php</a> - Gerar PDF</li>";
    }
}

if ($result_rapida->num_rows > 0 && $count_rapida > 0) {
    $primeira_rapida = $conn->query("SELECT id FROM devolucoes_rapidas ORDER BY data_devolucao DESC LIMIT 1")->fetch_assoc();
    if ($primeira_rapida) {
        echo "<li><a href='obter-detalhes-devolucao.php?id={$primeira_rapida['id']}&tipo=rapida' target='_blank'>obter-detalhes-devolucao.php (rápida)</a> - Detalhes de devolução rápida</li>";
        echo "<li><a href='gerar-pdf-devolucao.php?id={$primeira_rapida['id']}&tipo=rapida' target='_blank'>gerar-pdf-devolucao.php (rápida)</a> - Gerar PDF rápida</li>";
    }
}
echo "</ul>";

// 3. Verificar se campos de assinatura existem
echo "<h3>✍️ Verificação dos Campos de Assinatura:</h3>";

if ($result_func->num_rows > 0) {
    $result_assinatura_func = $conn->query("SHOW COLUMNS FROM devolucoes_epi LIKE 'assinatura'");
    echo "<p>Campo assinatura em devolucoes_epi: " . ($result_assinatura_func->num_rows > 0 ? "✅ Existe" : "⚠️ Será criado automaticamente") . "</p>";
}

if ($result_rapida->num_rows > 0) {
    $result_assinatura_rapida = $conn->query("SHOW COLUMNS FROM devolucoes_rapidas LIKE 'assinatura'");
    echo "<p>Campo assinatura em devolucoes_rapidas: " . ($result_assinatura_rapida->num_rows > 0 ? "✅ Existe" : "⚠️ Será criado automaticamente") . "</p>";
}

echo "<h3>🎯 Funcionalidades Implementadas:</h3>";
echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;'>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
echo "<h4>📋 Aba Histórico</h4>";
echo "<ul>";
echo "<li>✅ Tabela com todas as devoluções</li>";
echo "<li>✅ Busca em tempo real</li>";
echo "<li>✅ Botão de detalhes</li>";
echo "<li>✅ Ordenação por data</li>";
echo "<li>✅ Tipos diferenciados</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px;'>";
echo "<h4>🔍 Popup de Detalhes</h4>";
echo "<ul>";
echo "<li>✅ Informações completas</li>";
echo "<li>✅ Dados do funcionário</li>";
echo "<li>✅ Dados do produto</li>";
echo "<li>✅ Botão PDF no canto superior</li>";
echo "<li>✅ Campo de assinatura</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px;'>";
echo "<h4>✍️ Sistema de Assinatura</h4>";
echo "<ul>";
echo "<li>✅ Canvas para desenhar</li>";
echo "<li>✅ Suporte touch (mobile)</li>";
echo "<li>✅ Botão limpar</li>";
echo "<li>✅ Salvar no banco</li>";
echo "<li>✅ Exibir no PDF</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h3>🧪 Como Testar:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Fazer algumas devoluções:</strong>";
echo "<ul>";
echo "<li>Use a aba 'Devolução por Funcionário' para criar registros</li>";
echo "<li>Use a aba 'Devolução Rápida' para criar registros</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Acessar aba 'Histórico de Devoluções':</strong>";
echo "<ul>";
echo "<li>Deve mostrar todas as devoluções em ordem cronológica</li>";
echo "<li>Testar busca digitando nomes/produtos</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Clicar em 'Detalhes':</strong>";
echo "<ul>";
echo "<li>Popup deve abrir com informações completas</li>";
echo "<li>Testar botão PDF (canto superior direito)</li>";
echo "<li>Fazer assinatura no canvas</li>";
echo "<li>Salvar assinatura</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

if (($count_func ?? 0) == 0 && ($count_rapida ?? 0) == 0) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<h4>⚠️ Nenhuma devolução encontrada</h4>";
    echo "<p>Para testar o histórico, primeiro faça algumas devoluções:</p>";
    echo "<ol>";
    echo "<li>Acesse a aba 'Devolução por Funcionário' e faça uma devolução</li>";
    echo "<li>Acesse a aba 'Devolução Rápida' e faça uma devolução</li>";
    echo "<li>Depois volte para testar o histórico</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Testar Histórico de Devoluções</a>";
echo "</p>";

$conn->close();
?>

<?php
include 'conexao.php'; // Inclui a conexão com o banco de dados

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Obtém os dados do formulário
    $solicitante = $_POST['solicitante'];
    $empresa = $_POST['empresaSelecionada'];
    $contrato = $_POST['contrato'];
    $finalidade = $_POST['finalidade'];
    $detalhes = $_POST['detalhes'];
    $imagem_path = $_POST['imagem_path'] ?? null;
    
    // Processar upload de imagem, se houver
    if (isset($_FILES['imagem']) && $_FILES['imagem']['error'] == 0) {
        $upload_dir = 'uploads/pedidos_especiais/';
        
        // Criar diretório se não existir
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        // Gerar nome único para o arquivo
        $imagem_nome = time() . '_' . $_FILES['imagem']['name'];
        $imagem_path = $upload_dir . $imagem_nome;
        
        // Mover o arquivo para o diretório de uploads
        if (move_uploaded_file($_FILES['imagem']['tmp_name'], $imagem_path)) {
            // Arquivo movido com sucesso
        } else {
            echo "Erro ao fazer upload da imagem.";
            exit;
        }
    }

    // Insere o pedido na tabela pedidos_especiais
    $sql = "INSERT INTO pedidos_especiais (solicitante, empresa, contrato, finalidade, detalhes, imagem_path) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("ssssss", $solicitante, $empresa, $contrato, $finalidade, $detalhes, $imagem_path);
        
        if ($stmt->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Pedido especial salvo com sucesso!']);
            exit();
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Erro ao salvar o pedido: ' . $stmt->error]);
            exit();
        }
    } else {
        echo "Erro na preparação da consulta: " . $conn->error;
    }
}
?>
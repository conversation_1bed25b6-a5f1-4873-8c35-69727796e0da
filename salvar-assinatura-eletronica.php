<?php
include 'conexao.php';
header('Content-Type: application/json');
$data = json_decode(file_get_contents('php://input'), true);
$id = isset($data['id']) ? intval($data['id']) : 0;
$assinatura = isset($data['assinatura']) ? $data['assinatura'] : '';
if (!$id || !$assinatura) {
    echo json_encode(['success' => false, 'message' => 'Dados inválidos.']);
    exit;
}
// Extrair base64
if (strpos($assinatura, 'base64,') !== false) {
    $assinatura = explode('base64,', $assinatura, 2)[1];
}
$bin = base64_decode($assinatura);
$stmt = $conn->prepare('UPDATE saidas_estoque SET assinatura_eletronica = ?, data_assinatura_eletronica = NOW() WHERE id = ?');
$stmt->bind_param('si', $bin, $id);
$ok = $stmt->execute();
echo json_encode(['success' => $ok]); 
<?php
include 'conexao.php';

// Verificar estrutura da tabela limites_gastos
$sql = "DESCRIBE limites_gastos";
$result = $conn->query($sql);

echo "<h2>Estrutura atual da tabela limites_gastos:</h2>";
echo "<table border='1'>";
echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

while ($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "<td>" . $row['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Verificar se existem dados na tabela
$sql_count = "SELECT COUNT(*) as total FROM limites_gastos";
$result_count = $conn->query($sql_count);
$row_count = $result_count->fetch_assoc();

echo "<h2>Total de registros na tabela: " . $row_count['total'] . "</h2>";

// Mostrar alguns registros de exemplo
$sql_sample = "SELECT * FROM limites_gastos LIMIT 5";
$result_sample = $conn->query($sql_sample);

if ($result_sample->num_rows > 0) {
    echo "<h2>Registros de exemplo:</h2>";
    echo "<table border='1'>";
    
    // Cabeçalho
    $first_row = $result_sample->fetch_assoc();
    echo "<tr>";
    foreach ($first_row as $key => $value) {
        echo "<th>" . $key . "</th>";
    }
    echo "</tr>";
    
    // Primeira linha
    echo "<tr>";
    foreach ($first_row as $value) {
        echo "<td>" . htmlspecialchars($value) . "</td>";
    }
    echo "</tr>";
    
    // Restante das linhas
    while ($row = $result_sample->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Nenhum registro encontrado na tabela.</p>";
}

$conn->close();
?> 
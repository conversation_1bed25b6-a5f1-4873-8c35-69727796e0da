<?php
include 'conexao.php';

// Verificar se o código foi fornecido
if (!isset($_GET['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Código não fornecido']);
    exit;
}

$codigo = $_GET['codigo'];

// Consultar o pedido mensal
$stmt = $conn->prepare("SELECT p.*, e.nome_empresa FROM pedidos_mensais p LEFT JOIN empresas e ON p.empresa = e.codigo_empresa WHERE p.codigo_pedido = ?");
$stmt->bind_param("i", $codigo);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Pedido mensal não encontrado']);
    exit;
}

$pedido = $result->fetch_assoc();

// Se não existir a coluna status, definir como pendente
if (!isset($pedido['status'])) {
    $pedido['status'] = 'pendente';
}

// Adicionar campos explícitos para código da empresa e id do destinatário
$pedido['codigo_empresa'] = $pedido['empresa'];
$pedido['id_destinatario'] = $pedido['destinatario'];

// Buscar setor do destinatário, se id_destinatario estiver presente
if (!empty($pedido['destinatario'])) {
    $stmt_dest = $conn->prepare("SELECT setor FROM pessoas WHERE id = ? LIMIT 1");
    $stmt_dest->bind_param("i", $pedido['destinatario']);
    $stmt_dest->execute();
    $result_dest = $stmt_dest->get_result();
    if ($row_dest = $result_dest->fetch_assoc()) {
        $pedido['setor_destinatario'] = $row_dest['setor'];
    } else {
        $pedido['setor_destinatario'] = '';
    }
    $stmt_dest->close();
} else {
    $pedido['setor_destinatario'] = '';
}

// Consultar os itens do pedido mensal
$stmt = $conn->prepare("
    SELECT i.*, p.nome as nome_produto 
    FROM itens_pedido_mensal i
    LEFT JOIN produtos p ON i.produto = p.codigo
    WHERE i.codigo_pedido = ?
");
$stmt->bind_param("i", $codigo);
$stmt->execute();
$result = $stmt->get_result();

$itens = [];
while ($row = $result->fetch_assoc()) {
    $itens[] = $row;
}

echo json_encode(['success' => true, 'pedido' => $pedido, 'itens' => $itens]);
?>



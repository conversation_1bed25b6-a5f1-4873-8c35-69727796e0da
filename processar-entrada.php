<?php
include 'conexao.php';

// Recebe os dados da requisição
$responsavel = $_POST['responsavel'] ?? '';
$produtos = json_decode($_POST['produtos'] ?? '[]', true);
$codigo_empresa = $_POST['codigo_empresa'] ?? null;

if (empty($produtos)) {
    echo "Nenhum dado recebido!";
    exit();
}

// Criar um novo registro de entrada
$stmt = $conn->prepare("INSERT INTO entradas_estoque (responsavel, codigo_empresa) VALUES (?, ?)");
$stmt->bind_param("si", $responsavel, $codigo_empresa);
$stmt->execute();
$entrada_id = $conn->insert_id; // Obtém o ID da nova entrada

// Inserir os produtos na tabela produtos_entrada e atualizar estoque
$stmt = $conn->prepare("INSERT INTO produtos_entrada (entrada_id, codigo, nome, quantidade) VALUES (?, ?, ?, ?)");
$updateStmt = $conn->prepare("UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?");

foreach ($produtos as $produto) {
    // Inserindo na tabela produtos_entrada
    $stmt->bind_param("isss", $entrada_id, $produto['codigo'], $produto['nome'], $produto['quantidade']);
    $stmt->execute();

    // Atualizando a quantidade no estoque da tabela produtos
    $updateStmt->bind_param("is", $produto['quantidade'], $produto['codigo']);
    $updateStmt->execute();
}

echo "Entrada registrada com sucesso!";
?>

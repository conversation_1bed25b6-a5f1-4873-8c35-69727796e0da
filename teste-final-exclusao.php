<?php
include 'conexao.php';

echo "<h2>Teste Final - Criando <PERSON>quisi<PERSON> Urgente</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Final', '001', 'TESTE-FINAL', 'Teste final de exclusão', '1', 'Teste', 'TI', 'Requisição para teste final de exclusão', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'TESTE-FINAL', 1)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Teste Final Pronto!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Status:</strong> Urgente (fundo vermelho claro)</p>";
    echo "<p><strong>Próximos passos:</strong></p>";
    echo "<ol>";
    echo "<li>Ir para 'Todas as Solicitações'</li>";
    echo "<li>Localizar a requisição com fundo vermelho claro</li>";
    echo "<li>Clicar na requisição para abrir o popup</li>";
    echo "<li>Verificar se aparece o aviso '🚨 Pedido urgente após valor limite excedido'</li>";
    echo "<li>Clicar no botão 'Excluir Pedido Urgente'</li>";
    echo "<li>Confirmar a exclusão</li>";
    echo "<li>Verificar se não há mais erros de JavaScript</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p><a href='todas-solitacoes-estoque.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔗 Ir para Todas as Solicitações</a></p>";

$conn->close();
?>

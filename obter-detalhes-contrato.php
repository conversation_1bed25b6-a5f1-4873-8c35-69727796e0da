<?php
header('Content-Type: application/json');
include 'conexao.php';

$contrato = isset($_GET['contrato']) ? $_GET['contrato'] : '';
$mesFiltro = isset($_GET['mes_filtro']) ? $_GET['mes_filtro'] : '';

if (empty($contrato) || empty($mesFiltro)) {
    echo json_encode(['error' => 'Parâmetros inválidos.']);
    exit;
}

$conn->set_charset("utf8");

$detalhes = [];

// --- Lógica para descobrir as colunas de data ---
$col_data_requisicoes = 'data_hora'; // Valor padrão
$check_req_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
if ($check_req_data && $check_req_data->num_rows > 0) {
    $col = $check_req_data->fetch_assoc();
    $col_data_requisicoes = $col['Field'];
}

$col_data_pedidos_mensais = 'data_pedido'; // Valor padrão
$check_pm_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
if ($check_pm_data && $check_pm_data->num_rows > 0) {
    $col = $check_pm_data->fetch_assoc();
    $col_data_pedidos_mensais = $col['Field'];
}
// --- Fim da lógica das colunas de data ---

// 1. Saídas diretas
$sqlSaidas = "
    SELECT 
        p.nome as produto, 
        ps.quantidade, 
        p.valor as valor_unitario,
        (ps.quantidade * p.valor) as valor_total,
        s.data_saida as data,
        s.id as origem_id
    FROM saidas_estoque s
    JOIN produtos_saida ps ON s.id = ps.saida_id
    JOIN produtos p ON ps.codigo = p.codigo
    JOIN empresas e ON s.empresa_destino = e.nome_empresa
    WHERE e.contrato = ? 
    AND DATE_FORMAT(s.data_saida, '%Y-%m') = ?
    AND (s.status IS NULL OR s.status = 'ativa')
";
$stmt = $conn->prepare($sqlSaidas);
if ($stmt) {
    $stmt->bind_param('ss', $contrato, $mesFiltro);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $row['origem'] = 'Saída Direta';
        $detalhes[] = $row;
    }
    $stmt->close();
}

// 2. Requisições concluídas
$sqlReqs = "
    SELECT
        p.nome as produto,
        i.quantidade,
        p.valor as valor_unitario,
        (i.quantidade * p.valor) as valor_total,
        r.{$col_data_requisicoes} as data,
        r.codigo_solicitacao as origem_id
    FROM requisicoes r
    JOIN itens_solicitacao i ON r.codigo_solicitacao = i.codigo_solicitacao
    JOIN produtos p ON i.produto = p.codigo
    WHERE r.contrato = ?
    AND r.status = 'concluido'
    AND DATE_FORMAT(r.{$col_data_requisicoes}, '%Y-%m') = ?
";
$stmt = $conn->prepare($sqlReqs);
if ($stmt) {
    $stmt->bind_param('ss', $contrato, $mesFiltro);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $row['origem'] = 'Requisição';
        $detalhes[] = $row;
    }
    $stmt->close();
}

// 3. Pedidos Mensais concluídos
$sqlPMs = "
    SELECT
        p.nome as produto,
        i.quantidade,
        p.valor as valor_unitario,
        (i.quantidade * p.valor) as valor_total,
        pm.{$col_data_pedidos_mensais} as data,
        pm.codigo_pedido as origem_id
    FROM pedidos_mensais pm
    JOIN itens_pedido_mensal i ON pm.codigo_pedido = i.codigo_pedido
    JOIN produtos p ON i.produto = p.codigo
    WHERE pm.contrato = ?
    AND pm.status = 'concluido'
    AND DATE_FORMAT(pm.{$col_data_pedidos_mensais}, '%Y-%m') = ?
";
$stmt = $conn->prepare($sqlPMs);
if ($stmt) {
    $stmt->bind_param('ss', $contrato, $mesFiltro);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $row['origem'] = 'Pedido Mensal';
        $detalhes[] = $row;
    }
    $stmt->close();
}

// Sort results by date
if (!empty($detalhes)) {
    usort($detalhes, function($a, $b) {
        return strtotime($b['data']) - strtotime($a['data']);
    });
}

echo json_encode($detalhes);

$conn->close();
?> 
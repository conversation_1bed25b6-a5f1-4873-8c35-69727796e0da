<?php
require_once 'conexao.php';

echo "<h2>Corrigir Estados de Devolução</h2>";

try {
    // 1. Verificar problemas nos estados
    echo "<h3>1. Verificação dos Estados:</h3>";
    
    // Verificar devolucoes_epi
    $result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'");
    if ($result && $result->fetch_assoc()['count'] > 0) {
        echo "<h4>Tabela devolucoes_epi:</h4>";
        
        // Verificar estados problemáticos
        $sql_problemas = "SELECT id, estado, produto_nome FROM devolucoes_epi WHERE estado = '0' OR estado = '' OR estado IS NULL";
        $result_problemas = $conn->query($sql_problemas);
        
        if ($result_problemas && $result_problemas->num_rows > 0) {
            echo "<p style='color: red;'>❌ Encontrados " . $result_problemas->num_rows . " registros com estados problemáticos:</p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Produto</th><th>Estado Atual</th></tr>";
            
            while ($row = $result_problemas->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['produto_nome']}</td>";
                echo "<td>" . ($row['estado'] ?: 'NULL/Vazio') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Corrigir estados problemáticos
            echo "<p>Corrigindo estados problemáticos...</p>";
            $sql_corrigir = "UPDATE devolucoes_epi SET estado = 'descarte' WHERE estado = '0' OR estado = '' OR estado IS NULL";
            if ($conn->query($sql_corrigir)) {
                $affected = $conn->affected_rows;
                echo "<p style='color: green;'>✅ $affected registros corrigidos para 'descarte'</p>";
            } else {
                echo "<p style='color: red;'>❌ Erro ao corrigir: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ Nenhum estado problemático encontrado</p>";
        }
        
        // Mostrar distribuição de estados
        echo "<h5>Distribuição atual dos estados:</h5>";
        $sql_distribuicao = "SELECT estado, COUNT(*) as total FROM devolucoes_epi GROUP BY estado ORDER BY total DESC";
        $result_dist = $conn->query($sql_distribuicao);
        
        if ($result_dist) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Estado</th><th>Quantidade</th></tr>";
            while ($row = $result_dist->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['estado']}</td>";
                echo "<td>{$row['total']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Verificar devolucoes_rapidas
    $result = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'");
    if ($result && $result->fetch_assoc()['count'] > 0) {
        echo "<h4>Tabela devolucoes_rapidas:</h4>";
        
        $sql_problemas = "SELECT id, estado, produto_nome FROM devolucoes_rapidas WHERE estado = '0' OR estado = '' OR estado IS NULL";
        $result_problemas = $conn->query($sql_problemas);
        
        if ($result_problemas && $result_problemas->num_rows > 0) {
            echo "<p style='color: red;'>❌ Encontrados " . $result_problemas->num_rows . " registros com estados problemáticos</p>";
            
            $sql_corrigir = "UPDATE devolucoes_rapidas SET estado = 'descarte' WHERE estado = '0' OR estado = '' OR estado IS NULL";
            if ($conn->query($sql_corrigir)) {
                $affected = $conn->affected_rows;
                echo "<p style='color: green;'>✅ $affected registros corrigidos</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ Nenhum estado problemático encontrado</p>";
        }
    }
    
    // 2. Verificar usuários
    echo "<h3>2. Verificação dos Usuários:</h3>";
    
    $result_usuarios = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'");
    if ($result_usuarios && $result_usuarios->fetch_assoc()['count'] > 0) {
        echo "<p>✅ Tabela 'usuarios' existe</p>";
        
        $count_usuarios = $conn->query("SELECT COUNT(*) as total FROM usuarios")->fetch_assoc()['total'];
        echo "<p>Total de usuários cadastrados: $count_usuarios</p>";
        
        if ($count_usuarios == 0) {
            echo "<p style='color: orange;'>⚠️ Nenhum usuário cadastrado. Criando usuário padrão...</p>";
            
            $sql_usuario = "INSERT INTO usuarios (nome, email, senha, nivel) VALUES ('Administrador', '<EMAIL>', MD5('123456'), 'admin')";
            if ($conn->query($sql_usuario)) {
                echo "<p style='color: green;'>✅ Usuário administrador criado (email: <EMAIL>, senha: 123456)</p>";
            }
        } else {
            echo "<h5>Usuários cadastrados:</h5>";
            $result_lista = $conn->query("SELECT id, nome, email FROM usuarios LIMIT 5");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Nome</th><th>Email</th></tr>";
            while ($row = $result_lista->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['nome']}</td>";
                echo "<td>{$row['email']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ Tabela 'usuarios' não existe</p>";
        echo "<p>Criando tabela de usuários...</p>";
        
        $sql_create_usuarios = "
            CREATE TABLE usuarios (
                id INT AUTO_INCREMENT PRIMARY KEY,
                nome VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                senha VARCHAR(255) NOT NULL,
                nivel VARCHAR(50) DEFAULT 'user',
                status VARCHAR(20) DEFAULT 'ativo',
                data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ";
        
        if ($conn->query($sql_create_usuarios)) {
            echo "<p style='color: green;'>✅ Tabela 'usuarios' criada</p>";
            
            // Inserir usuário padrão
            $sql_usuario = "INSERT INTO usuarios (nome, email, senha, nivel) VALUES ('Administrador', '<EMAIL>', MD5('123456'), 'admin')";
            if ($conn->query($sql_usuario)) {
                echo "<p style='color: green;'>✅ Usuário administrador criado</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Erro ao criar tabela: " . $conn->error . "</p>";
        }
    }
    
    echo "<h3>3. Teste do Endpoint Corrigido:</h3>";
    echo "<p><a href='obter-historico-devolucoes.php' target='_blank'>Testar endpoint do histórico</a></p>";
    
    echo "<h3>4. Estados Válidos:</h3>";
    echo "<ul>";
    echo "<li><strong>novo</strong> - Novo / Sem Uso</li>";
    echo "<li><strong>usado_bom</strong> - Usado – Bom Estado</li>";
    echo "<li><strong>usado_leve</strong> - Usado – Desgaste leve</li>";
    echo "<li><strong>danificado_reparo</strong> - Danificado – Reparo Possível</li>";
    echo "<li><strong>danificado_irrecuperavel</strong> - Danificado – Irrecuperável</li>";
    echo "<li><strong>vencido</strong> - Vencido / Fora da Validade</li>";
    echo "<li><strong>descarte</strong> - Descarte</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Testar Histórico Corrigido</a>";
echo "</p>";

$conn->close();
?>

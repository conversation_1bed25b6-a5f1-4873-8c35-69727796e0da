<?php
include 'conexao.php';

echo "<h2>Teste do Popup Corrigido</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Popup Corrigido', '001', 'POPUP-CORR', 'Teste do popup corrigido', '1', 'Teste', 'TI', 'Requisição para testar popup corrigido', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'POPUP-CORR', 1)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>✅ Correções Aplicadas!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Correções implementadas:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Animação corrigida:</strong> Popup aparece diretamente no centro</li>";
    echo "<li>✅ <strong>Emojis removidos:</strong> Sem emojis coloridos de lixo e perigo</li>";
    echo "<li>✅ <strong>Animação suave:</strong> Apenas fade-in com escala sutil</li>";
    echo "<li>✅ <strong>Título limpo:</strong> 'Confirmar Exclusão' sem emojis</li>";
    echo "<li>✅ <strong>Design profissional:</strong> Visual mais limpo e corporativo</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;'>";
    echo "<h4>🧪 Como testar as correções:</h4>";
    echo "<ol>";
    echo "<li>Ir para 'Todas as Solicitações'</li>";
    echo "<li>Localizar a requisição com fundo vermelho claro</li>";
    echo "<li>Clicar na requisição para abrir o popup de detalhes</li>";
    echo "<li>Clicar no botão 'Excluir Pedido Urgente'</li>";
    echo "<li><strong>Verificar:</strong> Popup aparece diretamente no centro (sem movimento)</li>";
    echo "<li><strong>Verificar:</strong> Título sem emojis ('Confirmar Exclusão')</li>";
    echo "<li><strong>Verificar:</strong> Sem ícone grande de lixeira</li>";
    echo "<li>Testar os botões 'Cancelar' e 'Sim, Excluir'</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='todas-solitacoes-estoque.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Testar Popup Corrigido</a></p>";

$conn->close();
?>

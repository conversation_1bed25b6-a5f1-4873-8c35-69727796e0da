<?php
include 'conexao.php';

// Receber dados do POST
$data = json_decode(file_get_contents("php://input"), true);
$id = $data['id'];

// Iniciar transação para garantir integridade dos dados
$conn->begin_transaction();

try {
    // Verificar se o pedido existe
    $stmt = $conn->prepare("SELECT * FROM pedidos_especiais WHERE codigo_pedido = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Pedido especial não encontrado");
    }
    
    // Excluir o pedido especial
    $stmt = $conn->prepare("DELETE FROM pedidos_especiais WHERE codigo_pedido = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    
    if ($stmt->affected_rows === 0) {
        throw new Exception("Erro ao excluir o pedido especial");
    }
    
    // Commit da transação
    $conn->commit();
    
    // Retornar sucesso
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>

<?php
include 'conexao.php';
header('Content-Type: application/json');
$empresa = isset($_GET['empresa']) ? $conn->real_escape_string($_GET['empresa']) : '';
if (!$empresa) {
    echo json_encode(['success' => false, 'error' => 'Empresa não informada']);
    exit;
}
$res = $conn->query("SELECT id, nome FROM setor WHERE empresa = '$empresa' AND (status = 'ativo' OR status IS NULL) ORDER BY nome");
$setores = [];
while ($row = $res->fetch_assoc()) {
    $setores[] = [ 'id' => $row['id'], 'nome' => $row['nome'] ];
}
echo json_encode(['success' => true, 'setores' => $setores]); 
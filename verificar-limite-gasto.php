<?php
// Iniciar sessão apenas se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
header('Content-Type: application/json');
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

try {
    if (!isset($_SESSION['usuario_id'])) {
        echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
        exit();
    }

    include 'conexao.php';

    function verificarLimiteGasto($conn, $empresa, $setor = null, $valor_operacao = 0) {
        $avisos = [];
        
        // Verificar limite por empresa
        $sql_empresa = "SELECT lg.*, e.nome_empresa 
                        FROM limites_gastos lg 
                        LEFT JOIN empresas e ON lg.id_empresa = e.codigo_empresa 
                        WHERE lg.tipo = 'empresa' AND e.codigo_empresa = ?";
        $stmt_empresa = $conn->prepare($sql_empresa);
        $stmt_empresa->bind_param("i", $empresa);
        $stmt_empresa->execute();
        $result_empresa = $stmt_empresa->get_result();
        
        if ($result_empresa->num_rows > 0) {
            $limite_empresa = $result_empresa->fetch_assoc();
            
            // Calcular gastos já realizados para esta empresa no período
            $sql_gastos = "SELECT COALESCE(SUM(valor_total), 0) as total_gasto 
                           FROM gastos_realizados 
                           WHERE empresa = ? AND limite_id = ?";
            $stmt_gastos = $conn->prepare($sql_gastos);
            $stmt_gastos->bind_param("si", $empresa, $limite_empresa['id']);
            $stmt_gastos->execute();
            $result_gastos = $stmt_gastos->get_result();
            $gastos_empresa = $result_gastos->fetch_assoc();
            
            $total_gasto_empresa = $gastos_empresa['total_gasto'] + $valor_operacao;
            $saldo_empresa = $limite_empresa['valor_limite'] - $total_gasto_empresa;
            
            if ($total_gasto_empresa > $limite_empresa['valor_limite']) {
                $avisos[] = [
                    'tipo' => 'excedido',
                    'categoria' => 'empresa',
                    'nome' => $empresa,
                    'limite' => $limite_empresa['valor_limite'],
                    'gasto_atual' => $gastos_empresa['total_gasto'],
                    'gasto_projetado' => $total_gasto_empresa,
                    'excesso' => $total_gasto_empresa - $limite_empresa['valor_limite'],
                    'periodo' => $limite_empresa['periodicidade']
                ];
            } elseif ($saldo_empresa <= $limite_empresa['saldo_aviso']) {
                $avisos[] = [
                    'tipo' => 'aviso',
                    'categoria' => 'empresa',
                    'nome' => $empresa,
                    'limite' => $limite_empresa['valor_limite'],
                    'gasto_atual' => $gastos_empresa['total_gasto'],
                    'gasto_projetado' => $total_gasto_empresa,
                    'saldo_restante' => $saldo_empresa,
                    'periodo' => $limite_empresa['periodicidade']
                ];
            }
        }
        
        // Verificar limite por setor (se fornecido)
        if ($setor) {
            $sql_setor = "SELECT lg.*, s.nome 
                          FROM limites_gastos lg 
                          LEFT JOIN setor s ON lg.id_setor = s.id 
                          WHERE lg.tipo = 'setor' AND s.id = ?";
            $stmt_setor = $conn->prepare($sql_setor);
            $stmt_setor->bind_param("i", $setor);
            $stmt_setor->execute();
            $result_setor = $stmt_setor->get_result();
            
            if ($result_setor->num_rows > 0) {
                $limite_setor = $result_setor->fetch_assoc();
                
                // Calcular gastos já realizados para este setor no período
                $sql_gastos_setor = "SELECT COALESCE(SUM(valor_total), 0) as total_gasto 
                                     FROM gastos_realizados 
                                     WHERE setor = ? AND limite_id = ?";
                $stmt_gastos_setor = $conn->prepare($sql_gastos_setor);
                $stmt_gastos_setor->bind_param("si", $setor, $limite_setor['id']);
                $stmt_gastos_setor->execute();
                $result_gastos_setor = $stmt_gastos_setor->get_result();
                $gastos_setor = $result_gastos_setor->fetch_assoc();
                
                $total_gasto_setor = $gastos_setor['total_gasto'] + $valor_operacao;
                $saldo_setor = $limite_setor['valor_limite'] - $total_gasto_setor;
                
                if ($total_gasto_setor > $limite_setor['valor_limite']) {
                    $avisos[] = [
                        'tipo' => 'excedido',
                        'categoria' => 'setor',
                        'nome' => $setor,
                        'limite' => $limite_setor['valor_limite'],
                        'gasto_atual' => $gastos_setor['total_gasto'],
                        'gasto_projetado' => $total_gasto_setor,
                        'excesso' => $total_gasto_setor - $limite_setor['valor_limite'],
                        'periodo' => $limite_setor['periodicidade']
                    ];
                } elseif ($saldo_setor <= $limite_setor['saldo_aviso']) {
                    $avisos[] = [
                        'tipo' => 'aviso',
                        'categoria' => 'setor',
                        'nome' => $setor,
                        'limite' => $limite_setor['valor_limite'],
                        'gasto_atual' => $gastos_setor['total_gasto'],
                        'gasto_projetado' => $total_gasto_setor,
                        'saldo_restante' => $saldo_setor,
                        'periodo' => $limite_setor['periodicidade']
                    ];
                }
            }
        }
        
        return $avisos;
    }

    // Endpoint para verificar limite
    if (isset($_GET['empresa'])) {
        $empresa = intval($_GET['empresa']);
        $setor = isset($_GET['setor']) ? intval($_GET['setor']) : null;
        $valor = floatval($_GET['valor'] ?? 0);
        
        $avisos = verificarLimiteGasto($conn, $empresa, $setor, $valor);
        
        // Sempre retorna sucesso, mesmo se não houver limite cadastrado
        echo json_encode([
            'success' => true,
            'avisos' => $avisos,
            'tem_aviso' => count($avisos) > 0
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Parâmetros insuficientes']);
    }

    $conn->close();
} catch (Throwable $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}
?> 
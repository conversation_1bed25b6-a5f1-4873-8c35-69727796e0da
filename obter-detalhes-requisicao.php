<?php
include 'conexao.php';

// Verificar se o código foi fornecido
if (!isset($_GET['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Código não fornecido']);
    exit;
}

$codigo = $_GET['codigo'];

// Consultar a requisição
$stmt = $conn->prepare("SELECT r.*, e.nome_empresa FROM requisicoes r LEFT JOIN empresas e ON r.empresa = e.codigo_empresa WHERE r.codigo_solicitacao = ?");
$stmt->bind_param("i", $codigo);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Requisição não encontrada']);
    exit;
}

$requisicao = $result->fetch_assoc();

// Se não existir a coluna status, definir como pendente
if (!isset($requisicao['status'])) {
    $requisicao['status'] = 'pendente';
}

// Adicionar campos explícitos para código da empresa e id do funcionário
$requisicao['codigo_empresa'] = $requisicao['empresa'];
$requisicao['id_funcionario'] = $requisicao['funcionario'];

// Buscar nome e setor do funcionário, se id_funcionario estiver presente
if (!empty($requisicao['funcionario'])) {
    $stmt_func = $conn->prepare("SELECT nome, setor FROM pessoas WHERE id = ? LIMIT 1");
    $stmt_func->bind_param("i", $requisicao['funcionario']);
    $stmt_func->execute();
    $result_func = $stmt_func->get_result();
    if ($row_func = $result_func->fetch_assoc()) {
        $requisicao['nome_funcionario'] = $row_func['nome'];
        $requisicao['setor_funcionario'] = $row_func['setor'];
    } else {
        $requisicao['nome_funcionario'] = '';
        $requisicao['setor_funcionario'] = '';
    }
    $stmt_func->close();
} else {
    $requisicao['nome_funcionario'] = '';
    $requisicao['setor_funcionario'] = '';
}

// Consultar os itens da requisição
$stmt = $conn->prepare("
    SELECT i.*, p.nome as nome_produto 
    FROM itens_solicitacao i
    LEFT JOIN produtos p ON i.produto = p.codigo
    WHERE i.codigo_solicitacao = ?
");
$stmt->bind_param("i", $codigo);
$stmt->execute();
$result = $stmt->get_result();

$itens = [];
while ($row = $result->fetch_assoc()) {
    $itens[] = $row;
}

echo json_encode(['success' => true, 'requisicao' => $requisicao, 'itens' => $itens]);
?>



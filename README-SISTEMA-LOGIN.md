# Sistema de Login - Sistema de Estoque

## Funcionalidades Implementadas

### 1. Sistema de Autenticação
- **Login**: Página de login com validação de usuário e senha
- **Logout**: Sistema de logout seguro
- **Sessões**: Controle de sessões para manter o usuário logado

### 2. Tipos de Usuário
- **Administrador**: Acesso total ao sistema, pode gerenciar usuários
- **Supervisor**: Pode visualizar relatórios e gerenciar estoque
- **Almoxarife**: Pode fazer entradas e saídas de estoque

### 3. Gerenciamento de Usuários (Apenas Administradores)
- **Cadastro de Usuários**: Criar novos usuários com validações
- **Gerenciar Usuários**: Visualizar, ativar/inativar, alterar senha e excluir usuários
- **Controle de Acesso**: Diferentes níveis de permissão por tipo de usuário

## Arquivos Criados

### 1. `criar-tabela-usuarios.php`
- Cria a tabela `usuarios` no banco de dados
- Insere um usuário administrador padrão (admin/admin123)

### 2. `login.php`
- Página de login com interface moderna
- Validação de credenciais
- Redirecionamento automático se já logado

### 3. `cadastro-usuario.php`
- Formulário para criar novos usuários
- Validações de senha e nome de usuário
- Acesso restrito a administradores

### 4. `gerenciar-usuarios.php`
- Lista todos os usuários cadastrados
- Funcionalidades de ativar/inativar, alterar senha e excluir
- Interface com modais para ações

### 5. `logout.php`
- Sistema de logout seguro
- Destruição de sessões e cookies

### 6. `verificar-login.php`
- Arquivo de verificação de autenticação
- Funções para verificar diferentes níveis de permissão

## Como Usar

### 1. Primeira Configuração
1. Execute o arquivo `criar-tabela-usuarios.php` para criar a tabela de usuários
2. O sistema criará automaticamente um usuário administrador:
   - **Login**: admin
   - **Senha**: admin123

### 2. Acesso ao Sistema
1. Acesse `login.php` no navegador
2. Use as credenciais do administrador padrão
3. Após o login, você será redirecionado para `index.php`

### 3. Gerenciamento de Usuários
1. Faça login como administrador
2. No menu superior, clique em "Usuários"
3. Escolha entre "Cadastrar Usuário" ou "Gerenciar Usuários"

### 4. Funcionalidades por Tipo de Usuário

#### Administrador
- Acesso total ao sistema
- Pode gerenciar usuários (criar, editar, excluir)
- Pode ativar/inativar usuários
- Pode alterar senhas de outros usuários

#### Supervisor
- Pode visualizar relatórios
- Pode gerenciar estoque
- Não pode gerenciar usuários

#### Almoxarife
- Pode fazer entradas e saídas de estoque
- Acesso limitado às funcionalidades de estoque

## Segurança

### 1. Senhas
- Todas as senhas são criptografadas usando `password_hash()`
- Validação de senha mínima (6 caracteres)
- Confirmação de senha no cadastro

### 2. Sessões
- Controle de sessões para manter usuário logado
- Verificação de autenticação em todas as páginas
- Logout seguro com destruição de sessões

### 3. Permissões
- Controle de acesso baseado no tipo de usuário
- Redirecionamento automático para usuários não autorizados
- Proteção contra acesso direto a arquivos

## Estrutura da Tabela de Usuários

```sql
CREATE TABLE usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome_usuario VARCHAR(100) NOT NULL UNIQUE,
    senha VARCHAR(255) NOT NULL,
    tipo_usuario ENUM('administrador', 'supervisor', 'almoxarife') NOT NULL,
    data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo'
);
```

## Personalização

### 1. Adicionar Novos Tipos de Usuário
1. Modifique o ENUM na tabela `usuarios`
2. Atualize as funções de verificação em `verificar-login.php`
3. Adicione as verificações necessárias nas páginas

### 2. Modificar Permissões
1. Edite as funções `verificarAdmin()`, `verificarSupervisor()` e `verificarAlmoxarife()`
2. Ajuste as verificações nas páginas conforme necessário

### 3. Personalizar Interface
1. Modifique os estilos CSS nos arquivos
2. Ajuste as cores e layout conforme necessário

## Troubleshooting

### 1. Problemas de Login
- Verifique se a tabela `usuarios` foi criada corretamente
- Confirme se o usuário está ativo no sistema
- Verifique se as credenciais estão corretas

### 2. Problemas de Permissão
- Verifique se o tipo de usuário está correto
- Confirme se as funções de verificação estão sendo chamadas
- Verifique se o arquivo `verificar-login.php` está sendo incluído

### 3. Problemas de Sessão
- Verifique se o PHP está configurado para usar sessões
- Confirme se os cookies estão habilitados no navegador
- Verifique se não há conflitos de sessão

## Próximos Passos

1. **Implementar Recuperação de Senha**: Sistema de reset de senha por email
2. **Log de Atividades**: Registrar ações dos usuários no sistema
3. **Backup de Usuários**: Sistema de backup automático da tabela de usuários
4. **Autenticação de Dois Fatores**: Implementar 2FA para maior segurança
5. **Política de Senhas**: Implementar regras mais rigorosas para senhas 
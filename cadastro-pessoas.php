<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Cadastro de Pessoas</title>
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        /* Título minimalista */
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        /* Formulário minimalista */
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        /* Grupos de campos */
        .form-group {
            margin-bottom: 24px;
        }
        
        /* Labels minimalistas */
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        
        /* Inputs minimalistas */
        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        input:hover, select:hover {
            border-color: #9ca3af;
        }
        
        /* Botão de pesquisa */
        .search-btn {
            width: 48px;
            height: 41px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
            padding: 0 !important;
            margin-top: 0 !important;
        }
        
        .search-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .search-btn:active {
            transform: translateY(0);
        }
        
        .search-icon {
            width: 24px !important;
            height: 24px !important;
            stroke: white !important;
            fill: none !important;
            display: block !important;
        }
        
        /* Botão de submit */
        button[type="submit"] {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        button[type="submit"]:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        button[type="submit"]:active {
            transform: translateY(0);
        }
        
        /* Popup minimalista */
        .popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2563eb;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid #1d4ed8;
            margin: auto;
        }
        .popup.show {
            opacity: 1;
            transform: translateY(0);
        }
        .popup.error {
            background: #dc2626;
            border-color: #b91c1c;
        }
        /* Popup centralizado para seleção de empresa/setor */
        .popup-central {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            color: #111827;
            border-radius: 12px;
            box-shadow: 0 10px 32px rgba(0,0,0,0.18);
            min-width: 340px;
            max-width: 95vw;
            max-height: 80vh;
            overflow: auto;
            z-index: 11000;
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 28px 24px 18px 24px;
        }
        .popup-central .popup-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 12px;
        }
        .popup-central .popup-close {
            background: #e5e7eb;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 20px;
            color: #374151;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        .popup-central .popup-close:hover {
            background: #d1d5db;
        }
        .popup-central .popup-content {
            width: 100%;
        }
        .popup-central .popup-search {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 15px;
            margin-bottom: 12px;
        }
        .popup-central .popup-table-container {
            max-height: 45vh;
            overflow-y: auto;
        }
        .popup-central .popup-table {
            width: 100%;
            border-collapse: collapse;
        }
        .popup-central .popup-table th, .popup-central .popup-table td {
            padding: 10px 12px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        .popup-central .popup-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
        }
        .popup-central .popup-table tr:hover {
            background: #f1f5f9;
        }
        .popup-central .popup-table button {
            padding: 6px 14px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .popup-central .popup-table button:hover {
            background: #1d4ed8;
        }
        /* Overlay escuro para popup central */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 10999;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active {
            display: flex;
        }
        
        /* Validação de campos */
        input:invalid, select:invalid, textarea:invalid {
            border-color: #d1d5db;
            background-color: #ffffff;
        }
        
        input:invalid:focus, select:invalid:focus, textarea:invalid:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Classe para campos com erro */
        .field-error {
            border-color: #dc2626 !important;
            background-color: #fef2f2 !important;
        }
        
        .field-error:focus {
            border-color: #dc2626 !important;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
        }
        
        /* Placeholder para campos com erro */
        .field-error::placeholder {
            color: #dc2626;
            font-weight: 500;
        }
        
        /* Popup container minimalista */
        .popup-container {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffffff;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            z-index: 1000;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .popup-container h2 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        
        .popup-container .search-box {
            margin-bottom: 20px;
        }
        
        .popup-container .search-box input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .popup-container .table-wrapper {
            max-height: calc(80vh - 200px);
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .popup-container table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .popup-container th, .popup-container td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .popup-container th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
        }
        
        .popup-container tr:hover {
            background: #f9fafb;
        }
        
        .popup-container button {
            padding: 8px 16px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .popup-container button:hover {
            background: #1d4ed8;
        }
        
        .popup-container .close-btn {
            background: #6b7280;
            padding: 10px 20px;
            font-size: 14px;
        }
        
        .popup-container .close-btn:hover {
            background: #4b5563;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            
            form {
                padding: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
        }

        .form-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }

        /* Overlay escuro para popup */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 999;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active {
            display: flex;
        }
        .popup {
            margin: auto;
        }

        .popup-empresa-overlay {
            position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.4); z-index: 9999; display: none;
        }
        .popup-empresa-overlay.active { display: block; }
        .popup-empresa-container {
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #fff; border-radius: 12px; box-shadow: 0 10px 32px rgba(0,0,0,0.18);
            min-width: 340px; max-width: 95vw; max-height: 80vh; overflow: auto; z-index: 10000; display: none;
            padding: 28px 24px 18px 24px;
        }
        .popup-setor-overlay {
            position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.4); z-index: 9999; display: none;
        }
        .popup-setor-overlay.active { display: block; }
        .popup-setor-container {
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #fff; border-radius: 12px; box-shadow: 0 10px 32px rgba(0,0,0,0.18);
            min-width: 340px; max-width: 95vw; max-height: 80vh; overflow: auto; z-index: 10000; display: none;
            padding: 28px 24px 18px 24px;
        }
        .popup-empresa-container, .popup-setor-container {
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>
<?php include 'conexao.php'; ?>
<div class="content-container">
    <h1>Cadastro de Pessoas</h1>
    <form action="salvar-pessoa.php" method="POST">
        <div class="form-group">
            <label for="nome">Nome</label>
            <input type="text" id="nome" name="nome">
        </div>

        <div class="form-group">
            <label for="posto">Empresa</label>
            <div class="form-row">
                <div style="flex: 1; display: flex; align-items: center; gap: 8px;">
                    <input type="text" id="posto" name="posto" readonly placeholder="Código da empresa">
                    <span id="nome_empresa_exibicao" style="color: #2563eb; font-weight: 500;"></span>
                </div>
                <button type="button" class="search-btn" onclick="abrirPopup()" title="Pesquisar Empresa">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                        <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="form-group">
            <label for="setor">Setor</label>
            <div class="form-row">
                <div style="flex: 1; display: flex; align-items: center; gap: 8px;">
                    <input type="text" id="setor" name="setor" readonly placeholder="Código do setor">
                    <span id="nome_setor_exibicao" style="color: #2563eb; font-weight: 500;"></span>
                </div>
                <button type="button" class="search-btn" onclick="abrirPopupSetor()" title="Pesquisar Setor">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                        <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="form-group">
            <label for="funcao">Função</label>
            <input type="text" id="funcao" name="funcao">
        </div>

        <div class="form-group">
            <label for="data_admissao">Data de Admissão</label>
            <input type="date" id="data_admissao" name="data_admissao">
        </div>

        <button type="submit">Cadastrar Pessoa</button>
    </form>

<!-- Pop-up de Empresas (estilo requisicoes.php) -->
<div class="popup-overlay" id="popupEmpresasOverlay">
    <div class="popup-central" id="popupEmpresas">
        <div class="popup-header">
            <h3>Selecionar Empresa</h3>
            <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
    </div>
        <div class="popup-content">
            <input type="text" class="popup-search" id="pesquisaEmpresa" placeholder="Pesquisar empresa..." oninput="filtrarEmpresas()">
            <div class="popup-table-container">
                <table class="popup-table">
            <thead>
                <tr>
                            <th>Código</th>
                    <th>Nome</th>
                            <th>Contrato</th>
                    <th>Ação</th>
                </tr>
            </thead>
                    <tbody id="listaEmpresas">
                <?php
                        $result = $conn->query("SELECT codigo_empresa, nome_empresa, contrato FROM empresas WHERE status = 'ativo'");
                while ($row = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['codigo_empresa'] ?></td>
                        <td><?= $row['nome_empresa'] ?></td>
                                <td><?= htmlspecialchars($row['contrato'], ENT_QUOTES) ?></td>
                                <td><button onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= $row['nome_empresa'] ?>', '<?= htmlspecialchars($row['contrato'], ENT_QUOTES) ?>')">Selecionar</button></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
        </div>
    </div>
</div>

<!-- Pop-up de Setores (estilo requisicoes.php) -->
<div class="popup-overlay" id="popupSetorOverlay">
    <div class="popup-central" id="popupSetor">
        <div class="popup-header">
            <h3>Selecionar Setor</h3>
            <button class="popup-close" onclick="fecharPopupSetor()">&times;</button>
    </div>
        <div class="popup-content">
            <input type="text" class="popup-search" id="pesquisaSetor" placeholder="Pesquisar setor..." oninput="filtrarSetores()">
            <div class="popup-table-container">
                <table class="popup-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Ação</th>
                </tr>
            </thead>
                    <tbody id="listaSetores">
                        <?php
                        $result = $conn->query("SELECT id, nome FROM setor ORDER BY id");
                        while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td><?= $row['id'] ?></td>
                                <td><?= htmlspecialchars($row['nome']) ?></td>
                                <td><button onclick="selecionarSetor('<?= $row['id'] ?>', '<?= htmlspecialchars($row['nome'], ENT_QUOTES) ?>')">Selecionar</button></td>
                            </tr>
                        <?php endwhile; ?>
            </tbody>
        </table>
            </div>
        </div>
    </div>
</div>

<script>
    function abrirPopup() {
        const overlay = document.getElementById('popupEmpresasOverlay');
        const popup = document.getElementById('popupEmpresas');
        overlay.classList.add('active');
        overlay.style.display = 'flex';
        popup.style.display = 'flex';
        overlay.onclick = function(e) {
            if (e.target === overlay) fecharPopupEmpresas();
        };
    }
    function fecharPopupEmpresas() {
        const overlay = document.getElementById('popupEmpresasOverlay');
        const popup = document.getElementById('popupEmpresas');
        overlay.classList.remove('active');
        overlay.style.display = 'none';
        popup.style.display = 'none';
        overlay.onclick = null;
    }
    function selecionarEmpresa(codigo, nome, contrato) {
        document.getElementById('posto').value = codigo;
        document.getElementById('nome_empresa_exibicao').textContent = nome;
        fecharPopupEmpresas();
    }

    function filtrarEmpresas() {
        let input = document.getElementById('pesquisaEmpresa').value.toLowerCase();
        let linhas = document.getElementById('listaEmpresas').getElementsByTagName('tr');

        for (let i = 0; i < linhas.length; i++) {
            let nome = linhas[i].getElementsByTagName('td')[1];
            if (nome) {
                let txtValue = nome.textContent || nome.innerText;
                if (txtValue.toLowerCase().indexOf(input) > -1) {
                    linhas[i].style.display = '';
                } else {
                    linhas[i].style.display = 'none';
                }
            }
        }
    }

    function abrirPopupSetor() {
        const overlay = document.getElementById('popupSetorOverlay');
        const popup = document.getElementById('popupSetor');
        overlay.classList.add('active');
        overlay.style.display = 'flex';
        popup.style.display = 'flex';
        overlay.onclick = function(e) {
            if (e.target === overlay) fecharPopupSetor();
        };
        // Buscar setores vinculados à empresa selecionada
        const empresa = document.getElementById('posto').value;
        const listaSetores = document.getElementById('listaSetores');
        const pesquisaSetor = document.getElementById('pesquisaSetor');
        if (!empresa) {
            listaSetores.innerHTML = '<tr><td colspan="3" style="color:#dc2626; text-align:center;">Selecione uma empresa primeiro</td></tr>';
            pesquisaSetor.disabled = true;
            return;
        }
        pesquisaSetor.disabled = false;
        listaSetores.innerHTML = '<tr><td colspan="3" style="text-align:center; color:#2563eb;">Carregando setores...</td></tr>';
        fetch('buscar-setores-por-empresa.php?empresa=' + encodeURIComponent(empresa))
            .then(response => response.json())
            .then(data => {
                if (data.success && data.setores.length > 0) {
                    listaSetores.innerHTML = data.setores.map(setor =>
                        `<tr><td>${setor.id}</td><td>${setor.nome.replace(/</g,'&lt;')}</td><td><button onclick=\"selecionarSetor('${setor.id}', '${setor.nome.replace(/'/g, "&#39;").replace(/"/g, '&quot;')}')\">Selecionar</button></td></tr>`
                    ).join('');
                } else {
                    listaSetores.innerHTML = '<tr><td colspan="3" style="color:#dc2626; text-align:center;">Nenhum setor encontrado para esta empresa</td></tr>';
                }
            })
            .catch(() => {
                listaSetores.innerHTML = '<tr><td colspan="3" style="color:#dc2626; text-align:center;">Erro ao buscar setores</td></tr>';
            });
    }
    function fecharPopupSetor() {
        const overlay = document.getElementById('popupSetorOverlay');
        const popup = document.getElementById('popupSetor');
        overlay.classList.remove('active');
        overlay.style.display = 'none';
        popup.style.display = 'none';
        overlay.onclick = null;
    }
    function selecionarSetor(codigo, nome) {
        document.getElementById('setor').value = codigo;
        document.getElementById('nome_setor_exibicao').textContent = nome;
        fecharPopupSetor();
    }
    // Fechar popup ao clicar fora do overlay
    if (document.getElementById('popupSetorOverlay')) {
        document.getElementById('popupSetorOverlay').addEventListener('click', fecharPopupSetor);
    }

    // Validação no envio do formulário
    const form = document.querySelector('form[action="salvar-pessoa.php"]');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('field-error');
                // Adicionar placeholder de erro baseado no nome do campo
                const fieldName = input.getAttribute('name');
                let errorText = '';
                switch(fieldName) {
                    case 'nome':
                        errorText = 'Preencha o campo Nome';
                        break;
                    case 'posto':
                        errorText = 'Selecione uma empresa';
                        break;
                    case 'setor':
                        errorText = 'Preencha o campo Setor';
                        break;
                    case 'funcao':
                        errorText = 'Preencha o campo Função';
                        break;
                    case 'data_admissao':
                        errorText = 'Preencha o campo Data de Admissão';
                        break;
                    default:
                        errorText = 'Campo obrigatório';
                }
                input.setAttribute('placeholder', errorText);
            } else {
                input.classList.remove('field-error');
                // Restaurar placeholder original se existir
                const originalPlaceholder = input.getAttribute('data-original-placeholder');
                if (originalPlaceholder) {
                    input.setAttribute('placeholder', originalPlaceholder);
                } else {
                    input.removeAttribute('placeholder');
                }
            }
        });

        if (!isValid) {
            e.preventDefault();
            return;
        }
        // Envio AJAX
        e.preventDefault();
        const formData = new FormData(form);
        fetch('salvar-pessoa.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarPopupSucesso();
                form.reset();
            } else {
                mostrarPopupErro();
            }
        })
        .catch(() => {
            mostrarPopupErro();
        });
    });

    // Remover classe de erro quando o usuário começar a digitar
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.classList.contains('field-error')) {
                this.classList.remove('field-error');
            }
        });
        
        input.addEventListener('change', function() {
            if (this.classList.contains('field-error')) {
                this.classList.remove('field-error');
            }
        });
    });

    function mostrarPopupSucesso() {
        const popup = document.getElementById('popup-msg');
        popup.textContent = 'Pessoa cadastrada com sucesso!';
        popup.classList.remove('error');
        popup.classList.add('show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }
    
    function mostrarPopupErro() {
        const popup = document.getElementById('popup-msg');
        popup.textContent = 'Erro ao cadastrar pessoa.';
        popup.classList.add('error', 'show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }
</script>
<script>
  // Garantir escopo global
  window.abrirPopup = abrirPopup;
  window.abrirPopupSetor = abrirPopupSetor;
</script>

    <!-- Popup para mensagens -->
    <div id="popup-msg" class="popup">Pessoa cadastrada com sucesso!</div>
</div>
</body>
</html>

<?php
include 'conexao.php';
header('Content-Type: application/json');
$setor = isset($_GET['setor']) ? $conn->real_escape_string($_GET['setor']) : '';
if (!$setor) {
    echo json_encode(['success' => false, 'error' => 'Setor não informado']);
    exit;
}
$res = $conn->query("SELECT id, nome, funcao, status FROM pessoas WHERE setor = '$setor' ORDER BY nome");
$funcionarios = [];
while ($row = $res->fetch_assoc()) {
    $funcionarios[] = $row;
}
echo json_encode(['success' => true, 'funcionarios' => $funcionarios]); 
<?php
include 'conexao.php'; 

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $nome = $_POST['nome'];
    $posto = $_POST['posto'];
    $setor = $_POST['setor'];
    $funcao = $_POST['funcao'];
    $data_admissao = $_POST['data_admissao'];

    $sql = "INSERT INTO pessoas (nome, posto, setor, funcao, data_admissao) VALUES (?, ?, ?, ?, ?)";

    if ($stmt = $conn->prepare($sql)) {
        
        $stmt->bind_param("sssss", $nome, $posto, $setor, $funcao, $data_admissao);

        
        if ($stmt->execute()) {
           
            echo json_encode(['success' => true]);
        } else {
           
            echo json_encode(['success' => false]);
        }

        
        $stmt->close();
    } else {
      
        echo json_encode(['success' => false]);
    }
}


$conn->close();
?>

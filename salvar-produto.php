<?php
include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $codigo = $_POST['codigo'];
    $nome = $_POST['nome'];
    $categoria = $_POST['categoria'];
    $ca = $_POST['ca'];
    $validade = $_POST['validade']; // Agora recebe no formato YYYY-MM-DD
    $valor = $_POST['valor'];
    $valor_bruto = $_POST['valor_bruto'];
    $quantidade = $_POST['quantidade'];
    $validade_uso = isset($_POST['validade_uso']) ? $_POST['validade_uso'] : null;
    $unidade_medida = $_POST['unidade_medida'];

    // Verificar se o código já existe no banco
    $verifica_sql = "SELECT codigo FROM produtos WHERE codigo = '$codigo'";
    $resultado = $conn->query($verifica_sql);

    if ($resultado->num_rows > 0) {
        // Redireciona para a página de cadastro com erro de código duplicado e mantém os dados
        $params = http_build_query([
            'status' => 'error',
            'message' => 'codigo_duplicado',
            'codigo' => $codigo,
            'unidade_medida' => $unidade_medida,
            'nome' => $nome,
            'categoria' => $categoria,
            'ca' => $ca,
            'validade' => $validade,
            'valor' => $valor,
            'valor_bruto' => $valor_bruto,
            'quantidade' => $quantidade,
            'validade_uso' => $validade_uso
        ]);
        header("Location: cadastro-produto.php?" . $params);
        exit();
    }

    // Verificar se o nome já existe no banco
    $verifica_nome_sql = "SELECT nome FROM produtos WHERE nome = '" . $conn->real_escape_string($nome) . "'";
    $resultado_nome = $conn->query($verifica_nome_sql);
    if ($resultado_nome->num_rows > 0) {
        $params = http_build_query([
            'status' => 'error',
            'message' => 'nome_duplicado',
            'codigo' => $codigo,
            'unidade_medida' => $unidade_medida,
            'nome' => $nome,
            'categoria' => $categoria,
            'ca' => $ca,
            'validade' => $validade,
            'valor' => $valor,
            'valor_bruto' => $valor_bruto,
            'quantidade' => $quantidade,
            'validade_uso' => $validade_uso
        ]);
        header("Location: cadastro-produto.php?" . $params);
        exit();
    }

    // Inserir o produto no banco
    $sql = "INSERT INTO produtos (codigo, unidade_medida, nome, categoria, ca, valor, valor_bruto, quantidade, validade, validade_uso) 
        VALUES ('$codigo', '$unidade_medida', '$nome', '$categoria', '$ca', '$valor', '$valor_bruto', '$quantidade', '$validade', ".($validade_uso !== null ? "'$validade_uso'" : "NULL").")";


    if ($conn->query($sql) === TRUE) {
        // Redireciona para a página de cadastro com status de sucesso
        header("Location: cadastro-produto.php?status=success");
        exit();
    } else {
        // Redireciona para a página de cadastro com erro e mostra o erro do MySQL
        $erro = urlencode($conn->error);
        header("Location: cadastro-produto.php?status=error&mysql_error=$erro");
        exit();
    }

    $conn->close();
}
?>

<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
}

echo "<h2>🔍 Debug - Função toggleExportDropdown</h2>";

// Verificar se o arquivo index.php contém a função
$conteudo = file_get_contents('index.php');

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📊 Análise do Arquivo index.php:</h3>";

// Verificar se a função está definida
$tem_funcao = strpos($conteudo, 'function toggleExportDropdown') !== false;
$tem_window_assign = strpos($conteudo, 'window.toggleExportDropdown') !== false;
$tem_onclick = substr_count($conteudo, 'toggleExportDropdown(event,');

echo "<ul>";
echo "<li><strong>Função definida:</strong> " . ($tem_funcao ? "✅ Sim" : "❌ Não") . "</li>";
echo "<li><strong>Atribuição ao window:</strong> " . ($tem_window_assign ? "✅ Sim" : "❌ Não") . "</li>";
echo "<li><strong>Chamadas onclick:</strong> $tem_onclick</li>";
echo "</ul>";
echo "</div>";

// Listar todos os IDs de dropdown
echo "<h3>🎯 IDs de Dropdown Encontrados:</h3>";
echo "<div style='background: white; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px;'>";

$pattern = '/id="dropdown-([^"]+)"/';
preg_match_all($pattern, $conteudo, $matches);

if (!empty($matches[1])) {
    foreach ($matches[1] as $index => $id) {
        echo "<div style='margin: 5px 0; padding: 5px; background: #e8f5e8; border-radius: 4px;'>";
        echo "✅ <strong>dropdown-$id</strong>";
        echo "</div>";
    }
} else {
    echo "<p style='color: red;'>❌ Nenhum ID de dropdown encontrado!</p>";
}

echo "</div>";

// Verificar se há erros de sintaxe JavaScript
echo "<h3>🔧 Verificação de JavaScript:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";

// Procurar por possíveis erros
$js_errors = [];

if (strpos($conteudo, 'toggleExportDropdown(event,') !== false && !$tem_funcao) {
    $js_errors[] = "Função chamada mas não definida";
}

if (substr_count($conteudo, 'function toggleExportDropdown') > 1) {
    $js_errors[] = "Função definida múltiplas vezes";
}

if (empty($js_errors)) {
    echo "<p style='color: green;'>✅ Nenhum erro JavaScript detectado</p>";
} else {
    echo "<p style='color: red;'>❌ Possíveis problemas:</p>";
    echo "<ul>";
    foreach ($js_errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
}

echo "</div>";

// Script de teste inline
echo "<h3>🧪 Teste da Função:</h3>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px;'>";
echo "<p>Clique no botão abaixo para testar se a função está funcionando:</p>";

echo "<div style='position: relative; display: inline-block; margin: 10px;'>";
echo "<button onclick='testToggleFunction()' style='background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;'>";
echo "<i class='fas fa-ellipsis-v'></i> Testar Dropdown";
echo "</button>";
echo "<div id='test-dropdown' style='display: none; position: absolute; right: 0; top: 100%; background: white; border: 1px solid #ccc; border-radius: 6px; padding: 10px; margin-top: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); z-index: 1000;'>";
echo "<div style='padding: 8px 12px; cursor: pointer; color: #10b981;'><i class='fas fa-file-excel'></i> Excel</div>";
echo "<div style='padding: 8px 12px; cursor: pointer; color: #ef4444;'><i class='fas fa-file-pdf'></i> PDF</div>";
echo "</div>";
echo "</div>";

echo "<div id='test-result' style='margin-top: 15px; padding: 10px; border-radius: 4px; display: none;'></div>";
echo "</div>";

// Links úteis
echo "<h3>🔗 Links para Teste:</h3>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0 10px;'>🏠 Dashboard Principal</a>";
echo "<a href='teste-dropdowns.html' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0 10px;'>🧪 Teste Isolado</a>";
echo "</div>";

?>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<script>
// Função de teste
function testToggleFunction() {
    const result = document.getElementById('test-result');
    const dropdown = document.getElementById('test-dropdown');
    
    try {
        // Verificar se a função existe
        if (typeof toggleExportDropdown === 'function') {
            result.style.display = 'block';
            result.style.background = '#d4edda';
            result.style.color = '#155724';
            result.innerHTML = '✅ Função toggleExportDropdown está disponível!';
            
            // Simular toggle do dropdown de teste
            if (dropdown.style.display === 'none' || dropdown.style.display === '') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
            
        } else {
            result.style.display = 'block';
            result.style.background = '#f8d7da';
            result.style.color = '#721c24';
            result.innerHTML = '❌ Função toggleExportDropdown NÃO está disponível!';
        }
    } catch (error) {
        result.style.display = 'block';
        result.style.background = '#f8d7da';
        result.style.color = '#721c24';
        result.innerHTML = '❌ Erro ao testar função: ' + error.message;
    }
}

// Verificar quando a página carregar
window.addEventListener('load', function() {
    console.log('🔍 Debug carregado');
    console.log('Função disponível:', typeof toggleExportDropdown);
    
    if (typeof toggleExportDropdown === 'function') {
        console.log('✅ toggleExportDropdown está disponível');
    } else {
        console.log('❌ toggleExportDropdown NÃO está disponível');
        
        // Tentar definir a função aqui como fallback
        window.toggleExportDropdown = function(event, dropdownId) {
            event.stopPropagation();
            console.log('🔧 Usando função fallback para:', dropdownId);
            
            document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
                if (dropdown.id !== dropdownId) {
                    dropdown.classList.remove('show');
                }
            });
            
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        };
        
        console.log('🔧 Função fallback definida');
    }
});
</script>

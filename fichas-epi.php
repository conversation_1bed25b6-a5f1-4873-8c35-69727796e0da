<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

require_once 'conexao.php';

// Inicializar variáveis de filtro
$search = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'ativo';
$setorFilter = isset($_GET['setor_filter']) ? $_GET['setor_filter'] : '';
$postoFilter = isset($_GET['posto_filter']) ? $_GET['posto_filter'] : '';

// Construir a consulta SQL com filtros
$whereClause = [];
if ($search) {
    $whereClause[] = "(nome LIKE '%" . $conn->real_escape_string($search) . "%' OR posto LIKE '%" . $conn->real_escape_string($search) . "%' OR setor LIKE '%" . $conn->real_escape_string($search) . "%')";
}
if ($statusFilter !== 'todos') {
    $whereClause[] = "status = '" . $conn->real_escape_string($statusFilter) . "'";
}
if ($setorFilter !== '' && $setorFilter !== 'todos') {
    $whereClause[] = "setor = '" . $conn->real_escape_string($setorFilter) . "'";
}
if ($postoFilter !== '' && $postoFilter !== 'todos') {
    $whereClause[] = "posto = '" . $conn->real_escape_string($postoFilter) . "'";
}
$whereSQL = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";

$sql = "SELECT id, nome, posto, setor, funcao, data_admissao FROM pessoas $whereSQL ORDER BY nome ASC";
$result = $conn->query($sql);
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}

// Obter lista de postos para o filtro
$sql_postos = "SELECT DISTINCT posto FROM pessoas WHERE posto IS NOT NULL AND posto != '' ORDER BY posto ASC";
$result_postos = $conn->query($sql_postos);
$postos = [];
while ($row = $result_postos->fetch_assoc()) {
    $postos[] = $row['posto'];
}

// Obter lista de setores para o filtro
$sql_setores = "SELECT DISTINCT setor FROM pessoas WHERE setor IS NOT NULL AND setor != '' ORDER BY setor ASC";
$result_setores = $conn->query($sql_setores);
$setores = [];
while ($row = $result_setores->fetch_assoc()) {
    $setores[] = $row['setor'];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fichas de EPI</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px;
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* --- Estilo minimalista e moderno inspirado em tabela-produtos.php --- */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        .modern-table thead {
            background: #f8fafc;
        }
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        .modern-table td:last-child {
            text-align: center;
        }
        .acoes-btns {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }
        .btn-visualizar {
            background: #2563eb;
            color: #fff;
            border: none;
            padding: 7px 16px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 1em;
            font-weight: 500;
            transition: background 0.18s, color 0.18s;
            box-shadow: none;
            text-decoration: none;
        }
        .btn-visualizar:hover {
            background: #1d4ed8;
            color: #fff;
        }
        .btn-visualizar i {
            display: none;
            }
        .btn-sino {
            background: none;
            color: #2563eb;
            border: none;
            padding: 7px 10px;
            border-radius: 6px;
            
            transition: background 0.18s, color 0.18s;
            box-shadow: none;
            gap: 5px;
            margin-left: 0;
            margin-right: 0;
        }
        .btn-sino:hover {
            background: #f3f4f6;
            color: #1d4ed8;
        }
        .btn-sino i {
            font-size: 1.1em;
        }
        
        .sem-resultados {
            text-align: center;
            padding: 30px;
            background-color: #f9f9f9;
            border-radius: 5px;
            color: #666;
            margin-top: 20px;
        }
        
        .popup-vencimento-x {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none !important;
            border: none;
            font-size: 28px;
            color: #333;
            cursor: pointer;
            z-index: 1001;
            border-radius: 0;
            transition: none;
        }
        .popup-vencimento-x:hover, .popup-vencimento-x:focus {
            background: none !important;
            color: #333;
            outline: none;
        }
        .search-bar-container {
            width: 100%;
            margin-bottom: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
        }
        .search-bar-input-wrapper {
            position: relative;
            flex: 1;
            display: flex;
            align-items: center;
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 0 0 0 16px;
            height: 48px;
            max-width: 700px;
            width: 100%;
        }
        .search-bar-input {
            border: none;
            outline: none;
            background: transparent;
            font-size: 16px;
            color: #111827;
            width: 100%;
            height: 100%;
            padding: 12px 40px 12px 16px;
            text-align: left;
        }
        .search-icon {
            position: absolute;
            right: 16px;
            color: #111;
            font-size: 18px;
            pointer-events: none;
        }
        .filter-btn {
            background: none;
            color: #111;
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: background 0.2s;
            margin-left: 8px;
        }
        .filter-btn:hover {
            background: #f3f4f6;
            color: #111;
        }
        /* Popup de filtros */
        .filter-popup-overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.4);
            z-index: 1001;
        }
        .filter-popup {
            display: none;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .filter-popup-form {
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .filter-popup-group label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
        }
        .filter-popup-input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 15px;
            background: #fff;
            color: #111827;
            transition: border 0.2s;
        }
        .filter-popup-input:focus {
            border-color: #2563eb;
            outline: none;
        }
        .filter-popup-actions {
            display: flex;
            gap: 12px;
            margin-top: 10px;
        }
        .btn-filtrar, .btn-limpar {
            padding: 10px 18px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn-filtrar {
            background: #2563eb;
            color: #fff;
        }
        .btn-filtrar:hover {
            background: #1d4ed8;
        }
        .btn-limpar {
            background: #f44336;
            color: #fff;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .btn-limpar:hover {
            background: #b91c1c;
        }
        @media (max-width: 600px) {
            .filter-popup {
                min-width: 90vw;
                padding: 18px 8px 12px;
            }
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <div class="container">
            <h1>Fichas de EPI</h1>
            <!-- Barra de busca e filtro -->
            <form method="GET" id="searchForm" style="display:none;">
                <input type="hidden" name="search" id="search">
            </form>
            <div class="search-bar-container" style="margin-bottom: 24px; display: flex; justify-content: center; width: 100%; align-items: center; gap: 16px;">
                <div class="search-bar-wrapper" style="max-width: 700px; width: 100%; position: relative; display: flex; align-items: center;">
                    <input type="text" class="search-bar-input" id="searchVisible" placeholder="Buscar por nome, categoria ou CA..." value="<?= htmlspecialchars($search) ?>" style="width: 100%; padding: 12px 48px 12px 16px; border: 1.5px solid #d1d5db; border-radius: 8px; font-size: 1.08em; background: #fff; color: #222; margin-bottom: 1px;">
                    <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar" style="position: absolute; right: 0; top: 0px; height: 100%; width: 44px; background: none; border: none; display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 0; margin: 0;">
                        <i class="fas fa-search" style="color: #222; font-size: 1.2em;"></i>
                    </button>
                    </div>
                <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;"><i class="fas fa-filter"></i></button>
            </div>
            <!-- Popup de filtros moderno -->
            <div id="filterPopupOverlay" class="filter-popup-overlay" style="display:none; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(17, 17, 17, 0.4); z-index: 1001;"></div>
            <div id="filterPopup" class="filter-popup" style="display:none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #fff; border-radius: 16px; box-shadow: 0 8px 32px rgba(0,0,0,0.18); padding: 32px 28px 24px; min-width: 320px; z-index: 1002; min-height: 220px; max-width: 95vw;">
                <div class="filter-popup-header" style="display: flex; justify-content: space-between; align-items: center; font-size: 18px; font-weight: 600; color: #2563eb; margin-bottom: 18px;">
                    <span>Filtrar por:</span>
                    <button class="close-filter-popup" id="closeFilterPopup" title="Fechar" style="background: none; border: none; font-size: 28px; color: #6b7280; cursor: pointer; border-radius: 50%; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; transition: background 0.2s, color 0.2s;">&times;</button>
                </div>
                <form method="get" class="filter-popup-form" style="display: flex; flex-direction: column; gap: 18px;">
                    <div class="filter-popup-group">
                        <label for="status_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Status:</label>
                        <select id="status_filter" name="status_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="ativo" <?= $statusFilter == 'ativo' ? 'selected' : '' ?>>Ativo</option>
                            <option value="inativo" <?= $statusFilter == 'inativo' ? 'selected' : '' ?>>Inativo</option>
                            <option value="todos" <?= $statusFilter == 'todos' ? 'selected' : '' ?>>Todos</option>
                        </select>
                    </div>
                    <div class="filter-popup-group">
                        <label for="setor_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Setor:</label>
                        <select id="setor_filter" name="setor_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="">Todos os setores</option>
                            <?php foreach ($setores as $setor): ?>
                                <option value="<?= htmlspecialchars($setor) ?>" <?= $setorFilter == $setor ? 'selected' : '' ?>><?= htmlspecialchars($setor) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-popup-group">
                        <label for="posto_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Posto:</label>
                        <select id="posto_filter" name="posto_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="">Todos os postos</option>
                            <?php foreach ($postos as $posto): ?>
                                <option value="<?= htmlspecialchars($posto) ?>" <?= $postoFilter == $posto ? 'selected' : '' ?>><?= htmlspecialchars($posto) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-popup-actions" style="display: flex; gap: 12px; margin-top: 10px;">
                        <a href="fichas-epi.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                        <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
                    </div>
                </form>
            </div>
            
            <div class="modern-table-container">
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Posto</th>
                            <th>Setor</th>
                            <th>Função</th>
                            <th>Data de Admissão</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($dados) > 0): ?>
                        <?php foreach ($dados as $pessoa): ?>
                        <tr>
                            <td><?= htmlspecialchars($pessoa['id']) ?></td>
                            <td><?= htmlspecialchars($pessoa['nome']) ?></td>
                            <td><?= htmlspecialchars($pessoa['posto']) ?></td>
                            <td><?= htmlspecialchars($pessoa['setor']) ?></td>
                            <td><?= htmlspecialchars($pessoa['funcao']) ?></td>
                            <td><?= htmlspecialchars(date('d/m/Y', strtotime($pessoa['data_admissao']))) ?></td>
                            <td>
                                <div class="acoes-btns">
                                    <a href="ficha-epi-detalhes.php?id=<?php echo $pessoa['id']; ?>" class="btn-visualizar" title="Visualizar Ficha">
                                        Visualizar Ficha
                                </a>
                                    <button class="btn-sino" title="Alertas de Vencimento" onclick="abrirAlertaVencimento(<?php echo $pessoa['id']; ?>, '<?php echo htmlspecialchars(addslashes($pessoa['nome'])); ?>')">
                                    <i class="fas fa-bell"></i>
                                </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <tr><td colspan="7" style="text-align:center; color:#888;">Nenhum registro encontrado.</td></tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="popupVencimento" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.4);z-index:999;align-items:center;justify-content:center;" onclick="if(event.target===this)fecharAlertaVencimento()">
        <div style="background:#fff;padding:20px 12px;max-width:400px;width:90vw;min-height:160px;border-radius:8px;box-shadow:0 2px 10px #0002;position:relative;display:flex;flex-direction:column;justify-content:center;align-items:center;">
            <div style="position:absolute;top:10px;right:10px;width:auto;height:auto;z-index:10;">
                <button onclick="fecharAlertaVencimento()" class="popup-vencimento-x" style="pointer-events:auto;padding:0;margin:0;width:28px;height:28px;display:flex;align-items:center;justify-content:center;font-size:28px;background:none;border:none;color:#333;">&times;</button>
            </div>
            <h3 id="popupVencimentoTitulo" style="margin-bottom:15px; margin-top: 10px; text-align: center; width: auto;"> </h3>
            <div id="popupVencimentoConteudo" style="width:auto;text-align:center;">Carregando...</div>
        </div>
    </div>
    <script>
    function abrirAlertaVencimento(pessoaId, pessoaNome) {
        var popup = document.getElementById('popupVencimento');
        popup.style.display = 'flex';
        popup.style.alignItems = 'center';
        popup.style.justifyContent = 'center';
        document.getElementById('popupVencimentoTitulo').innerText = 'Alertas de Vencimento - ' + pessoaNome;
        document.getElementById('popupVencimentoConteudo').innerHTML = 'Carregando...';
        fetch('vencimento-epis-pessoa.php?id=' + pessoaId)
            .then(resp => resp.json())
            .then(data => {
                if (data.length === 0) {
                    document.getElementById('popupVencimentoConteudo').innerHTML = '<div style="color:#4CAF50;font-weight:bold;">Nenhum EPI próximo do vencimento.</div>';
                } else {
                    let html = '<ul style="padding-left:18px;">';
                    data.forEach(function(epi) {
                        let cor = epi.dias_restantes < 0 ? '#f44336' : (epi.dias_restantes <= 30 ? '#ff9800' : '#4CAF50');
                        html += `<li style="margin-bottom:8px;"><span style="font-weight:bold;">${epi.nome}</span> - <span style="color:${cor};font-weight:bold;">${epi.dias_restantes < 0 ? 'VENCIDO há ' + Math.abs(epi.dias_restantes) + ' dias' : 'Faltam ' + epi.dias_restantes + ' dias'}</span></li>`;
                    });
                    html += '</ul>';
                    document.getElementById('popupVencimentoConteudo').innerHTML = html;
                }
            });
    }
    function fecharAlertaVencimento() {
        document.getElementById('popupVencimento').style.display = 'none';
    }
    // Sincronizar barra de pesquisa visível com o form invisível
    const searchVisible = document.getElementById('searchVisible');
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('search');
    const searchVisibleBtn = document.getElementById('searchVisibleBtn');
    searchVisible.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
                e.preventDefault();
            searchInput.value = searchVisible.value;
            searchForm.submit();
        }
    });
    searchVisibleBtn.addEventListener('click', function() {
        searchInput.value = searchVisible.value;
        searchForm.submit();
    });
    // Popup de filtros moderno
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const filterPopupOverlay = document.getElementById('filterPopupOverlay');
    const closeFilterPopup = document.getElementById('closeFilterPopup');
    filterBtn.addEventListener('click', function(e) {
        filterPopup.style.display = 'block';
        filterPopupOverlay.style.display = 'block';
        e.stopPropagation();
    });
    closeFilterPopup.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
    filterPopupOverlay.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
    </script>
</body>
</html>

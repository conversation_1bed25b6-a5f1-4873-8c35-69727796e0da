<?php
include 'conexao.php';

if (isset($_GET['codigo_empresa'])) {
    $codigo_empresa = $_GET['codigo_empresa'];

    $stmt = $conn->prepare("SELECT nome_empresa, cnpj FROM empresas WHERE codigo_empresa = ?");
    $stmt->bind_param("s", $codigo_empresa);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        echo json_encode(["success" => true, "nome_empresa" => $row['nome_empresa'], "cnpj" => $row['cnpj']]);
    } else {
        echo json_encode(["success" => false]);
    }

    $stmt->close();
    $conn->close();
}

?>
    
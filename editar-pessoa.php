<?php
include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id = $_POST['id'];
    $nome = $_POST['nome'];
    $posto = $_POST['posto'];
    $setor = $_POST['setor'];
    $funcao = $_POST['funcao'];
    $data_admissao = $_POST['data_admissao'];

    $sql = "UPDATE pessoas SET nome=?, posto=?, setor=?, funcao=?, data_admissao=? WHERE id=?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssi", $nome, $posto, $setor, $funcao, $data_admissao, $id);

    if ($stmt->execute()) {
        // Redireciona para a página de listagem com status de sucesso
        header('Location: tabela-pessoas.php?status=edit-success');
    } else {
        // Redireciona para a página de listagem com status de erro
        header('Location: tabela-pessoas.php?status=edit-error');
    }
    exit;
}
?>

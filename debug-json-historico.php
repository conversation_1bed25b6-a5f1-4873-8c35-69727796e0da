<?php
echo "<h2>Debug - JSON do Histórico</h2>";

// Testar ambos os endpoints
$endpoints = [
    'obter-historico-devolucoes.php' => 'Endpoint Original',
    'obter-historico-devolucoes-limpo.php' => 'Endpoint Limpo',
    'obter-historico-devolucoes-final.php' => 'Endpoint Final'
];

foreach ($endpoints as $endpoint => $nome) {
    echo "<h3>$nome ($endpoint)</h3>";
    
    // Fazer requisição
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "http://localhost/sistema/$endpoint");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    curl_close($ch);
    
    $headers = substr($response, 0, $header_size);
    $body = substr($response, $header_size);
    
    echo "<h4>Status HTTP: $http_code</h4>";
    
    echo "<h4>Headers:</h4>";
    echo "<pre>" . htmlspecialchars($headers) . "</pre>";
    
    echo "<h4>Corpo da Resposta:</h4>";
    echo "<p>Tamanho: " . strlen($body) . " bytes</p>";
    
    // Verificar se começa com HTML
    if (strpos($body, '<') === 0) {
        echo "<p style='color: red;'>❌ Resposta começa com HTML!</p>";
        echo "<pre>" . htmlspecialchars(substr($body, 0, 200)) . "...</pre>";
    } else {
        echo "<p style='color: green;'>✅ Resposta não começa com HTML</p>";
    }
    
    // Tentar decodificar JSON
    $data = json_decode($body, true);
    if ($data === null) {
        echo "<p style='color: red;'>❌ JSON inválido!</p>";
        echo "<p>Erro JSON: " . json_last_error_msg() . "</p>";
        echo "<h5>Primeiros 500 caracteres:</h5>";
        echo "<pre>" . htmlspecialchars(substr($body, 0, 500)) . "</pre>";
    } else {
        echo "<p style='color: green;'>✅ JSON válido!</p>";
        echo "<p>Tipo: " . gettype($data) . "</p>";
        
        if (is_array($data)) {
            echo "<p>Total de registros: " . count($data) . "</p>";
            
            if (count($data) > 0) {
                echo "<h5>Primeiro registro:</h5>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Campo</th><th>Valor</th></tr>";
                foreach ($data[0] as $campo => $valor) {
                    echo "<tr><td>$campo</td><td>" . htmlspecialchars($valor) . "</td></tr>";
                }
                echo "</table>";
            }
        }
    }
    
    echo "<hr>";
}

// Testar JavaScript
echo "<h3>Teste JavaScript:</h3>";
echo "<button onclick='testarEndpoint()'>Testar com JavaScript</button>";
echo "<div id='resultado'></div>";

echo "<script>
function testarEndpoint() {
    const resultado = document.getElementById('resultado');
    resultado.innerHTML = '<p>Testando...</p>';
    
    fetch('obter-historico-devolucoes-final.php')
        .then(response => {
            console.log('Status:', response.status);
            console.log('Headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Resposta como texto:', text);
            
            try {
                const data = JSON.parse(text);
                resultado.innerHTML = '<p style=\"color: green;\">✅ JSON válido! Total: ' + data.length + ' registros</p>';
            } catch (e) {
                resultado.innerHTML = '<p style=\"color: red;\">❌ Erro JSON: ' + e.message + '</p><pre>' + text.substring(0, 500) + '</pre>';
            }
        })
        .catch(error => {
            resultado.innerHTML = '<p style=\"color: red;\">❌ Erro na requisição: ' + error.message + '</p>';
        });
}
</script>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Voltar para Devolução</a>";
echo "</p>";
?>

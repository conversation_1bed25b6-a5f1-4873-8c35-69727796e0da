<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Obter o próximo código de entrada
$result = $conn->query("SELECT MAX(id) as ultimo FROM entradas_estoque");
$row = $result->fetch_assoc();
$codigo_entrada = isset($row['ultimo']) ? $row['ultimo'] + 1 : 1;

// Verifica se um código foi enviado via GET para buscar o nome do produto
if (isset($_GET['codigo'])) {
    $codigo = $_GET['codigo'];
    $stmt = $conn->prepare("SELECT nome FROM produtos WHERE codigo = ?");
    $stmt->bind_param("s", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    $produto = $result->fetch_assoc();
    echo json_encode($produto);
    exit();
}
if (isset($_GET['codigo_empresa'])) {
    $codigo_empresa = $_GET['codigo_empresa'];

    $stmt = $conn->prepare("SELECT nome_empresa, cnpj FROM empresas WHERE codigo_empresa = ?");
    $stmt->bind_param("s", $codigo_empresa);
    $stmt->execute();
    $result = $stmt->get_result();
    $empresa = $result->fetch_assoc();

    if ($empresa) {
        echo json_encode([
            "success" => true,
            "nome_empresa" => $empresa['nome_empresa'],
            "cnpj" => $empresa['cnpj']
        ]);
    } else {
        echo json_encode(["success" => false]);
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entrada de Estoque</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .search-btn {
            width: 48px;
            height: 41px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
            padding: 0 !important;
        }
        
        .search-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .search-btn:active {
            transform: translateY(0);
        }
        
        .search-icon {
            width: 24px !important;
            height: 24px !important;
            stroke: white !important;
            fill: none !important;
            display: block !important;
        }
        
        .divider {
            height: 1px;
            background: #d1d5db;
            margin: 32px 0;
            width: 100%;
        }
        
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        input:hover, select:hover, textarea:hover {
            border-color: #9ca3af;
        }
        
        button, .btn, .btn-confirm, .btn-cancel {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        button:hover, .btn:hover, .btn-confirm:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        button:active, .btn:active, .btn-confirm:active {
            transform: translateY(0);
        }
        
        .btn-cancel {
            background: #6b7280;
        }
        .btn-cancel:hover {
            background: #374151;
        }
        
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        /* Seção Produtos Inseridos */
        .produtos-inseridos {
            max-width: 680px;
            margin: 40px auto 0 auto;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .produtos-inseridos h2 {
            background: #f8fafc;
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            padding: 20px 24px;
            margin: 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .produtos-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .produtos-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .produtos-table td {
            padding: 16px 24px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
            font-size: 14px;
        }
        
        .produtos-table tr:hover {
            background: #f9fafb;
        }
        
        .produtos-table input[type="number"] {
            width: 80px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .btn-remove {
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-remove:hover {
            background: #b91c1c;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 24px;
            color: #6b7280;
            font-size: 14px;
        }
        
        /* Popup melhorado */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
        }
        
        .popup-overlay.active {
            display: flex;
        }
        
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .popup h3 {
            margin: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        
        .popup-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
            width: auto;
            margin: 0;
        }
        
        .popup-close:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .popup-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .popup input[type="text"] {
            width: 100%;
            max-width: 400px;
            margin-bottom: 20px;
        }
        
        .popup-table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        .popup table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .popup table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .popup table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
            font-size: 14px;
        }
        
        .popup table tr:hover {
            background: #f9fafb;
        }
        
        .btn-select {
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-select:hover {
            background: #1d4ed8;
        }
        
        .btn-close {
            background: #6b7280;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            margin-top: 16px;
            align-self: flex-end;
            transition: background 0.2s;
            width: auto;
        }
        
        .btn-close:hover {
            background: #374151;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            form {
                padding: 24px;
            }
            h1 {
                font-size: 24px;
            }
            .form-row {
                flex-direction: column;
                gap: 16px;
            }
            .search-btn {
                align-self: flex-end;
            }
        }
        
        /* Estilos para notificações */
        .notificacao {
            position: fixed;
            top: 20px;
            right: -300px;
            width: 280px;
            padding: 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transition: right 0.5s ease;
        }

        .notificacao.success {
            background-color: #4CAF50;
        }

        .notificacao.error {
            background-color: #f44336;
        }

        #registrarEntrada {
            width: 680px;
            display: block;
            margin: 32px auto 0 auto;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
<h1>Entrada de Estoque</h1>

<form id="entradaForm">
    <div class="form-row">
        <div class="form-group">
            <label for="codigo_entrada">Código da Entrada:</label>
            <input type="text" id="codigo_entrada" name="codigo_entrada" value="<?php echo $codigo_entrada; ?>" readonly>
        </div>
        <div class="form-group">
            <label for="responsavel">Responsável pela Entrada:</label>
            <input type="text" id="responsavel" name="responsavel" value="<?php echo htmlspecialchars($_SESSION['nome_usuario']); ?>" readonly required>
        </div>
        <div class="form-group">
            <label for="data_entrada">Data da Entrada:</label>
            <input type="date" id="data_entrada" name="data_entrada" value="<?php echo date('Y-m-d'); ?>" required>
        </div>
    </div>
    
    <div class="divider"></div>
    
    <div class="form-group">
        <label for="codigo_empresa">Código da Empresa:</label>
        <div class="form-row">
            <div class="form-group">
                <input type="text" id="codigo_empresa" name="codigo_empresa" oninput="buscarEmpresa()" placeholder="Digite o código da empresa">
            </div>
            <button type="button" class="search-btn" onclick="abrirPopupEmpresas()" title="Pesquisar Empresa">
                <svg class="search-icon" viewBox="0 0 24 24">
                  <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                  <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                </svg>
            </button>
        </div>
    </div>
    <div class="form-group">
        <label for="nome_empresa">Nome da Empresa:</label>
        <input type="text" id="nome_empresa" name="nome" readonly>
    </div>
    <div class="form-group">
        <label for="cnpj">CNPJ:</label>
        <input type="text" id="cnpj" name="cnpj" readonly>
    </div>
    
    <div class="divider"></div>
    
    <h2>Produtos</h2>
    <div class="form-group">
        <label for="codigo">Código do Produto:</label>
        <div class="form-row">
            <div class="form-group">
                <input type="text" id="codigo" name="codigo" oninput="buscarNomeProduto()" placeholder="Digite o código do produto">
            </div>
            <button type="button" class="search-btn" onclick="abrirPopupProdutos()" title="Pesquisar Produto">
                <svg class="search-icon" viewBox="0 0 24 24">
                  <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                  <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                </svg>
            </button>
        </div>
    </div>
    <div class="form-group">
        <label for="nome">Nome do Produto:</label>
        <input type="text" id="nome" name="nome" readonly>
    </div>
    <div class="form-group">
        <label for="quantidade">Quantidade:</label>
        <input type="number" id="quantidade" name="quantidade" min="1">
    </div>
    <div class="form-group">
        <button type="button" onclick="inserirProduto()">Adicionar Produto</button>
    </div>
</form>

<div class="produtos-inseridos">
    <h2>Produtos Inseridos</h2>
    <table class="produtos-table" id="tabelaInseridos">
        <thead>
            <tr>
                <th>Código</th>
                <th>Nome</th>
                <th>Quantidade</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            <tr class="empty-state-row">
                <td colspan="4" class="empty-state">Nenhum produto inserido ainda</td>
            </tr>
        </tbody>
    </table>
</div>
        
<button id="registrarEntrada" onclick="registrarEntrada()">Registrar Entrada</button>

<!-- POPUP DE PRODUTOS -->
<div class="popup-overlay" id="produtoPopup">
    <div class="popup">
        <div class="popup-header">
            <h3>Selecionar Produto</h3>
            <button class="popup-close" onclick="fecharPopupProdutos()">&times;</button>
        </div>
        <div class="popup-content">
            <input type="text" id="pesquisaProduto" placeholder="Pesquisar produtos..." oninput="filtrarProdutos()">
            <div class="popup-table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Ação</th>
                        </tr>
                    </thead>    
                    <tbody id="listaPopupProdutos">
                        <?php
                        $result = $conn->query("SELECT codigo, nome FROM produtos WHERE status = 'ativo'");
                        while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td><?= $row['codigo'] ?></td>
                                <td><?= $row['nome'] ?></td>
                                <td><button class="btn-select" onclick="selecionarProduto('<?= $row['codigo'] ?>', '<?= $row['nome'] ?>')">Selecionar</button></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- POPUP DE EMPRESAS -->
<div class="popup-overlay" id="popupEmpresas">
    <div class="popup">
        <div class="popup-header">
            <h3>Selecionar Empresa</h3>
            <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
        </div>
        <div class="popup-content">
            <input type="text" id="filtroEmpresa" placeholder="Pesquisar empresas..." onkeyup="filtrarEmpresas()">
            <div class="popup-table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>CNPJ</th>
                            <th>Ação</th>
                        </tr>
                    </thead>
                    <tbody id="listaEmpresas">
                        <?php
                        $result = $conn->query("SELECT codigo_empresa, nome_empresa, cnpj FROM empresas WHERE status = 'ativo'");
                        while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td><?= $row['codigo_empresa'] ?></td>
                                <td><?= $row['nome_empresa'] ?></td>
                                <td><?= $row['cnpj'] ?></td>
                                <td><button class="btn-select" onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= $row['nome_empresa'] ?>', '<?= $row['cnpj'] ?>')">Selecionar</button></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Popup de confirmação de entrada -->
<div class="popup-overlay" id="confirmacaoPopup">
    <div class="popup">
        <div class="popup-header">
            <h3>Confirmar Entrada de Estoque</h3>
            <button class="popup-close" onclick="fecharConfirmacaoPopup()">&times;</button>
        </div>
        <div class="popup-content">
            <p>Tem certeza que deseja registrar a entrada dos seguintes itens?</p>
            <div class="popup-table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Quantidade</th>
                        </tr>
                    </thead>
                    <tbody id="tabelaConfirmacao">
                        <!-- Será preenchido dinamicamente -->
                    </tbody>
                </table>
            </div>
            <div class="form-buttons">
                <button onclick="fecharConfirmacaoPopup()" class="btn btn-cancel">Cancelar</button>
                <button onclick="confirmarEntrada()" class="btn btn-confirm">Confirmar</button>
            </div>
        </div>
    </div>
</div>
</div>

<script>
    let produtosInseridos = [];

    function buscarNomeProduto() {
        let codigo = document.getElementById("codigo").value;
        if (codigo.length > 0) {
            fetch("entrada-estoque.php?codigo=" + codigo)
                .then(response => response.json())
                .then(data => {
                    document.getElementById("nome").value = data.nome || "";
                })
                .catch(() => document.getElementById("nome").value = "");
        } else {
            document.getElementById("nome").value = "";
        }
    }

    function inserirProduto() {
        let codigo = document.getElementById("codigo").value.trim();
        let nome = document.getElementById("nome").value.trim();
        let quantidade = document.getElementById("quantidade").value.trim();

        if (codigo !== "" && nome !== "" && quantidade > 0) {
            produtosInseridos.push({ codigo, nome, quantidade });

            atualizarTabela();
            limparCampos();
        } else {
            alert("Preencha todos os campos corretamente antes de inserir!");
        }
    }

    function atualizarTabela() {
        let tabela = document.getElementById("tabelaInseridos").getElementsByTagName("tbody")[0];
        tabela.innerHTML = "";
        
        if (produtosInseridos.length === 0) {
            tabela.innerHTML = '<tr class="empty-state-row"><td colspan="4" class="empty-state">Nenhum produto inserido ainda</td></tr>';
            return;
        }
        
        produtosInseridos.forEach((produto, index) => {
            let novaLinha = tabela.insertRow();
            novaLinha.innerHTML = `
                <td>${produto.codigo}</td>
                <td>${produto.nome}</td>
                <td><input type="number" value="${produto.quantidade}" min="1" onchange="editarProduto(${index}, this.value)"></td>
                <td>
                    <button class="btn-remove" onclick="removerProduto(${index})">Excluir</button>
                </td>
            `;
        });
    }

    function editarProduto(index, novaQuantidade) {
        if (novaQuantidade <= 0) {
            alert("Quantidade inválida!");
            return;
        }
        produtosInseridos[index].quantidade = novaQuantidade;
    }

    function removerProduto(index) {
        produtosInseridos.splice(index, 1);
        atualizarTabela();
    }

    function limparCampos() {
        document.getElementById("codigo").value = "";
        document.getElementById("nome").value = "";
        document.getElementById("quantidade").value = "";
    }

    function registrarEntrada() {
        if (produtosInseridos.length === 0) {
            alert("Nenhum produto foi inserido!");
            return;
        }
        // Verificar se a empresa foi selecionada
        const codigoEmpresa = document.getElementById("codigo_empresa").value.trim();
        const nomeEmpresa = document.getElementById("nome_empresa").value.trim();
        if (!codigoEmpresa || !nomeEmpresa) {
            alert("Por favor, selecione uma empresa antes de registrar a entrada!");
            return;
        }
        // Preencher a tabela de confirmação
        let tabelaConfirmacao = document.getElementById("tabelaConfirmacao");
        tabelaConfirmacao.innerHTML = "";
        produtosInseridos.forEach(produto => {
            let linha = tabelaConfirmacao.insertRow();
            linha.insertCell(0).textContent = produto.codigo;
            linha.insertCell(1).textContent = produto.nome;
            linha.insertCell(2).textContent = produto.quantidade;
        });
        // Mostrar o popup centralizado
        document.getElementById("confirmacaoPopup").classList.add("active");
    }

    function fecharConfirmacaoPopup() {
        document.getElementById("confirmacaoPopup").classList.remove("active");
    }

    function confirmarEntrada() {
        const responsavel = document.getElementById("responsavel").value;
        const data_entrada = document.getElementById("data_entrada").value;
        const codigo_empresa = document.getElementById("codigo_empresa").value;
        if (!responsavel || !data_entrada) {
            mostrarNotificacao("Por favor, informe o responsável e a data da entrada!", "error");
            return;
        }

        const formData = new FormData();
        formData.append('responsavel', responsavel);
        formData.append('data_entrada', data_entrada);
        formData.append('codigo_empresa', codigo_empresa);
        formData.append('produtos', JSON.stringify(produtosInseridos));

        fetch("processar-entrada.php", {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // Verificar se a resposta indica sucesso
            if (data.includes("sucesso") || data.includes("Sucesso") || data.includes("SUCESSO")) {
                mostrarNotificacao("Entrada registrada com sucesso!", "success");
                
                // Limpar lista de produtos inseridos
                produtosInseridos = [];

                // Limpar tabela de produtos inseridos
                atualizarTabela();

                // Limpar os campos de entrada do formulário
                document.getElementById("codigo").value = "";
                document.getElementById("nome").value = "";
                document.getElementById("quantidade").value = "";

                // Também limpar os campos da empresa
                document.getElementById("codigo_empresa").value = "";
                document.getElementById("nome_empresa").value = "";
                document.getElementById("cnpj").value = "";
                
                // Atualizar o código da entrada para o próximo
                atualizarCodigoEntrada();
                
                // Fechar o popup de confirmação
                fecharConfirmacaoPopup();
            } else {
                mostrarNotificacao("Erro ao registrar entrada: " + data, "error");
                fecharConfirmacaoPopup();
            }
        })
        .catch(error => {
            console.error("Erro ao registrar entrada:", error);
            mostrarNotificacao("Erro ao processar a entrada. Por favor, tente novamente.", "error");
            fecharConfirmacaoPopup();
        });
    }

    // Função para mostrar notificação
    function mostrarNotificacao(mensagem, tipo) {
        const notificacao = document.createElement("div");
        notificacao.className = "notificacao " + tipo;
        notificacao.textContent = mensagem;
        
        document.body.appendChild(notificacao);
        
        // Animar entrada
        setTimeout(() => {
            notificacao.style.right = "20px";
        }, 10);
        
        // Remover após 3 segundos
        setTimeout(() => {
            notificacao.style.right = "-300px";
            setTimeout(() => {
                document.body.removeChild(notificacao);
            }, 500);
        }, 3000);
    }

    // Função para atualizar o código da entrada para o próximo
    function atualizarCodigoEntrada() {
        // Obter o código atual e incrementar
        const codigoAtual = parseInt(document.getElementById("codigo_entrada").value);
        const proximoCodigo = codigoAtual + 1;
        
        // Atualizar o campo com o próximo código
        document.getElementById("codigo_entrada").value = proximoCodigo;
    }

    function abrirPopupProdutos() {
        document.getElementById("produtoPopup").classList.add("active");
    }

    function fecharPopupProdutos() {
        document.getElementById("produtoPopup").classList.remove("active");
    }

    function selecionarProduto(codigo, nome) {
        document.getElementById("codigo").value = codigo;
        document.getElementById("nome").value = nome;
        fecharPopupProdutos();
    }

    function filtrarProdutos() {
        let termo = document.getElementById("pesquisaProduto").value.toLowerCase();
        let linhas = document.querySelectorAll("#listaPopupProdutos tr");
        linhas.forEach(tr => {
            let nome = tr.children[1].textContent.toLowerCase();
            tr.style.display = nome.includes(termo) ? "" : "none";
        });
    }
</script>

<script>
    let empresas = [];

    function buscarEmpresa() {
        let codigo_empresa = document.getElementById("codigo_empresa").value.trim();

        if (codigo_empresa === "") {
            document.getElementById("nome_empresa").value = "";
            document.getElementById("cnpj").value = "";
            return;
        }

        fetch("buscar-empresa.php?codigo_empresa=" + encodeURIComponent(codigo_empresa))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById("nome_empresa").value = data.nome_empresa;
                    document.getElementById("cnpj").value = data.cnpj;
                } else {
                    document.getElementById("nome_empresa").value = "";
                    document.getElementById("cnpj").value = "";
                }
            })
            .catch(error => console.error("Erro ao buscar empresa:", error));
    }

    function abrirPopupEmpresas() {
        document.getElementById("popupEmpresas").classList.add("active");
    }

    function fecharPopupEmpresas() {
        document.getElementById("popupEmpresas").classList.remove("active");
    }

    function selecionarEmpresa(codigo, nome, cnpj) {
        document.getElementById("codigo_empresa").value = codigo;
        document.getElementById("nome_empresa").value = nome;
        document.getElementById("cnpj").value = cnpj;
        fecharPopupEmpresas();
    }

    function filtrarEmpresas() {
        let termo = document.getElementById("filtroEmpresa").value.toLowerCase();
        let linhas = document.querySelectorAll("#listaEmpresas tr");
        linhas.forEach(tr => {
            let nome = tr.children[1].textContent.toLowerCase();
            tr.style.display = nome.includes(termo) ? "" : "none";
        });
    }
</script>

</body>
</html>

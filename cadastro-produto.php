<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';
$prox_codigo = 1;
$res = $conn->query("SELECT MAX(codigo) as max_codigo FROM produtos");
if ($res && $row = $res->fetch_assoc()) {
    $prox_codigo = $row['max_codigo'] + 1;
    if ($prox_codigo < 1) $prox_codigo = 1;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Cadastro de Produtos</title>
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        /* Título minimalista */
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        /* Formulário minimalista */
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        /* Grupos de campos */
        .form-group {
            margin-bottom: 10px;
        }
        
        /* Labels minimalistas */
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        
        /* Inputs minimalistas */
        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        input:hover, select:hover {
            border-color: #9ca3af;
        }
        
        /* Botão minimalista */
        button[type="submit"] {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        button[type="submit"]:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        button[type="submit"]:active {
            transform: translateY(0);
        }
        
        /* Mensagem de erro */
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        /* Popup minimalista */
        .popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2563eb;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid #1d4ed8;
        }

        .popup.show {
            opacity: 1;
            transform: translateY(0);
        }

        .popup.error {
            background: #dc2626;
            border-color: #b91c1c;
        }
        
        /* Validação de campos */
        input:invalid, select:invalid, textarea:invalid {
            border-color: #d1d5db;
            background-color: #ffffff;
        }
        
        input:invalid:focus, select:invalid:focus, textarea:invalid:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Classe para campos com erro */
        .field-error {
            border-color: #dc2626 !important;
            background-color: #fef2f2 !important;
        }
        
        .field-error:focus {
            border-color: #dc2626 !important;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
        }
        
        /* Placeholder para campos com erro */
        .field-error::placeholder {
            color: #dc2626;
            font-weight: 500;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            
            form {
                padding: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>
<div class="content-container">
    <h1>Cadastro de Produtos</h1>
    <form action="salvar-produto.php" method="POST" novalidate>
        <div class="form-group">
            <label for="codigo">Código</label>
            <input type="number" id="codigo" name="codigo" value="<?php echo $prox_codigo; ?>" required>
        </div>

        <div class="form-group" style="display: flex; gap: 12px; align-items: flex-end;">
            <div style="flex: 0 0 110px;">
                <label for="unidade_medida">Unidade</label>
                <input type="text" id="unidade_medida" name="unidade_medida" maxlength="5" placeholder="Ex: UN, DZ, ML, LT, GL" style="width: 100%; text-transform: uppercase;" value="<?php echo (isset($_GET['unidade_medida']) ? htmlspecialchars($_GET['unidade_medida']) : ''); ?>" required>
            </div>
            <div style="flex: 1;">
                <label for="nome">Nome do Produto</label>
                <?php if (isset($_GET['status'], $_GET['message']) && ($_GET['message'] === 'nome_duplicado' || $_GET['message'] === 'codigo_duplicado')): ?>
                    <div class="error-message">Este nome de produto ou código já existe.</div>
                <?php endif; ?>
                <input type="text" id="nome" name="nome" value="<?php echo (isset($_GET['nome']) ? htmlspecialchars($_GET['nome']) : ''); ?>" required>
            </div>
        </div>

        <div class="form-group">
            <label for="categoria">Categoria</label>
            <select id="categoria" name="categoria" required>
                <option value="">Selecione uma categoria</option>
                <?php
                $categorias = [
                    "Equipamentos de Proteção Individual (EPIs)",
                    "Materiais de Escritório",
                    "Materiais de Limpeza",
                    "Ferramentas e Equipamentos Manuais",
                    "Uniformes e Roupas de Trabalho",
                    "Materiais Elétricos",
                    "Peças de Reposição e Componentes Mecânicos",
                    "Produtos de Informática",
                    "Materiais de Construção",
                    "Itens de Consumo Rápido (Descartáveis)",
                    "Materiais de Higiene Pessoal",
                    "Produtos Químicos Industriais",
                    "Equipamentos Eletrônicos",
                    "Materiais de Embalagem",
                    "Suprimentos Médicos",
                    "Alimentos e Bebidas para Consumo Interno"
                ];
                $catSel = (isset($_GET['status']) && $_GET['status'] === 'success') ? '' : (isset($_GET['categoria']) ? $_GET['categoria'] : '');
                foreach ($categorias as $cat) {
                    $sel = ($catSel === $cat) ? 'selected' : '';
                    echo "<option value=\"$cat\" $sel>$cat</option>";
                }
                ?>
            </select>
        </div>

        <div class="form-group">
            <label for="ca">CA</label>
            <input type="text" id="ca" name="ca" value="<?php echo (isset($_GET['ca']) ? htmlspecialchars($_GET['ca']) : ''); ?>" required>
        </div>

        <div class="form-group">
            <label for="validade">Data de Validade</label>
            <input type="date" id="validade" name="validade" value="<?php echo (isset($_GET['validade']) ? htmlspecialchars($_GET['validade']) : ''); ?>" required>
        </div>
        
        <div class="form-group" id="validade_uso_group" style="display:none;">
            <label for="validade_uso">Validade de Uso (meses)</label>
            <input type="number" id="validade_uso" name="validade_uso" min="1" step="1" value="<?php echo (isset($_GET['validade_uso']) ? htmlspecialchars($_GET['validade_uso']) : ''); ?>">
        </div>

        <div class="form-group">
            <label for="valor">Valor</label>
            <input type="number" step="0.01" id="valor" name="valor" value="<?php echo (isset($_GET['valor']) ? htmlspecialchars($_GET['valor']) : ''); ?>" required>
        </div>

        <div class="form-group">
            <label for="valor_bruto">Valor Bruto</label>
            <input type="number" step="0.01" id="valor_bruto" name="valor_bruto" value="<?php echo (isset($_GET['valor_bruto']) ? htmlspecialchars($_GET['valor_bruto']) : ''); ?>" required>
        </div>

        <div class="form-group">
            <label for="quantidade">Quantidade</label>
            <input type="number" id="quantidade" name="quantidade" value="<?php echo (isset($_GET['quantidade']) ? htmlspecialchars($_GET['quantidade']) : ''); ?>" required>
        </div>

        <button type="submit">Cadastrar Produto</button>
    </form>

    <!-- Popup para mensagens -->
    <div id="popup" class="popup"></div>

    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const status = urlParams.get('status');
        const message = urlParams.get('message');
        const popup = document.getElementById('popup');

        if (status === 'success') {
            popup.textContent = "Produto cadastrado com sucesso!";
            popup.classList.add('show');
            // Limpar os campos do formulário
            const form = document.querySelector('form[action="salvar-produto.php"]');
            if (form) form.reset();
        } else if (status === 'error') {
            if (message === 'codigo_duplicado') {
                popup.textContent = "Erro: o código do produto já existe!";
            } else if (message === 'nome_duplicado') {
                popup.textContent = "Erro: o nome do produto já existe!";
            } else {
                popup.textContent = "Erro ao cadastrar produto.";
            }
            popup.classList.add('show', 'error');
        }

        if (popup.classList.contains('show')) {
            setTimeout(() => {
                popup.classList.remove('show');
            }, 5000);
        }

        // Exibir campo validade_uso para categorias específicas
        const categoriaSelect = document.getElementById('categoria');
        const validadeUsoGroup = document.getElementById('validade_uso_group');
        categoriaSelect.addEventListener('change', function() {
            if (this.value === 'Equipamentos de Proteção Individual (EPIs)' || this.value === 'Uniformes e Roupas de Trabalho') {
                validadeUsoGroup.style.display = 'block';
                document.getElementById('validade_uso').required = true;
            } else {
                validadeUsoGroup.style.display = 'none';
                document.getElementById('validade_uso').required = false;
                document.getElementById('validade_uso').value = '';
            }
        });
        // Se voltar para a página com valor já selecionado
        if (categoriaSelect.value === 'Equipamentos de Proteção Individual (EPIs)' || categoriaSelect.value === 'Uniformes e Roupas de Trabalho') {
            validadeUsoGroup.style.display = 'block';
            document.getElementById('validade_uso').required = true;
        }

        // Validação visual igual aos outros cadastros
        const form = document.querySelector('form[action="salvar-produto.php"]');
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const inputs = form.querySelectorAll('input[required], select[required]');
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('field-error');
                    let fieldName = input.getAttribute('name');
                    let errorText = '';
                    switch(fieldName) {
                        case 'codigo': errorText = 'Preencha o campo Código'; break;
                        case 'unidade_medida': errorText = 'Preencha o campo Unidade'; break;
                        case 'nome': errorText = 'Preencha o campo Nome do Produto'; break;
                        case 'categoria': errorText = 'Preencha o campo Categoria'; break;
                        case 'ca': errorText = 'Preencha o campo CA'; break;
                        case 'validade': errorText = 'Preencha o campo Data de Validade'; break;
                        case 'valor': errorText = 'Preencha o campo Valor'; break;
                        case 'valor_bruto': errorText = 'Preencha o campo Valor Bruto'; break;
                        case 'quantidade': errorText = 'Preencha o campo Quantidade'; break;
                        default: errorText = 'Preencha este campo';
                    }
                    input.setAttribute('placeholder', errorText);
                } else {
                    input.classList.remove('field-error');
                    input.removeAttribute('placeholder');
                }
            });
            if (!isValid) {
                e.preventDefault();
            }
        });
        // Remover erro ao digitar
        form.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('input', function() {
                if (this.classList.contains('field-error')) {
                    this.classList.remove('field-error');
                    this.removeAttribute('placeholder');
                }
            });
            input.addEventListener('change', function() {
                if (this.classList.contains('field-error')) {
                    this.classList.remove('field-error');
                    this.removeAttribute('placeholder');
                }
            });
        });
    </script>
</div>
</body>
</html>

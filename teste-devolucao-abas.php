<?php
require_once 'conexao.php';

echo "<h2>Teste do Sistema de Abas - Devolução</h2>";

// Verificar se existem produtos para teste
$sql_produtos = "SELECT codigo, nome, categoria, quantidade FROM produtos ORDER BY nome LIMIT 10";
$result = $conn->query($sql_produtos);

if ($result && $result->num_rows > 0) {
    echo "<h3>✅ Produtos Disponíveis para Teste:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Código</th><th>Nome</th><th>Categoria</th><th>Quantidade</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['codigo'] . "</td>";
        echo "<td>" . $row['nome'] . "</td>";
        echo "<td>" . ($row['categoria'] ?? 'N/A') . "</td>";
        echo "<td>" . $row['quantidade'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<h3>⚠️ Nenhum produto encontrado</h3>";
}

// Verificar tabelas de devolução
echo "<h3>📋 Verificação das Tabelas:</h3>";

$result = $conn->query("SHOW TABLES LIKE 'devolucoes_rapidas'");
echo "<p>Tabela 'devolucoes_rapidas': " . ($result->num_rows > 0 ? "✅ Existe" : "⚠️ Será criada automaticamente") . "</p>";

// Verificar devoluções rápidas existentes
if ($result->num_rows > 0) {
    $sql_dev_rapidas = "SELECT * FROM devolucoes_rapidas ORDER BY data_devolucao DESC LIMIT 5";
    $result_dev = $conn->query($sql_dev_rapidas);
    
    if ($result_dev && $result_dev->num_rows > 0) {
        echo "<h4>Devoluções Rápidas Registradas:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Produto</th><th>Quantidade</th><th>Estado</th><th>Data</th></tr>";
        while ($row = $result_dev->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['produto_nome']}</td>";
            echo "<td>{$row['quantidade']}</td>";
            echo "<td>{$row['estado']}</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($row['data_devolucao'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Nenhuma devolução rápida registrada ainda</p>";
    }
}

echo "<h3>🎯 Funcionalidades Implementadas:</h3>";
echo "<div style='display: flex; gap: 30px;'>";

echo "<div style='flex: 1; background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
echo "<h4>📋 Aba 1: Devolução por Funcionário</h4>";
echo "<ul>";
echo "<li>✅ Seleção de funcionário via popup</li>";
echo "<li>✅ Carregamento automático dos EPIs</li>";
echo "<li>✅ Campos de quantidade e estado</li>";
echo "<li>✅ Validação de quantidades</li>";
echo "<li>✅ Atualização da ficha EPI</li>";
echo "<li>✅ Controle de estoque por estado</li>";
echo "</ul>";
echo "</div>";

echo "<div style='flex: 1; background: #e8f5e8; padding: 20px; border-radius: 8px;'>";
echo "<h4>⚡ Aba 2: Devolução Rápida</h4>";
echo "<ul>";
echo "<li>✅ Campo código + nome + lupa</li>";
echo "<li>✅ Popup de seleção de produtos</li>";
echo "<li>✅ Busca por código/nome/categoria</li>";
echo "<li>✅ Campos dinâmicos (adicionar/remover)</li>";
echo "<li>✅ Validação completa dos campos</li>";
echo "<li>✅ Processamento direto ao estoque</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "<h3>🔄 Estados de Produto (ambas as abas):</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<ul>";
echo "<li><strong>Novo / Sem Uso:</strong> Volta ao estoque original</li>";
echo "<li><strong>Usado – Bom Estado:</strong> Cria produto 'NOME SEMI'</li>";
echo "<li><strong>Usado – Desgaste leve:</strong> Cria produto 'NOME SEMI'</li>";
echo "<li><strong>Danificado – Reparo Possível:</strong> Registra mas não volta ao estoque</li>";
echo "<li><strong>Danificado – Irrecuperável:</strong> Não volta ao estoque</li>";
echo "<li><strong>Vencido / Fora da Validade:</strong> Não volta ao estoque</li>";
echo "<li><strong>Descarte:</strong> Não volta ao estoque</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🧪 Como Testar:</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<h4>Aba 1 - Devolução por Funcionário:</h4>";
echo "<ol>";
echo "<li>Acesse <a href='devolucao.php' target='_blank'>devolucao.php</a></li>";
echo "<li>Certifique-se que a aba 'Devolução por Funcionário' está ativa</li>";
echo "<li>Clique na lupa para selecionar funcionário</li>";
echo "<li>Preencha quantidades e estados</li>";
echo "<li>Finalize a devolução</li>";
echo "</ol>";

echo "<h4>Aba 2 - Devolução Rápida:</h4>";
echo "<ol>";
echo "<li>Clique na aba 'Devolução Rápida'</li>";
echo "<li>Digite código do produto ou clique na lupa</li>";
echo "<li>Preencha quantidade e estado</li>";
echo "<li>Clique '+ Adicionar Outro Produto' para mais itens</li>";
echo "<li>Finalize a devolução rápida</li>";
echo "</ol>";
echo "</div>";

echo "<h3>📊 Endpoints Criados:</h3>";
echo "<ul>";
echo "<li><a href='obter-todos-produtos.php' target='_blank'>obter-todos-produtos.php</a> - Lista todos os produtos</li>";
echo "<li><strong>processar-devolucao-rapida.php</strong> - Processa devolução rápida</li>";
echo "</ul>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin-right: 10px;'>🔗 Testar Sistema de Abas</a>";
echo "<a href='obter-todos-produtos.php' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>📋 Ver Produtos Disponíveis</a>";
echo "</p>";

$conn->close();
?>

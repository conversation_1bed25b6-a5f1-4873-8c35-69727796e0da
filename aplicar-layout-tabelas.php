<?php
echo "<h2>🔄 Aplicar Layout de Tabelas Laterais</h2>";

// Lista dos cards que precisam ser atualizados
$cards_config = [
    'validade' => [
        'title' => 'Produtos com Validade Próxima',
        'icon' => 'fas fa-calendar-alt',
        'table_title' => 'Produtos Próximos do Vencimento',
        'table_icon' => 'fas fa-calendar-times',
        'has_chart' => true
    ],
    'entradas' => [
        'title' => 'Entradas por Mês',
        'icon' => 'fas fa-arrow-down',
        'table_title' => 'Detalhes das Entradas',
        'table_icon' => 'fas fa-list',
        'has_chart' => true
    ],
    'saidas' => [
        'title' => 'Saídas por Mês',
        'icon' => 'fas fa-arrow-up',
        'table_title' => 'Detalhes das Saídas',
        'table_icon' => 'fas fa-list',
        'has_chart' => true
    ],
    'comparativo' => [
        'title' => 'Comparativo Entradas vs Saídas',
        'icon' => 'fas fa-chart-line',
        'table_title' => 'Dados Comparativos',
        'table_icon' => 'fas fa-balance-scale',
        'has_chart' => true
    ],
    'cnpj' => [
        'title' => 'Valor Total por CNPJ',
        'icon' => 'fas fa-money-bill-wave',
        'table_title' => 'Empresas por Valor',
        'table_icon' => 'fas fa-building',
        'has_chart' => true
    ],
    'gastos-setor' => [
        'title' => 'Gastos por Setor',
        'icon' => 'fas fa-chart-pie',
        'table_title' => 'Detalhes por Setor',
        'table_icon' => 'fas fa-users',
        'has_chart' => true
    ],
    'contrato' => [
        'title' => 'Valor Total por Contrato',
        'icon' => 'fas fa-file-contract',
        'table_title' => 'Contratos por Valor',
        'table_icon' => 'fas fa-file-invoice-dollar',
        'has_chart' => true
    ],
    'epis' => [
        'title' => 'EPIs Próximos do Vencimento',
        'icon' => 'fas fa-shield-alt',
        'table_title' => 'EPIs por Vencimento',
        'table_icon' => 'fas fa-shield-virus',
        'has_chart' => false
    ]
];

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📊 Cards a serem atualizados:</h3>";
echo "<ul>";
foreach ($cards_config as $id => $config) {
    echo "<li><strong>{$config['title']}</strong> (ID: $id)</li>";
}
echo "</ul>";
echo "</div>";

// Gerar código HTML para cada card
echo "<h3>🔧 Código HTML Gerado:</h3>";

foreach ($cards_config as $id => $config) {
    echo "<div style='background: white; padding: 15px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007cba;'>";
    echo "<h4>📋 Card: {$config['title']}</h4>";
    
    // Botão de toggle
    echo "<h5>1. Botão de Toggle (adicionar antes do dropdown):</h5>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;'>";
    echo htmlspecialchars('<button class="table-toggle-btn" onclick="toggleTable(\'table-' . $id . '\', this)" title="Mostrar tabela">
  <i class="fas fa-table"></i>
</button>');
    echo "</pre>";
    
    // Layout do conteúdo
    echo "<h5>2. Layout do Conteúdo:</h5>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;'>";
    
    $content_html = '<div class="card-content">
  <div class="card-content-with-table">
    <div class="chart-section">';
    
    if ($config['has_chart']) {
        $content_html .= '
      <!-- Conteúdo do gráfico existente aqui -->';
    } else {
        $content_html .= '
      <!-- Conteúdo sem gráfico (filtros, etc.) -->';
    }
    
    $content_html .= '
    </div>
    
    <div class="table-section" id="table-' . $id . '">
      <div class="table-section-title">
        <i class="' . $config['table_icon'] . '"></i>
        ' . $config['table_title'] . '
      </div>
      <div class="table-container">
        <!-- Tabela existente aqui -->
      </div>
    </div>
  </div>
</div>';
    
    echo htmlspecialchars($content_html);
    echo "</pre>";
    echo "</div>";
}

// Instruções de aplicação
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📝 Instruções de Aplicação:</h3>";
echo "<ol>";
echo "<li><strong>Para cada card:</strong></li>";
echo "<ul>";
echo "<li>Adicionar o botão de toggle antes do dropdown de exportação</li>";
echo "<li>Envolver o conteúdo existente com a nova estrutura</li>";
echo "<li>Mover a tabela para dentro da seção table-section</li>";
echo "<li>Adicionar o título da seção com ícone apropriado</li>";
echo "</ul>";
echo "<li><strong>Testar:</strong></li>";
echo "<ul>";
echo "<li>Verificar se o botão de tabela aparece</li>";
echo "<li>Testar o toggle (mostrar/esconder)</li>";
echo "<li>Verificar responsividade</li>";
echo "<li>Confirmar que exportação ainda funciona</li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

// Links úteis
echo "<h3>🔗 Links para Teste:</h3>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0 10px;'>🏠 Dashboard Principal</a>";
echo "</div>";

// JavaScript para copiar código
echo "<script>";
echo "function copiarCodigo(elemento) {";
echo "  const texto = elemento.textContent;";
echo "  navigator.clipboard.writeText(texto).then(() => {";
echo "    alert('Código copiado para a área de transferência!');";
echo "  });";
echo "}";
echo "</script>";

echo "<style>";
echo "pre { cursor: pointer; }";
echo "pre:hover { background: #e9ecef !important; }";
echo "</style>";
?>

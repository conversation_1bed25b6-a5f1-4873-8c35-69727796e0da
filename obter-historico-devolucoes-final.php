<?php
// Versão final - JSON puro garantido
ob_start();

// Desabilitar completamente qualquer saída de erro
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

// Iniciar sessão silenciosamente
@session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
}

$historico = [];

try {
    // Incluir conexão silenciosamente
    @include_once 'conexao.php';
    
    if (isset($conn)) {
        // Buscar devoluções por funcionário
        $sql1 = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_epi'";
        $result1 = @$conn->query($sql1);
        
        if ($result1 && $result1->fetch_assoc()['count'] > 0) {
            $sql = "
                SELECT 
                    id,
                    pessoa_id,
                    pessoa_nome,
                    produto_id,
                    produto_nome,
                    quantidade,
                    estado,
                    data_devolucao,
                    usuario_id,
                    'Administrador' as usuario_nome,
                    'funcionario' as tipo
                FROM devolucoes_epi
                ORDER BY data_devolucao DESC
                LIMIT 100
            ";
            
            $result = @$conn->query($sql);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $historico[] = [
                        'id' => $row['id'],
                        'pessoa_id' => $row['pessoa_id'],
                        'pessoa_nome' => $row['pessoa_nome'] ?: '',
                        'produto_id' => $row['produto_id'] ?: '',
                        'produto_nome' => $row['produto_nome'] ?: '',
                        'quantidade' => intval($row['quantidade']),
                        'estado' => $row['estado'] ?: 'descarte',
                        'data_devolucao' => $row['data_devolucao'],
                        'usuario_id' => intval($row['usuario_id']),
                        'usuario_nome' => 'Administrador',
                        'tipo' => 'funcionario',
                        'assinatura' => ''
                    ];
                }
            }
        }
        
        // Buscar devoluções rápidas
        $sql2 = "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'devolucoes_rapidas'";
        $result2 = @$conn->query($sql2);
        
        if ($result2 && $result2->fetch_assoc()['count'] > 0) {
            $sql = "
                SELECT 
                    id,
                    produto_id,
                    produto_nome,
                    quantidade,
                    estado,
                    data_devolucao,
                    usuario_id
                FROM devolucoes_rapidas
                ORDER BY data_devolucao DESC
                LIMIT 100
            ";
            
            $result = @$conn->query($sql);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $historico[] = [
                        'id' => $row['id'],
                        'pessoa_id' => null,
                        'pessoa_nome' => null,
                        'produto_id' => $row['produto_id'] ?: '',
                        'produto_nome' => $row['produto_nome'] ?: '',
                        'quantidade' => intval($row['quantidade']),
                        'estado' => $row['estado'] ?: 'descarte',
                        'data_devolucao' => $row['data_devolucao'],
                        'usuario_id' => intval($row['usuario_id']),
                        'usuario_nome' => 'Administrador',
                        'tipo' => 'rapida',
                        'assinatura' => ''
                    ];
                }
            }
        }
        
        @$conn->close();
    }
    
} catch (Exception $e) {
    // Silenciar qualquer erro
}

// Ordenar por data
if (count($historico) > 0) {
    usort($historico, function($a, $b) {
        return strtotime($b['data_devolucao']) - strtotime($a['data_devolucao']);
    });
}

// Limpar buffer de saída
ob_end_clean();

// Enviar headers e JSON
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

echo json_encode($historico, JSON_UNESCAPED_UNICODE);
exit;
?>

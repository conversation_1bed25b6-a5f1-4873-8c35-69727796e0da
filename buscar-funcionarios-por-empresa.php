<?php
include 'conexao.php';

header('Content-Type: application/json');

// Verificar se o código da empresa foi fornecido
$empresa_codigo = $_GET['empresa_codigo'] ?? $_GET['empresa'] ?? '';

if (empty($empresa_codigo)) {
    echo json_encode(['success' => false, 'message' => 'Código da empresa não fornecido']);
    exit;
}

try {
    // Buscar o nome da empresa pelo código
    $stmt_empresa = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
    $stmt_empresa->bind_param("s", $empresa_codigo);
    $stmt_empresa->execute();
    $result_empresa = $stmt_empresa->get_result();

    if ($result_empresa->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Empresa não encontrada']);
        exit;
    }

    $empresa = $result_empresa->fetch_assoc();
    $nome_empresa = $empresa['nome_empresa'];

    // Verificar quais colunas existem na tabela pessoas
    $colunas_pessoas = $conn->query("DESCRIBE pessoas");
    $tem_empresa_id = false;
    $campos_empresa = [];
    $colunas_disponiveis = [];

    while ($coluna = $colunas_pessoas->fetch_assoc()) {
        $nome_coluna = $coluna['Field'];
        $colunas_disponiveis[] = $nome_coluna;

        if ($nome_coluna === 'empresa_id') {
            $tem_empresa_id = true;
        }
        if (in_array($nome_coluna, ['empresa', 'empresa_nome', 'posto'])) {
            $campos_empresa[] = $nome_coluna;
        }
    }

    // Determinar quais campos usar baseado no que existe
    $campos_select = ['id', 'nome'];
    if (in_array('setor', $colunas_disponiveis)) $campos_select[] = 'setor';
    if (in_array('cargo', $colunas_disponiveis)) $campos_select[] = 'cargo';
    if (in_array('posto', $colunas_disponiveis)) $campos_select[] = 'posto';
    if (in_array('funcao', $colunas_disponiveis)) $campos_select[] = 'funcao';

    $campos_sql = implode(', ', $campos_select);

    $funcionarios = [];

    // Verificar se é para mostrar todos os funcionários
    if ($empresa_codigo === 'TODOS') {
        $stmt = $conn->prepare("
            SELECT $campos_sql
            FROM pessoas
            WHERE status = 'ativo'
            ORDER BY nome
        ");
    } else if (in_array('posto', $colunas_disponiveis)) {
        // Priorizar busca pela coluna 'posto' com código da empresa
        $stmt = $conn->prepare("
            SELECT $campos_sql
            FROM pessoas
            WHERE posto = ? AND status = 'ativo'
            ORDER BY nome
        ");
        $stmt->bind_param("s", $empresa_codigo);
    } else if ($tem_empresa_id) {
        // Buscar por empresa_id (código da empresa)
        $stmt = $conn->prepare("
            SELECT $campos_sql
            FROM pessoas
            WHERE empresa_id = ? AND status = 'ativo'
            ORDER BY nome
        ");
        $stmt->bind_param("s", $empresa_codigo);
    } else if (!empty($campos_empresa)) {
        // Construir consulta dinâmica baseada nos campos de empresa disponíveis
        $condicoes_empresa = [];
        $valores = [];
        foreach ($campos_empresa as $campo) {
            if ($campo === 'posto') {
                $condicoes_empresa[] = "$campo = ?";
                $valores[] = $empresa_codigo; // Para posto, usar código
            } else {
                $condicoes_empresa[] = "$campo = ?";
                $valores[] = $nome_empresa; // Para outros campos, usar nome
            }
        }
        $where_empresa = implode(' OR ', $condicoes_empresa);

        $stmt = $conn->prepare("
            SELECT $campos_sql
            FROM pessoas
            WHERE ($where_empresa) AND status = 'ativo'
            ORDER BY nome
        ");

        // Bind parameters
        $tipos = str_repeat('s', count($valores));
        $stmt->bind_param($tipos, ...$valores);
    } else {
        // Se não há relacionamento, retornar todos os funcionários
        $stmt = $conn->prepare("
            SELECT $campos_sql
            FROM pessoas
            WHERE status = 'ativo'
            ORDER BY nome
        ");
    }

    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        // Usar os campos disponíveis dinamicamente
        $setor = '';
        if (isset($row['setor'])) $setor = $row['setor'];
        elseif (isset($row['posto'])) $setor = $row['posto'];

        $cargo = '';
        if (isset($row['cargo'])) $cargo = $row['cargo'];
        elseif (isset($row['funcao'])) $cargo = $row['funcao'];

        $funcionarios[] = [
            'id' => $row['id'],
            'nome' => $row['nome'],
            'setor' => $setor,
            'cargo' => $cargo
        ];
    }

    echo json_encode([
        'success' => true,
        'funcionarios' => $funcionarios,
        'empresa' => [
            'codigo' => $empresa_codigo,
            'nome' => $nome_empresa
        ],
        'total' => count($funcionarios)
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro ao buscar funcionários: ' . $e->getMessage()]);
}

$conn->close();
?>
<?php
include 'conexao.php';

// Verificar se o ID foi fornecido
if (!isset($_POST['delete_id']) || empty($_POST['delete_id'])) {
    echo json_encode(['success' => false, 'message' => 'ID da requisição não fornecido']);
    exit;
}

$id = $_POST['delete_id'];

// Iniciar transação
$conn->begin_transaction();

try {
    // Excluir os itens da requisição
    // Nota: Se você tiver uma tabela de itens relacionada, use esta consulta
    $stmt = $conn->prepare("DELETE FROM itens_requisicao WHERE codigo_solicitacao = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    
    // Excluir a requisição
    $stmt = $conn->prepare("DELETE FROM requisicoes WHERE codigo_solicitacao = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    
    if ($stmt->affected_rows === 0) {
        throw new Exception("Erro ao excluir a requisição");
    }
    
    // Commit da transação
    $conn->commit();
    
    // Redirecionar de volta com mensagem de sucesso
    header("Location: requisicoes-feitas.php?status=delete-success");
    exit;
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    
    // Redirecionar de volta com mensagem de erro
    header("Location: requisicoes-feitas.php?status=delete-error&message=" . urlencode($e->getMessage()));
    exit;
}
?>


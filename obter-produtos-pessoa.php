<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

$pessoa_id = isset($_GET['pessoa_id']) ? intval($_GET['pessoa_id']) : 0;

if ($pessoa_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'ID da pessoa inválido']);
    exit();
}

try {
    // Primeiro, verificar se os campos de devolução existem
    $result_check = $conn->query("SHOW COLUMNS FROM pessoa_epi LIKE 'quantidade_devolvida'");
    $tem_campo_devolvida = $result_check->num_rows > 0;

    // Buscar produtos vinculados à pessoa
    if ($tem_campo_devolvida) {
        // Consulta com campo quantidade_devolvida
        $sql = "
            SELECT
                pe.produto_id,
                p.nome,
                p.codigo,
                SUM(pe.quantidade) - COALESCE(SUM(pe.quantidade_devolvida), 0) as quantidade_disponivel,
                MAX(pe.data_entrega) as ultima_entrega
            FROM
                pessoa_epi pe
            JOIN
                produtos p ON pe.produto_id = p.codigo
            WHERE
                pe.pessoa_id = ?
            GROUP BY
                pe.produto_id, p.nome, p.codigo
            HAVING
                quantidade_disponivel > 0
            ORDER BY
                p.nome ASC
        ";
    } else {
        // Consulta sem campo quantidade_devolvida (fallback)
        $sql = "
            SELECT
                pe.produto_id,
                p.nome,
                p.codigo,
                pe.quantidade as quantidade_disponivel,
                pe.data_entrega as ultima_entrega
            FROM
                pessoa_epi pe
            INNER JOIN (
                SELECT
                    produto_id,
                    MAX(data_entrega) as max_data_entrega
                FROM
                    pessoa_epi
                WHERE
                    pessoa_id = ?
                GROUP BY
                    produto_id
            ) as pe_recente
                ON pe.produto_id = pe_recente.produto_id
                AND pe.data_entrega = pe_recente.max_data_entrega
            JOIN
                produtos p ON pe.produto_id = p.codigo
            WHERE
                pe.pessoa_id = ?
            ORDER BY
                p.nome ASC
        ";
    }
    
    $stmt = $conn->prepare($sql);

    if ($tem_campo_devolvida) {
        $stmt->bind_param("i", $pessoa_id);
    } else {
        $stmt->bind_param("ii", $pessoa_id, $pessoa_id);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $produtos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $produtos[] = [
                'produto_id' => $row['produto_id'],
                'nome' => $row['nome'],
                'codigo' => $row['codigo'],
                'quantidade' => $row['quantidade_disponivel'],
                'ultima_entrega' => $row['ultima_entrega']
            ];
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode($produtos);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

$conn->close();
?>

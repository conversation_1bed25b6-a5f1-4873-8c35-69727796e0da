<?php
include 'conexao.php';
header('Content-Type: application/json');
$nome = isset($_GET['nome']) ? trim($_GET['nome']) : '';
if (!$nome) {
    echo json_encode(['setor' => '']);
    exit;
}
$stmt = $conn->prepare('SELECT setor FROM pessoas WHERE nome = ? LIMIT 1');
$stmt->bind_param('s', $nome);
$stmt->execute();
$result = $stmt->get_result();
if ($row = $result->fetch_assoc()) {
    echo json_encode(['setor' => $row['setor']]);
} else {
    echo json_encode(['setor' => '']);
}
$stmt->close();
$conn->close(); 
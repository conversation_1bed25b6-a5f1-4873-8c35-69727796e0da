<?php
include 'conexao.php';
header('Content-Type: application/json; charset=utf-8');

$tipo = $_POST['tipo'] ?? '';
$codigo = $_POST['codigo'] ?? '';
$data_inicio = $_POST['data_inicio'] ?? '';
$data_fim = $_POST['data_fim'] ?? '';

if (!$tipo || !$codigo) {
    echo json_encode(['success' => false, 'message' => 'Parâmetros obrigatórios ausentes']);
    exit;
}

// Filtros de data específicos para cada consulta
$whereDatasSaida = '';
$whereDatasReq = '';
$whereDatasPM = '';
$paramsSaida = [];
$paramsReq = [];
$paramsPM = [];
$typesSaida = '';
$typesReq = '';
$typesPM = '';
if ($data_inicio && $data_fim) {
    $whereDatasSaida = ' AND s.data_saida BETWEEN ? AND ? ';
    $whereDatasReq = ' AND r.data_solicitacao BETWEEN ? AND ? ';
    $whereDatasPM = ' AND pm.data_criacao BETWEEN ? AND ? ';
    $paramsSaida = [$data_inicio, $data_fim];
    $paramsReq = [$data_inicio, $data_fim];
    $paramsPM = [$data_inicio, $data_fim];
    $typesSaida = $typesReq = $typesPM = 'ss';
}

$gastos = [];
$totalQtd = 0;
$totalValor = 0;
$totalValorBruto = 0;

// Buscar gastos realizados na tabela gastos_realizados
$where = [];
$params = [];
$types = '';

if ($tipo === 'empresa') {
    // Buscar o nome da empresa pelo código
    $nome_empresa = $codigo;
    if (is_numeric($codigo)) {
        $stmt_nome = $conn->prepare("SELECT nome_empresa FROM empresas WHERE codigo_empresa = ?");
        $stmt_nome->bind_param("i", $codigo);
        $stmt_nome->execute();
        $result_nome = $stmt_nome->get_result();
        if ($result_nome && $result_nome->num_rows > 0) {
            $row_nome = $result_nome->fetch_assoc();
            $nome_empresa = $row_nome['nome_empresa'];
        }
    }
    $where[] = 'empresa = ?';
    $params[] = $nome_empresa;
    $types .= 's';
} else {
    $where[] = 'setor = ?';
    $params[] = $codigo;
    $types .= 's';
}
if ($data_inicio && $data_fim) {
    $where[] = 'data_operacao BETWEEN ? AND ?';
    $params[] = $data_inicio;
    $params[] = $data_fim;
    $types .= 'ss';
}
$whereSql = $where ? ('WHERE ' . implode(' AND ', $where)) : '';
$sql = "SELECT * FROM gastos_realizados $whereSql ORDER BY data_operacao DESC, id DESC";
$stmt = $conn->prepare($sql);
if ($params) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$res = $stmt->get_result();
while ($row = $res->fetch_assoc()) {
    $produtos = [];
    $quantidade_total = 0;
    // Buscar itens detalhados conforme o tipo de operação
    if ($row['tipo_operacao'] === 'requisicao') {
        $stmt_itens = $conn->prepare("SELECT i.quantidade, p.nome FROM itens_solicitacao i LEFT JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_solicitacao = ?");
        $stmt_itens->bind_param("i", $row['referencia_id']);
        $stmt_itens->execute();
        $res_itens = $stmt_itens->get_result();
        while ($item = $res_itens->fetch_assoc()) {
            $produtos[] = $item['nome'] . ' (x' . $item['quantidade'] . ')';
            $quantidade_total += intval($item['quantidade']);
        }
        $stmt_itens->close();
    } else if ($row['tipo_operacao'] === 'pedido_mensal') {
        $stmt_itens = $conn->prepare("SELECT i.quantidade, p.nome FROM itens_pedido_mensal i LEFT JOIN produtos p ON i.produto = p.codigo WHERE i.codigo_pedido = ?");
        $stmt_itens->bind_param("i", $row['referencia_id']);
        $stmt_itens->execute();
        $res_itens = $stmt_itens->get_result();
        while ($item = $res_itens->fetch_assoc()) {
            $produtos[] = $item['nome'] . ' (x' . $item['quantidade'] . ')';
            $quantidade_total += intval($item['quantidade']);
        }
        $stmt_itens->close();
    } else if ($row['tipo_operacao'] === 'saida_estoque') {
        // Buscar produtos da saída de estoque
        $stmt_itens = $conn->prepare("SELECT ps.quantidade, ps.nome FROM produtos_saida ps WHERE ps.saida_id = ?");
        $stmt_itens->bind_param("i", $row['referencia_id']);
        $stmt_itens->execute();
        $res_itens = $stmt_itens->get_result();
        while ($item = $res_itens->fetch_assoc()) {
            $produtos[] = $item['nome'] . ' (x' . $item['quantidade'] . ')';
            $quantidade_total += intval($item['quantidade']);
        }
        $stmt_itens->close();
    }
    $gastos[] = [
        'data' => $row['data_operacao'],
        'origem' => $row['tipo_operacao'],
        'produto' => implode(', ', $produtos),
        'quantidade' => $quantidade_total,
        'valor' => $row['valor_total'],
        'valor_bruto' => $row['valor_total'],
        'id_origem' => $row['referencia_id'],
        'tipo_origem' => $row['tipo_operacao']
    ];
    $totalQtd += $quantidade_total;
    $totalValor += floatval($row['valor_total']);
    $totalValorBruto += floatval($row['valor_total']);
}
$stmt->close();

// Ordenar por data desc
usort($gastos, function($a, $b) {
    return strtotime($b['data']) - strtotime($a['data']);
});

echo json_encode([
    'success' => true,
    'gastos' => $gastos,
    'totalQtd' => $totalQtd,
    'totalValor' => $totalValor,
    'totalValorBruto' => $totalValorBruto
]); 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

try {
    // Buscar pessoas ativas
    $sql = "SELECT id, nome, posto, setor, funcao FROM pessoas WHERE status = 'ativo' ORDER BY nome ASC";
    $result = $conn->query($sql);
    
    $pessoas = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $pessoas[] = [
                'id' => $row['id'],
                'nome' => $row['nome'],
                'posto' => $row['posto'],
                'setor' => $row['setor'],
                'funcao' => $row['funcao']
            ];
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode($pessoas);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro interno do servidor']);
}

$conn->close();
?>

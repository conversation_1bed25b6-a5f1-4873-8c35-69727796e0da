<?php
// Iniciar sessão apenas se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';
include_once 'funcoes-gastos.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $empresa = $_POST['empresa'] ?? '';
    $setor = $_POST['setor'] ?? null;
    $valor_total = floatval($_POST['valor_total'] ?? 0);
    $tipo_operacao = $_POST['tipo_operacao'] ?? '';
    $referencia_id = intval($_POST['referencia_id'] ?? 0);
    
    if (empty($empresa) || empty($tipo_operacao) || $valor_total <= 0 || $referencia_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Parâmetros inválidos']);
        exit();
    }
    
    $sucesso = registrarGasto($conn, $empresa, $setor, $valor_total, $tipo_operacao, $referencia_id);
    
    if ($sucesso) {
        echo json_encode(['success' => true, 'message' => 'Gasto registrado com sucesso']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Nenhum limite ativo encontrado para registrar o gasto']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
}

$conn->close();
?> 
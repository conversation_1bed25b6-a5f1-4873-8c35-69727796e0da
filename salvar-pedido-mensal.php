<?php
header('Content-Type: application/json');
include 'conexao.php'; // Inclui a conexão com o banco de dados

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validar dados obrigatórios
    $campos_obrigatorios = ['solicitante', 'empresaSelecionada', 'contrato', 'finalidade', 'id_destinatario', 'funcao'];
    foreach ($campos_obrigatorios as $campo) {
        if (empty($_POST[$campo])) {
            echo json_encode(['success' => false, 'message' => "Campo obrigatório '$campo' está vazio"]);
            exit;
        }
    }

    // Obtém os dados do formulário
    $solicitante = $_POST['solicitante'];
    $empresa = $_POST['empresaSelecionada'];
    $contrato = $_POST['contrato'];
    $finalidade = $_POST['finalidade'];
    $destinatario = $_POST['id_destinatario'];
    $funcao = $_POST['funcao'];
    $setor = $_POST['setor'] ?? '';
    $observacao = $_POST['observacao'] ?? '';

    // Verificar se é pedido urgente
    $pedido_urgente = isset($_POST['pedido_urgente']) ? 1 : 0;
    $motivo_urgente = $_POST['motivo_urgente'] ?? '';

    // Insere o pedido na tabela pedidos_mensais
    $sql = "INSERT INTO pedidos_mensais (solicitante, empresa, contrato, finalidade, destinatario, funcao, setor, observacao, pedido_urgente, motivo_urgente)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("ssssssssss", $solicitante, $empresa, $contrato, $finalidade, $destinatario, $funcao, $setor, $observacao, $pedido_urgente, $motivo_urgente);

        if ($stmt->execute()) {
            $codigo_pedido = $stmt->insert_id; // Obtém o ID do novo pedido

            // Agora, insira os itens do pedido
            $itens_inseridos = 0;
            if (isset($_POST['codigo_produto']) && isset($_POST['quantidade'])) {
                $produtos = $_POST['codigo_produto'];
                $quantidades = $_POST['quantidade'];

                foreach ($produtos as $index => $produto) {
                    if (!empty($produto) && isset($quantidades[$index]) && $quantidades[$index] > 0) {
                        $quantidade = $quantidades[$index];
                        $sql_item = "INSERT INTO itens_pedido_mensal (codigo_pedido, produto, quantidade) VALUES (?, ?, ?)";
                        if ($stmt_item = $conn->prepare($sql_item)) {
                            $stmt_item->bind_param("isi", $codigo_pedido, $produto, $quantidade);
                            if ($stmt_item->execute()) {
                                $itens_inseridos++;
                            } else {
                                // Log do erro mas continua
                                error_log("Erro ao inserir item: " . $stmt_item->error);
                            }
                            $stmt_item->close();
                        }
                    }
                }
            } else if (isset($_POST['produto']) && isset($_POST['quantidade'])) {
                // Compatibilidade com o código antigo
                $produtos = $_POST['produto'];
                $quantidades = $_POST['quantidade'];

                foreach ($produtos as $index => $produto) {
                    if (!empty($produto) && isset($quantidades[$index]) && $quantidades[$index] > 0) {
                        $quantidade = $quantidades[$index];
                        $sql_item = "INSERT INTO itens_pedido_mensal (codigo_pedido, produto, quantidade) VALUES (?, ?, ?)";
                        if ($stmt_item = $conn->prepare($sql_item)) {
                            $stmt_item->bind_param("isi", $codigo_pedido, $produto, $quantidade);
                            if ($stmt_item->execute()) {
                                $itens_inseridos++;
                            } else {
                                // Log do erro mas continua
                                error_log("Erro ao inserir item: " . $stmt_item->error);
                            }
                            $stmt_item->close();
                        }
                    }
                }
            }

            echo json_encode(['success' => true, 'message' => 'Pedido salvo com sucesso!']);
            exit();
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao salvar o pedido: ' . $stmt->error]);
            exit();
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro na preparação da consulta: ' . $conn->error]);
    }
}
?>



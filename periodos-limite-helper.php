<?php
// periodos-limite-helper.php
include_once 'conexao.php';

function calcularPeriodoVigente($dataCriacao, $periodicidade, $dataReferencia = null) {
    // $periodicidade: 1=mensal, 2=bimestral, 6=semestral, 12=anual
    $inicio = new DateTime($dataCriacao);
    $hoje = $dataReferencia ? new DateTime($dataReferencia) : new DateTime();
    $periodos = 0;
    while ($inicio <= $hoje) {
        $fim = clone $inicio;
        $fim->modify("+{$periodicidade} month");
        $fim->modify('-1 day');
        if ($hoje <= $fim) {
            return [
                'data_inicio' => $inicio->format('Y-m-d'),
                'data_fim' => $fim->format('Y-m-d')
            ];
        }
        $inicio = clone $fim;
        $inicio->modify('+1 day');
        $periodos++;
    }
    // fallback: retorna o último
    return [
        'data_inicio' => $inicio->format('Y-m-d'),
        'data_fim' => $fim->format('Y-m-d')
    ];
}

function fecharPeriodoSeNecessario($id_limite, $dataCriacao, $periodicidade) {
    global $conn;
    $hoje = new DateTime();
    $periodo = calcularPeriodoVigente($dataCriacao, $periodicidade);
    $data_fim = new DateTime($periodo['data_fim']);
    if ($hoje > $data_fim) {
        // Buscar todos os gastos do ciclo vigente
        $gastos = [];
        $sql = "SELECT * FROM gastos_realizados WHERE id_limite = ? AND data BETWEEN ? AND ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('iss', $id_limite, $periodo['data_inicio'], $periodo['data_fim']);
        $stmt->execute();
        $res = $stmt->get_result();
        while ($row = $res->fetch_assoc()) {
            $gastos[] = $row;
        }
        $stmt->close();
        if ($gastos) {
            // Salvar no periodos_limite_gastos
            $sql = "INSERT INTO periodos_limite_gastos (id_limite, data_inicio, data_fim, dados_gastos) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $json = json_encode($gastos, JSON_UNESCAPED_UNICODE);
            $stmt->bind_param('isss', $id_limite, $periodo['data_inicio'], $periodo['data_fim'], $json);
            $stmt->execute();
            $stmt->close();
            // Remover gastos do ciclo da tabela ativa
            $sql = "DELETE FROM gastos_realizados WHERE id_limite = ? AND data BETWEEN ? AND ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('iss', $id_limite, $periodo['data_inicio'], $periodo['data_fim']);
            $stmt->execute();
            $stmt->close();
        }
    }
}

function listarPeriodosFinalizados($id_limite) {
    global $conn;
    $sql = "SELECT * FROM periodos_limite_gastos WHERE id_limite = ? ORDER BY data_inicio DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id_limite);
    $stmt->execute();
    $res = $stmt->get_result();
    $periodos = [];
    while ($row = $res->fetch_assoc()) {
        $periodos[] = $row;
    }
    $stmt->close();
    return $periodos;
}

function detalhesPeriodoFinalizado($id_periodo) {
    global $conn;
    $sql = "SELECT * FROM periodos_limite_gastos WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id_periodo);
    $stmt->execute();
    $res = $stmt->get_result();
    $row = $res->fetch_assoc();
    $stmt->close();
    return $row;
} 
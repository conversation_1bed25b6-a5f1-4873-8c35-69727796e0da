<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Dropdowns de Exportação</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6366f1;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 6px;
            --radius-md: 8px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--light-bg);
            padding: 20px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
        }

        .test-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Dropdown Styles */
        .export-dropdown {
            position: relative;
            display: inline-block;
        }

        .export-dropdown-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: var(--radius-sm);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .export-dropdown-btn:hover {
            background: var(--light-bg);
            color: var(--text-primary);
        }

        .export-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background: white;
            min-width: 160px;
            box-shadow: var(--shadow-lg);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            z-index: 1000;
            padding: 8px 0;
            margin-top: 4px;
        }

        .export-dropdown-content.show {
            display: block;
            animation: dropdownFadeIn 0.2s ease;
        }

        @keyframes dropdownFadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .export-dropdown-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: var(--text-primary);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            transition: background-color 0.2s ease;
        }

        .export-dropdown-item:hover {
            background: var(--light-bg);
        }

        .export-dropdown-item.excel {
            color: var(--success-color);
        }

        .export-dropdown-item.pdf {
            color: var(--danger-color);
        }

        .export-dropdown-item.excel:hover {
            background: #dcfce7;
        }

        .export-dropdown-item.pdf:hover {
            background: #fee2e2;
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: var(--radius-md);
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Teste dos Dropdowns de Exportação</h1>
        <p>Teste para verificar se todos os dropdowns estão funcionando corretamente.</p>

        <div class="status" id="status">
            <strong>Status:</strong> Carregando...
        </div>

        <!-- Teste Card 1 -->
        <div class="test-card">
            <div class="card-header">
                <h3 class="card-title">📊 Card de Teste 1</h3>
                <div class="export-dropdown">
                    <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-test1')" title="Opções de exportação">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="export-dropdown-content" id="dropdown-test1">
                        <button class="export-dropdown-item excel" onclick="testExport('Excel', 'Test1')">
                            <i class="fas fa-file-excel"></i>
                            Exportar Excel
                        </button>
                        <button class="export-dropdown-item pdf" onclick="testExport('PDF', 'Test1')">
                            <i class="fas fa-file-pdf"></i>
                            Exportar PDF
                        </button>
                    </div>
                </div>
            </div>
            <p>Este é um card de teste para verificar o funcionamento do dropdown.</p>
        </div>

        <!-- Teste Card 2 -->
        <div class="test-card">
            <div class="card-header">
                <h3 class="card-title">📈 Card de Teste 2</h3>
                <div class="export-dropdown">
                    <button class="export-dropdown-btn" onclick="toggleExportDropdown(event, 'dropdown-test2')" title="Opções de exportação">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="export-dropdown-content" id="dropdown-test2">
                        <button class="export-dropdown-item excel" onclick="testExport('Excel', 'Test2')">
                            <i class="fas fa-file-excel"></i>
                            Exportar Excel
                        </button>
                        <button class="export-dropdown-item pdf" onclick="testExport('PDF', 'Test2')">
                            <i class="fas fa-file-pdf"></i>
                            Exportar PDF
                        </button>
                    </div>
                </div>
            </div>
            <p>Segundo card de teste para verificar múltiplos dropdowns.</p>
        </div>

        <div class="status">
            <h3>🔧 Instruções de Teste:</h3>
            <ol>
                <li>Clique no botão de 3 pontos (⋮) em cada card</li>
                <li>Verifique se o dropdown abre com animação suave</li>
                <li>Teste os itens Excel (verde) e PDF (vermelho)</li>
                <li>Verifique se apenas um dropdown fica aberto por vez</li>
                <li>Teste clicar fora para fechar</li>
                <li>Teste pressionar ESC para fechar</li>
            </ol>
        </div>
    </div>

    <script>
        // Sistema de Dropdown para Exportação
        function toggleExportDropdown(event, dropdownId) {
            event.stopPropagation();
            
            // Fechar todos os outros dropdowns
            document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
                if (dropdown.id !== dropdownId) {
                    dropdown.classList.remove('show');
                }
            });
            
            // Toggle do dropdown atual
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.classList.toggle('show');
                console.log(`Dropdown ${dropdownId} ${dropdown.classList.contains('show') ? 'aberto' : 'fechado'}`);
            }
        }

        // Função de teste para exportação
        function testExport(type, card) {
            console.log(`🎯 Teste de exportação: ${type} do ${card}`);
            alert(`✅ Teste bem-sucedido!\n\nTipo: ${type}\nCard: ${card}`);
            
            // Fechar dropdown após ação
            document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }

        // Fechar dropdown ao clicar fora
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.export-dropdown')) {
                document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });

        // Fechar dropdown ao pressionar ESC
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                document.querySelectorAll('.export-dropdown-content').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
                console.log('🔑 ESC pressionado - dropdowns fechados');
            }
        });

        // Verificação quando carregado
        document.addEventListener('DOMContentLoaded', function() {
            const status = document.getElementById('status');
            const dropdowns = document.querySelectorAll('.export-dropdown-content');
            
            status.innerHTML = `
                <strong>Status:</strong> ✅ Sistema carregado com sucesso!<br>
                <strong>Dropdowns encontrados:</strong> ${dropdowns.length}<br>
                <strong>Função disponível:</strong> ${typeof toggleExportDropdown === 'function' ? 'Sim' : 'Não'}
            `;
            
            console.log('✅ Sistema de teste carregado');
            console.log(`📊 Dropdowns encontrados: ${dropdowns.length}`);
            console.log(`🔧 Função disponível: ${typeof toggleExportDropdown === 'function'}`);
        });
    </script>
</body>
</html>

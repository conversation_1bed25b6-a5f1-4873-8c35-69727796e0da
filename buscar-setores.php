<?php
include 'conexao.php';
$termo = $_GET['termo'] ?? '';
$sql = "SELECT id as codigo, nome as nome FROM setor WHERE nome LIKE ? ORDER BY nome";
$stmt = $conn->prepare($sql);
$like = "%$termo%";
$stmt->bind_param("s", $like);
$stmt->execute();
$result = $stmt->get_result();
$setores = [];
while ($row = $result->fetch_assoc()) {
    $setores[] = [
        'codigo' => str_pad($row['codigo'], 2, '0', STR_PAD_LEFT), // Sempre dois dígitos
        'nome' => $row['nome']
    ];
}
echo json_encode($setores); 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    echo "Erro: Usuário não logado<br>";
    echo "Simulando login...<br>";
    $_SESSION['usuario_id'] = 1; // Simular usuário logado
}

require_once 'conexao.php';

echo "<h2>Debug - Endpoint Histórico de Devoluções</h2>";

try {
    echo "<h3>1. Verificação das Tabelas:</h3>";
    
    // Verificar tabela devolucoes_epi
    $result_func = $conn->query("SHOW TABLES LIKE 'devolucoes_epi'");
    if ($result_func && $result_func->num_rows > 0) {
        echo "<p>✅ Tabela 'devolucoes_epi' existe</p>";
        
        $count = $conn->query("SELECT COUNT(*) as total FROM devolucoes_epi")->fetch_assoc()['total'];
        echo "<p>Registros: $count</p>";
        
        if ($count > 0) {
            echo "<h4>Estrutura da tabela:</h4>";
            $result = $conn->query("DESCRIBE devolucoes_epi");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p>❌ Tabela 'devolucoes_epi' não existe</p>";
    }
    
    // Verificar tabela devolucoes_rapidas
    $result_rapida = $conn->query("SHOW TABLES LIKE 'devolucoes_rapidas'");
    if ($result_rapida && $result_rapida->num_rows > 0) {
        echo "<p>✅ Tabela 'devolucoes_rapidas' existe</p>";
        
        $count = $conn->query("SELECT COUNT(*) as total FROM devolucoes_rapidas")->fetch_assoc()['total'];
        echo "<p>Registros: $count</p>";
    } else {
        echo "<p>❌ Tabela 'devolucoes_rapidas' não existe</p>";
    }
    
    // Verificar tabela usuarios
    $result_usuarios = $conn->query("SHOW TABLES LIKE 'usuarios'");
    if ($result_usuarios && $result_usuarios->num_rows > 0) {
        echo "<p>✅ Tabela 'usuarios' existe</p>";
        
        $count = $conn->query("SELECT COUNT(*) as total FROM usuarios")->fetch_assoc()['total'];
        echo "<p>Usuários: $count</p>";
    } else {
        echo "<p>❌ Tabela 'usuarios' não existe</p>";
    }
    
    echo "<h3>2. Teste das Consultas:</h3>";
    
    $historico = [];
    
    // Testar consulta devolucoes_epi
    if ($result_func && $result_func->num_rows > 0) {
        echo "<h4>Consulta devolucoes_epi:</h4>";
        
        $sql_funcionario = "
            SELECT 
                d.id,
                d.pessoa_id,
                d.pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                'Sistema' as usuario_nome,
                'funcionario' as tipo,
                COALESCE(d.assinatura, '') as assinatura
            FROM devolucoes_epi d
            ORDER BY d.data_devolucao DESC
            LIMIT 5
        ";
        
        echo "<pre>" . htmlspecialchars($sql_funcionario) . "</pre>";
        
        $result = $conn->query($sql_funcionario);
        if ($result) {
            echo "<p>✅ Consulta executada com sucesso</p>";
            echo "<p>Linhas retornadas: " . $result->num_rows . "</p>";
            
            while ($row = $result->fetch_assoc()) {
                $historico[] = $row;
                echo "<p>Registro: " . json_encode($row) . "</p>";
            }
        } else {
            echo "<p>❌ Erro na consulta: " . $conn->error . "</p>";
        }
    }
    
    // Testar consulta devolucoes_rapidas
    if ($result_rapida && $result_rapida->num_rows > 0) {
        echo "<h4>Consulta devolucoes_rapidas:</h4>";
        
        $sql_rapida = "
            SELECT 
                d.id,
                NULL as pessoa_id,
                NULL as pessoa_nome,
                d.produto_id,
                d.produto_nome,
                d.quantidade,
                d.estado,
                d.data_devolucao,
                d.usuario_id,
                'Sistema' as usuario_nome,
                'rapida' as tipo,
                COALESCE(d.assinatura, '') as assinatura
            FROM devolucoes_rapidas d
            ORDER BY d.data_devolucao DESC
            LIMIT 5
        ";
        
        echo "<pre>" . htmlspecialchars($sql_rapida) . "</pre>";
        
        $result = $conn->query($sql_rapida);
        if ($result) {
            echo "<p>✅ Consulta executada com sucesso</p>";
            echo "<p>Linhas retornadas: " . $result->num_rows . "</p>";
            
            while ($row = $result->fetch_assoc()) {
                $historico[] = $row;
                echo "<p>Registro: " . json_encode($row) . "</p>";
            }
        } else {
            echo "<p>❌ Erro na consulta: " . $conn->error . "</p>";
        }
    }
    
    echo "<h3>3. Resultado Final:</h3>";
    echo "<p>Total de registros no histórico: " . count($historico) . "</p>";
    
    if (!empty($historico)) {
        echo "<h4>JSON de saída:</h4>";
        echo "<pre>" . json_encode($historico, JSON_PRETTY_PRINT) . "</pre>";
    } else {
        echo "<p>⚠️ Nenhum registro encontrado</p>";
        echo "<p>Para testar, faça algumas devoluções primeiro:</p>";
        echo "<ul>";
        echo "<li><a href='devolucao.php'>Fazer devolução por funcionário</a></li>";
        echo "<li><a href='devolucao.php'>Fazer devolução rápida</a></li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>4. Teste do Endpoint:</h3>";
echo "<p><a href='obter-historico-devolucoes.php' target='_blank'>Testar obter-historico-devolucoes.php</a></p>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Voltar para Devolução</a>";
echo "</p>";

$conn->close();
?>

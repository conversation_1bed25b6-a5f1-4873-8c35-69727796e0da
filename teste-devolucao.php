<?php
require_once 'conexao.php';

echo "<h2>Teste do Sistema de Devolução</h2>";

// Verificar se existem pessoas com EPIs
$sql_pessoas_epi = "
    SELECT DISTINCT p.id, p.nome, COUNT(pe.id) as total_epis
    FROM pessoas p
    INNER JOIN pessoa_epi pe ON p.id = pe.pessoa_id
    WHERE p.status = 'ativo'
    GROUP BY p.id, p.nome
    ORDER BY p.nome
    LIMIT 5
";

$result = $conn->query($sql_pessoas_epi);

if ($result && $result->num_rows > 0) {
    echo "<h3>✅ Pessoas com EPIs encontradas:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>ID</th><th>Nome</th><th>Total EPIs</th><th>Testar</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nome'] . "</td>";
        echo "<td>" . $row['total_epis'] . "</td>";
        echo "<td><a href='obter-produtos-pessoa.php?pessoa_id=" . $row['id'] . "' target='_blank'>Ver EPIs</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<h3>⚠️ Nenhuma pessoa com EPIs encontrada</h3>";
    echo "<p>Vou criar dados de teste...</p>";
    
    // Criar pessoa de teste se não existir
    $sql_check_pessoa = "SELECT id FROM pessoas WHERE nome = 'TESTE DEVOLUCAO' LIMIT 1";
    $result_pessoa = $conn->query($sql_check_pessoa);
    
    if ($result_pessoa->num_rows == 0) {
        $sql_insert_pessoa = "INSERT INTO pessoas (nome, posto, setor, funcao, status) VALUES ('TESTE DEVOLUCAO', 'Operador', 'Produção', 'Operador de Máquina', 'ativo')";
        if ($conn->query($sql_insert_pessoa)) {
            $pessoa_id = $conn->insert_id;
            echo "<p>✅ Pessoa de teste criada com ID: $pessoa_id</p>";
            
            // Verificar se existe produto de teste
            $sql_check_produto = "SELECT codigo FROM produtos LIMIT 1";
            $result_produto = $conn->query($sql_check_produto);
            
            if ($result_produto->num_rows > 0) {
                $produto = $result_produto->fetch_assoc();
                $produto_id = $produto['codigo'];
                
                // Criar registro de EPI para teste
                $sql_insert_epi = "INSERT INTO pessoa_epi (pessoa_id, produto_id, quantidade, data_entrega) VALUES (?, ?, 2, NOW())";
                $stmt = $conn->prepare($sql_insert_epi);
                $stmt->bind_param("is", $pessoa_id, $produto_id);
                
                if ($stmt->execute()) {
                    echo "<p>✅ EPI de teste vinculado à pessoa</p>";
                } else {
                    echo "<p>❌ Erro ao vincular EPI: " . $stmt->error . "</p>";
                }
            } else {
                echo "<p>⚠️ Nenhum produto encontrado para criar EPI de teste</p>";
            }
        } else {
            echo "<p>❌ Erro ao criar pessoa de teste: " . $conn->error . "</p>";
        }
    }
}

// Verificar estrutura das tabelas
echo "<h3>📋 Verificação das Tabelas:</h3>";

// Verificar tabela pessoas
$result = $conn->query("SHOW TABLES LIKE 'pessoas'");
echo "<p>Tabela 'pessoas': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

// Verificar tabela pessoa_epi
$result = $conn->query("SHOW TABLES LIKE 'pessoa_epi'");
echo "<p>Tabela 'pessoa_epi': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

// Verificar tabela produtos
$result = $conn->query("SHOW TABLES LIKE 'produtos'");
echo "<p>Tabela 'produtos': " . ($result->num_rows > 0 ? "✅ Existe" : "❌ Não existe") . "</p>";

// Verificar tabela devolucoes_epi
$result = $conn->query("SHOW TABLES LIKE 'devolucoes_epi'");
echo "<p>Tabela 'devolucoes_epi': " . ($result->num_rows > 0 ? "✅ Existe" : "⚠️ Será criada automaticamente") . "</p>";

// Testar endpoints
echo "<h3>🔗 Teste dos Endpoints:</h3>";
echo "<p><a href='obter-pessoas.php' target='_blank'>Testar obter-pessoas.php</a></p>";

if ($result && $result->num_rows > 0) {
    $conn->data_seek(0);
    $primeira_pessoa = $result->fetch_assoc();
    if ($primeira_pessoa) {
        echo "<p><a href='obter-produtos-pessoa.php?pessoa_id=" . $primeira_pessoa['id'] . "' target='_blank'>Testar obter-produtos-pessoa.php</a></p>";
    }
}

echo "<h3>🎯 Como testar o sistema:</h3>";
echo "<ol>";
echo "<li>Acesse <a href='devolucao.php' target='_blank'>devolucao.php</a></li>";
echo "<li>Clique no botão da lupa para selecionar um funcionário</li>";
echo "<li>Selecione uma pessoa que tenha EPIs vinculados</li>";
echo "<li>Preencha as quantidades de devolução e estados dos produtos</li>";
echo "<li>Clique em 'Finalizar Devolução'</li>";
echo "<li>Verifique se a devolução foi registrada corretamente</li>";
echo "</ol>";

echo "<h3>📊 Funcionalidades Implementadas:</h3>";
echo "<ul>";
echo "<li>✅ Página de devolução com design padronizado</li>";
echo "<li>✅ Seleção de funcionário via popup com busca</li>";
echo "<li>✅ Carregamento automático dos EPIs vinculados</li>";
echo "<li>✅ Campos para quantidade e estado de devolução</li>";
echo "<li>✅ Validação de quantidades máximas</li>";
echo "<li>✅ Processamento de devolução com transação</li>";
echo "<li>✅ Atualização da ficha EPI com status de devolução</li>";
echo "<li>✅ Criação de produtos SEMI para itens usados</li>";
echo "<li>✅ Controle de estoque baseado no estado do produto</li>";
echo "</ul>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔗 Ir para Devolução</a>";
echo "<a href='fichas-epi.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📋 Ver Fichas EPI</a>";
echo "</p>";

$conn->close();
?>

<?php
session_start();

// Se já estiver logado, redirecionar para o index
if (isset($_SESSION['usuario_id'])) {
    header("Location: index.php");
    exit();
}

$erro = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    include 'conexao.php';
    
    $nome_usuario = $_POST['nome_usuario'];
    $senha = $_POST['senha'];
    
    // Buscar usuário no banco
    $sql = "SELECT id, nome_usuario, senha, tipo_usuario FROM usuarios WHERE nome_usuario = ? AND status = 'ativo'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $nome_usuario);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $usuario = $result->fetch_assoc();
        
        // Verificar senha
        if (password_verify($senha, $usuario['senha'])) {
            // Login bem-sucedido
            $_SESSION['usuario_id'] = $usuario['id'];
            $_SESSION['nome_usuario'] = $usuario['nome_usuario'];
            $_SESSION['tipo_usuario'] = $usuario['tipo_usuario'];
            
            header("Location: index.php");
            exit();
        } else {
            $erro = "Senha incorreta!";
        }
    } else {
        $erro = "Usuário não encontrado ou inativo!";
    }
    
    $conn->close();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistema de Estoque</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(120deg, #111 0%, #162447 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            margin-right: 12vw;
            position: relative;
            z-index: 2;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        
        .login-header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-login {
            width: 100%;
            padding: 12px;
            background: #162447;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
        }
        
        .error-message {
            background: #ff6b6b;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .register-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <canvas id="bg-particles"></canvas>
    <div style="position: fixed; left: 50%; top: 50%; transform: translate(-50%, -50%); z-index: 10; display: flex; align-items: center; justify-content: center;">
        <img src="assets/logo.png" alt="Logo" style="max-width: 700px; max-height: 400px; display: block;">
    </div>
    <div class="login-container">
        <div class="login-header">
            <h1>Iniciar sessão no FLUXepi</h1>
            <p>Faça login para continuar</p>
        </div>
        
        <?php if ($erro): ?>
            <div class="error-message">
                <?php echo $erro; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="nome_usuario">Nome:</label>
                <input type="text" id="nome_usuario" name="nome_usuario" required>
            </div>
            
            <div class="form-group">
                <label for="senha">Senha:</label>
                <input type="password" id="senha" name="senha" required>
            </div>
            
            <button type="submit" class="btn-login">Entrar</button>
        </form>
    </div>
</body>
<script>
// --- Animação de pontos indo para o centro ---
const canvas = document.getElementById('bg-particles');
const ctx = canvas.getContext('2d');
let w, h, cx, cy;
function resize() {
    w = window.innerWidth;
    h = window.innerHeight;
    cx = w/2;
    cy = h/2 - 100;
    canvas.width = w;
    canvas.height = h;
}
resize();
window.addEventListener('resize', resize);

const POINTS = 60;
const points = [];
for (let i = 0; i < POINTS; i++) {
    const angle = Math.random() * 2 * Math.PI;
    const radius = Math.max(w, h) * (0.5 + Math.random() * 0.5);
    points.push({
        x: cx + Math.cos(angle) * radius,
        y: cy + Math.sin(angle) * radius,
        vx: 0,
        vy: 0,
        size: 2 + Math.random() * 3,
        speed: 0.5 + Math.random() * 1.2,
        baseAlpha: 0.3 + Math.random() * 0.5,
        color: 'rgba(59,130,246,1)'
    });
}

function resetParticle(p) {
    const angle = Math.random() * 2 * Math.PI;
    const radius = Math.max(w, h) * (0.5 + Math.random() * 0.5);
    p.x = cx + Math.cos(angle) * radius;
    p.y = cy + Math.sin(angle) * radius;
    p.vx = 0;
    p.vy = 0;
    p.size = 2 + Math.random() * 3;
    p.speed = 0.5 + Math.random() * 1.2;
    p.baseAlpha = 0.3 + Math.random() * 0.5;
}

function animate() {
    ctx.clearRect(0, 0, w, h);
    for (const p of points) {
        // Vetor para o centro
        const dx = cx - p.x;
        const dy = cy - p.y;
        const dist = Math.sqrt(dx*dx + dy*dy);
        // Fade out ao chegar perto do centro
        let alpha = p.baseAlpha;
        if (dist < 200) {
            alpha *= dist / 200;
        }
        // Movimento suavizado (sempre)
        p.vx += (dx/dist) * p.speed * 0.05;
        p.vy += (dy/dist) * p.speed * 0.05;
        p.vx *= 0.96;
        p.vy *= 0.96;
        p.x += p.vx;
        p.y += p.vy;
        // Desenhar ponto
        if (alpha > 0.02) {
            ctx.beginPath();
            ctx.arc(p.x, p.y, p.size, 0, 2*Math.PI);
            ctx.fillStyle = `rgba(59,130,246,${alpha})`;
            ctx.fill();
        }
    }
    requestAnimationFrame(animate);
}
animate();
// Canvas estilo
canvas.style.position = 'fixed';
canvas.style.left = '0';
canvas.style.top = '0';
canvas.style.width = '100vw';
canvas.style.height = '100vh';
canvas.style.zIndex = '0';
canvas.style.pointerEvents = 'none';
</script>
</html> 
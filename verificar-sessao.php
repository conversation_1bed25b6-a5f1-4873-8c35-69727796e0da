<?php
session_start();

echo "<h2>Verificação da Sessão</h2>";

echo "<h3><PERSON><PERSON> da Sessão Atual:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

if (!isset($_SESSION['usuario_id'])) {
    echo "<p style='color: red;'>❌ Usuário não está logado</p>";
    echo "<p>Simulando login...</p>";
    
    // Simular login com usuário ID 1
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
    $_SESSION['usuario_email'] = '<EMAIL>';
    
    echo "<p style='color: green;'>✅ Login simulado:</p>";
    echo "<ul>";
    echo "<li>ID: " . $_SESSION['usuario_id'] . "</li>";
    echo "<li>Nome: " . $_SESSION['usuario_nome'] . "</li>";
    echo "<li>Email: " . $_SESSION['usuario_email'] . "</li>";
    echo "</ul>";
} else {
    echo "<p style='color: green;'>✅ Usuário logado:</p>";
    echo "<ul>";
    echo "<li>ID: " . $_SESSION['usuario_id'] . "</li>";
    if (isset($_SESSION['usuario_nome'])) {
        echo "<li>Nome: " . $_SESSION['usuario_nome'] . "</li>";
    }
    if (isset($_SESSION['usuario_email'])) {
        echo "<li>Email: " . $_SESSION['usuario_email'] . "</li>";
    }
    echo "</ul>";
}

// Verificar se o usuário existe no banco
require_once 'conexao.php';

echo "<h3>Verificação no Banco de Dados:</h3>";

$result_usuarios = $conn->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'usuarios'");
if ($result_usuarios && $result_usuarios->fetch_assoc()['count'] > 0) {
    $usuario_id = $_SESSION['usuario_id'];
    $sql = "SELECT id, nome, email FROM usuarios WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $usuario = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ Usuário encontrado no banco:</p>";
        echo "<ul>";
        echo "<li>ID: " . $usuario['id'] . "</li>";
        echo "<li>Nome: " . $usuario['nome'] . "</li>";
        echo "<li>Email: " . $usuario['email'] . "</li>";
        echo "</ul>";
        
        // Atualizar sessão com dados do banco
        $_SESSION['usuario_nome'] = $usuario['nome'];
        $_SESSION['usuario_email'] = $usuario['email'];
        
    } else {
        echo "<p style='color: red;'>❌ Usuário ID {$usuario_id} não encontrado no banco</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Tabela 'usuarios' não existe</p>";
}

echo "<h3>Teste dos Endpoints:</h3>";
echo "<ul>";
echo "<li><a href='obter-historico-devolucoes.php' target='_blank'>Testar histórico de devoluções</a></li>";
echo "<li><a href='devolucao.php' target='_blank'>Testar página de devolução</a></li>";
echo "</ul>";

$conn->close();
?>

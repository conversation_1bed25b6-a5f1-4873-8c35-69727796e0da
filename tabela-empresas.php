<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

$feedbackMessage = "";
$feedbackType = ""; // "success" ou "error"

// Verificar se a coluna status existe, se não, criar
$result = $conn->query("SHOW COLUMNS FROM empresas LIKE 'status'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE empresas ADD COLUMN status VARCHAR(10) DEFAULT 'ativo'");
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['delete_id']) && !empty($_POST['delete_id'])) {
        $deleteId = $_POST['delete_id'];
        $sql = "DELETE FROM empresas WHERE codigo_empresa = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $deleteId);
        if ($stmt->execute()) {
            header('Location: tabela-empresas.php?status=delete-success');
        } else {
            header('Location: tabela-empresas.php?status=error&message=' . urlencode('Erro ao excluir o registro.'));
        }
        $stmt->close();
        exit;
    } elseif (isset($_POST['inativar_id']) && !empty($_POST['inativar_id'])) {
        $empresaId = $_POST['inativar_id'];
        $status = $_POST['status'];
        $sql = "UPDATE empresas SET status = ? WHERE codigo_empresa = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $status, $empresaId);
        if ($stmt->execute()) {
            header('Location: tabela-empresas.php?status=success&message=' . urlencode('Status da empresa atualizado com sucesso!'));
        } else {
            header('Location: tabela-empresas.php?status=error&message=' . urlencode('Erro ao atualizar status da empresa.'));
        }
        $stmt->close();
        exit;
    }
}

// Verificando o status da URL para exibir o feedback
if (isset($_GET['status'])) {
    if ($_GET['status'] == 'edit-success') {
        $feedbackMessage = "Edição realizada com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'edit-error') {
        $feedbackMessage = "Erro ao editar o registro.";
        $feedbackType = "error";
    } elseif ($_GET['status'] == 'delete-success') {
        $feedbackMessage = "Exclusão realizada com sucesso!";
        $feedbackType = "delete-success";
    }
}

// Definir filtro de status padrão (ativo)
$statusFilter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'ativo';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Construir a cláusula WHERE com base nos filtros
$whereClause = [];

if ($search) {
    $whereClause[] = "(nome_empresa LIKE '%$search%' OR fantasia_empresa LIKE '%$search%' OR cnpj LIKE '%$search%' OR regiao LIKE '%$search%')";
}

if ($statusFilter !== 'todos') {
    $whereClause[] = "status = '$statusFilter'";
}

// Montar a cláusula WHERE final
$whereSQL = "";
if (!empty($whereClause)) {
    $whereSQL = "WHERE " . implode(" AND ", $whereClause);
}

$result = $conn->query("SELECT * FROM empresas $whereSQL ORDER BY codigo_empresa");
if (!$result) { echo $conn->error; }
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <title>Tabela de Empresas</title>
    <style>
        /* Estilos gerais */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px; /* 270px da topbar + 20px de margem */
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Estilos modernos para a tabela principal */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        
        /* Status badges minimalistas */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-ativo {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inativo {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
            margin: 2px;
        }
        
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        /* Filtros e busca */
        .filters-container {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .filters-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .modern-table {
                font-size: 12px;
            }
            
            .modern-table th,
            .modern-table td {
                padding: 12px 8px;
            }
        }
        
        /* Feedback messages */
        .feedback {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .feedback.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback.delete-success {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Dropdown no canto superior direito da div principal */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        
        
        .main-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .main-dropdown-toggle {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 14px;
            cursor: pointer;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .main-dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .main-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        
        .main-dropdown-menu.show {
            display: block;
        }
        
        .main-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .main-dropdown-item:hover {
            background: #f9fafb;
        }
        
        .main-dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }
        
        .main-dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }
        
        /* Popup de edição */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .popup {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .popup h3 {
            margin-top: 0;
            color: #111827;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        /* Abas do popup */
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .popup-tab.active {
            background: white;
            color: #2563eb;
            border-color: #2563eb;
        }
        
        .popup-tab:hover {
            background: #f3f4f6;
        }
        
        .popup-tab-content {
            display: none;
        }
        
        .popup-tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        /* Seção de ações */
        .actions-section {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .actions-section h4 {
            margin-top: 0;
            color: #111827;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        /* Popup de feedback */
        .feedback-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }
        
        .feedback-popup.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .feedback-popup.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback-popup.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback-popup.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Ordenação de colunas */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 25px !important;
        }
        
        .sortable:hover {
            background: #f3f4f6;
        }
        
        .sort-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #9ca3af;
            display: none;
        }
        
        .sort-icon.active {
            display: inline;
            color: #2563eb;
        }
        
        .sort-icon.asc::after {
            content: "▲";
        }
        
        .sort-icon.desc::after {
            content: "▼";
        }
        
        .sort-icon.default::after {
            content: "▲▼";
        }
        
        .confirm-overlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 20000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .confirm-popup {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            padding: 32px 24px;
            min-width: 280px;
            max-width: 90vw;
            text-align: center;
            z-index: 21000;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .confirm-popup .confirm-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .confirm-popup .confirm-message {
            font-size: 1.1em;
            margin-bottom: 12px;
        }
        .confirm-popup .confirm-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }
        .confirm-popup .btn-confirm {
            background: #22c55e;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-cancel {
            background: #e5e7eb;
            color: #111;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-danger {
            background: #ef4444;
            color: #fff;
        }
        .confirm-popup .btn-warning {
            background: #facc15;
            color: #92400e;
        }
        .search-bar-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 32px 0 18px 0;
            background: none;
            box-shadow: none;
            border: none;
            padding: 0;
        }
        .search-bar-wrapper {
            position: relative;
            width: 900px;
            max-width: 98vw;
            display: flex;
            align-items: center;
        }
        .search-bar-input {
            width: 100%;
            padding: 13px 44px 13px 14px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 1.08em;
            outline: none;
            background: transparent;
            color: #222;
            transition: border 0.2s;
            box-shadow: none;
            height: 51px;
            margin-right: 0;
        }
        .search-bar-btn-inside {
            position: absolute;
            right: 0px;
            top: 1px;
            height: 50px;
            width: 55px;
            background: none;
            color: #111;
            border: none;
            border-radius: 8px 8px 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 2;
        }
        .search-bar-btn-inside i {
            color: #fff;
            font-size: 1.3em;
        }
        .filter-btn {
            margin-left: 10px;
            margin-bottom: 10px;
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
            margin-top: -7px;
        }
        .filter-slide-menu {
            display: none;
            position: absolute;
            top: 45px;
            right: 0px;
            background: #fff;
            border: 1.5px solid #d1d5db;
            border-radius: 10px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.10);
            min-width: 26px;
            z-index: 100;
            padding: 16px 18px;
        }
        .filter-slide-menu.open {
            display: block;
        }
        .close-slide-menu {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 22px;
            height: 22px;
            background: none;
            border: none;
            color: #111;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            padding: 0;
            z-index: 10;
            border-radius: 50%;
            transition: background 0.2s;
        }
        .close-slide-menu:hover {
            background: none;
            color: #000;
        }
        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 18px;
        }
        .filter-popup, .popup-filtros, .popup-filtro {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header, .popup-filtros-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup, .popup-filtros-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover, .popup-filtros-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .filter-popup-overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.4);
            z-index: 1001;
        }
        .filter-popup {
            display: none;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
    <div class="main-header">
        <h1 class="main-title">Tabela de Empresas</h1>
        <div class="main-dropdown">
            <button class="btn-modern btn-cancel main-dropdown-toggle" onclick="toggleMainDropdown()">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="main-dropdown-menu" id="mainExportDropdown">
                <div class="main-dropdown-item" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Exportar para Excel
                </div>
                <div class="main-dropdown-item" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Exportar para PDF
                </div>
            </div>
        </div>
    </div>

    <?php if ($feedbackMessage): ?>
        <!-- Removido aviso fixo, feedback só via popup -->
    <?php endif; ?>

    <!-- Popup de feedback -->
    <div id="feedbackPopup" class="feedback-popup"></div>

    <!-- Barra de busca e filtro -->
    <div class="search-bar-container">
        <div style="display: flex; align-items: center; gap: 20px; justify-content: center;">
            <div class="search-bar-wrapper">
                <input type="text" class="search-bar-input" id="searchVisible" placeholder="Pesquisar..." value="<?= htmlspecialchars($search) ?>">
                <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar">
                    <i class="fas fa-search" style="color: #111;"></i>
                </button>
            </div>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;"><i class="fas fa-filter"></i></button>
        </div>
    </div>
    <!-- Formulário oculto para busca -->
    <form id="searchForm" method="get" style="display:none;">
        <input type="hidden" id="search" name="search" value="<?= htmlspecialchars($search) ?>">
        <!-- Manter filtros avançados ao pesquisar -->
        <input type="hidden" name="status_filter" value="<?= htmlspecialchars($statusFilter) ?>">
    </form>
    <!-- Popup de filtros moderno -->
    <div id="filterPopupOverlay" class="filter-popup-overlay"></div>
    <div id="filterPopup" class="filter-popup">
        <div class="filter-popup-header">
            <span>Filtrar por:</span>
            <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
        </div>
        <form method="get" class="filter-popup-form" style="display: flex; flex-direction: column; gap: 18px;">
            <div class="filter-popup-group">
                <label for="status_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Status:</label>
                <select id="status_filter" name="status_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                    <option value="ativo" <?= $statusFilter == 'ativo' ? 'selected' : '' ?>>Ativo</option>
                    <option value="inativo" <?= $statusFilter == 'inativo' ? 'selected' : '' ?>>Inativo</option>
                    <option value="todos" <?= $statusFilter == 'todos' ? 'selected' : '' ?>>Todos</option>
                </select>
            </div>
            <div class="filter-popup-actions" style="display: flex; gap: 12px; margin-top: 10px;">
                <a href="tabela-empresas.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
            </div>
        </form>
    </div>
    <!-- Fim da barra de busca/filtro -->

    <div class="modern-table-container">
        <table class="modern-table" id="empresasTable">
            <thead>
                <tr>
                    <th class="sortable" data-sort="codigo_empresa">
                        Código
                    </th>
                    <th class="sortable" data-sort="nome_empresa">
                        Nome
                    </th>
                    <th class="sortable" data-sort="fantasia_empresa">
                        Fantasia
                    </th>
                    <th class="sortable" data-sort="cnpj">
                        CNPJ
                    </th>
                    <th class="sortable" data-sort="contrato">
                        Contrato
                    </th>
                    <th class="sortable" data-sort="regiao">
                        Região
                    </th>
                    <th class="sortable" data-sort="status">
                        Status
                    </th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dados as $row): ?>
                    <?php 
                        $status = isset($row['status']) ? $row['status'] : 'ativo';
                        $statusClass = $status === 'ativo' ? 'status-ativo' : 'status-inativo';
                    ?>
                    <tr>
                        <td><?= $row['codigo_empresa'] ?></td>
                        <td><?= $row['nome_empresa'] ?></td>
                        <td><?= $row['fantasia_empresa'] ?></td>
                        <td><?= $row['cnpj'] ?></td>
                        <td><?= $row['contrato'] ?></td>
                        <td><?= $row['regiao'] ?></td>
                        <td><span class="status-badge <?= $statusClass ?>"><?= ucfirst($status) ?></span></td>
                        <td>
                            <button class="btn-modern btn-confirm" onclick="openEditPopup(<?= htmlspecialchars(json_encode($row)) ?>)">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a href="cadastro-empresas.php" class="btn-modern btn-confirm">
            <i class="fas fa-plus"></i> Adicionar Nova Empresa
        </a>
    </div>
</div>

<!-- Popup de edição -->
<div id="editPopup" class="popup-overlay">
    <div class="popup">
        <h3>Editar Empresa</h3>
        
        <div class="popup-tabs">
            <div class="popup-tab active" onclick="switchTab('info')">Informações</div>
            <div class="popup-tab" onclick="switchTab('actions')">Ações</div>
        </div>
        
        <div id="info-tab" class="popup-tab-content active">
            <form id="editForm" action="editar-empresa.php" method="POST">
                <input type="hidden" id="edit_id" name="codigo_empresa">
                
                <div class="form-group">
                    <label for="edit_nome">Nome da Empresa:</label>
                    <input type="text" id="edit_nome" name="nome_empresa" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_fantasia">Nome Fantasia:</label>
                    <input type="text" id="edit_fantasia" name="fantasia_empresa" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_cnpj">CNPJ:</label>
                    <input type="text" id="edit_cnpj" name="cnpj" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_contrato">Contrato:</label>
                    <input type="text" id="edit_contrato" name="contrato" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_regiao">Região:</label>
                    <input type="text" id="edit_regiao" name="regiao">
                </div>
                
                <div class="form-group">
                    <label for="edit_contratos">Contratos:</label>
                    <textarea id="edit_contratos" name="contratos" rows="4"></textarea>
                </div>
                
                <div class="form-buttons">
                    <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Cancelar</button>
                    <button type="submit" class="btn-modern btn-confirm">Salvar</button>
                </div>
            </form>
        </div>
        
        <div id="actions-tab" class="popup-tab-content">
            <div class="actions-section">
                <h4>Ações da Empresa</h4>
                <p>Selecione uma ação para executar na empresa <strong id="empresa-nome-acoes"></strong>:</p>
                
                <div class="action-buttons">
                    <button class="btn-modern btn-warning" onclick="inativarEmpresaFromPopup()">
                        <i class="fas fa-pause"></i> Inativar Empresa
                    </button>
                    <button class="btn-modern btn-confirm" onclick="ativarEmpresaFromPopup()" id="btn-ativar-popup" style="display: none;">
                        <i class="fas fa-play"></i> Ativar Empresa
                    </button>
                    <button class="btn-modern btn-danger" onclick="excluirEmpresaFromPopup()">
                        <i class="fas fa-trash"></i> Excluir Empresa
                    </button>
                </div>
            </div>
            
            <div class="form-buttons">
                <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Formulário oculto para ações -->
<form id="actionForm" method="POST" action="tabela-empresas.php" style="display: none;">
    <input type="hidden" id="delete_id" name="delete_id">
    <input type="hidden" id="inativar_id" name="inativar_id">
    <input type="hidden" id="status" name="status">
</form>

<!-- Overlay e Popup de Confirmação -->
<div class="confirm-overlay" id="confirmOverlay">
    <div class="confirm-popup" id="confirmPopup">
        <div class="confirm-title" id="confirmTitle"></div>
        <div class="confirm-message" id="confirmMessage"></div>
        <div class="confirm-actions">
            <button class="btn-confirm" id="btnConfirmYes">Confirmar</button>
            <button class="btn-cancel" id="btnConfirmNo">Cancelar</button>
        </div>
    </div>
</div>

<!-- Bibliotecas para exportação -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.7.0/jspdf.plugin.autotable.min.js"></script>

<script>
    let currentEmpresa = null;
    let currentSort = { column: null, direction: 'asc' };
    
    // Função para mostrar popup de feedback
    function showFeedback(message, type = 'success') {
        const popup = document.getElementById('feedbackPopup');
        popup.textContent = message;
        popup.className = `feedback-popup ${type}`;
        popup.classList.add('show');
        
        setTimeout(() => {
            popup.classList.remove('show');
        }, 5000);
    }
    
    // Verificar se há mensagem de feedback na URL
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    if (status) {
        let message = '';
        let type = 'success';
        
        switch(status) {
            case 'edit-success':
                message = 'Edição realizada com sucesso!';
                type = 'success'; // verde
                break;
            case 'edit-error':
                message = 'Erro ao editar o registro.';
                type = 'error'; // vermelho
                break;
            case 'delete-success':
                message = 'Exclusão realizada com sucesso!';
                type = 'error'; // vermelho
                break;
            case 'success':
                message = urlParams.get('message') || 'Operação realizada com sucesso!';
                type = 'warning'; // amarelo para ativar/inativar
                break;
            case 'error':
                message = urlParams.get('message') || 'Erro ao realizar a operação.';
                type = 'error'; // vermelho
                break;
        }
        
        if (message) {
            showFeedback(message, type);
        }
    }
    
    // Fechar dropdown quando clicar fora
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('.main-dropdown');
        const dropdownMenu = document.getElementById('mainExportDropdown');
        
        if (!dropdown.contains(event.target)) {
            dropdownMenu.classList.remove('show');
        }
    });

    // --- Popup de Confirmação Global ---
    let confirmAction = null;
    function showConfirmPopup({title, message, onConfirm, type = 'default'}) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        const popup = document.getElementById('confirmPopup');
        popup.classList.remove('btn-danger', 'btn-warning');
        if (type === 'danger') {
            document.getElementById('btnConfirmYes').classList.add('btn-danger');
            document.getElementById('btnConfirmYes').classList.remove('btn-warning');
        } else if (type === 'warning') {
            document.getElementById('btnConfirmYes').classList.add('btn-warning');
            document.getElementById('btnConfirmYes').classList.remove('btn-danger');
        } else {
            document.getElementById('btnConfirmYes').classList.remove('btn-danger', 'btn-warning');
        }
        document.getElementById('confirmOverlay').style.display = 'flex';
        confirmAction = onConfirm;
    }
    document.getElementById('btnConfirmYes').onclick = function() {
        document.getElementById('confirmOverlay').style.display = 'none';
        if (typeof confirmAction === 'function') confirmAction();
    };
    document.getElementById('btnConfirmNo').onclick = function() {
        document.getElementById('confirmOverlay').style.display = 'none';
        confirmAction = null;
    };

    // Substituir ações por popups de confirmação
    function excluirEmpresa(id, nome) {
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir a empresa "${nome}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = id;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarEmpresa(id) {
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar esta empresa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = id;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarEmpresa(id) {
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar esta empresa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = id;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function excluirEmpresaFromPopup() {
        if (!currentEmpresa) return;
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir a empresa "${currentEmpresa.nome_empresa}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = currentEmpresa.codigo_empresa;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarEmpresaFromPopup() {
        if (!currentEmpresa) return;
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar esta empresa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentEmpresa.codigo_empresa;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarEmpresaFromPopup() {
        if (!currentEmpresa) return;
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar esta empresa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentEmpresa.codigo_empresa;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    // Confirmação ao salvar edição
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.onsubmit = function(e) {
            e.preventDefault();
            showConfirmPopup({
                title: 'Confirmar Edição',
                message: 'Deseja salvar as alterações desta empresa?',
                type: 'confirm',
                onConfirm: function() {
                    editForm.onsubmit = null;
                    editForm.submit();
                }
            });
            return false;
        };
    }

    function openEditPopup(rowData) {
        currentEmpresa = rowData;
        
        document.getElementById('edit_id').value = rowData.codigo_empresa;
        document.getElementById('edit_nome').value = rowData.nome_empresa;
        document.getElementById('edit_fantasia').value = rowData.fantasia_empresa;
        document.getElementById('edit_cnpj').value = rowData.cnpj;
        document.getElementById('edit_contrato').value = rowData.contrato;
        document.getElementById('edit_regiao').value = rowData.regiao;
        document.getElementById('edit_contratos').value = rowData.contratos;
        
        // Atualizar nome da empresa na aba de ações
        document.getElementById('empresa-nome-acoes').textContent = rowData.nome_empresa;
        
        // Configurar botões de ativar/inativar baseado no status
        const status = rowData.status || 'ativo';
        if (status === 'ativo') {
            document.querySelector('[onclick="inativarEmpresaFromPopup()"]').style.display = 'inline-flex';
            document.getElementById('btn-ativar-popup').style.display = 'none';
        } else {
            document.querySelector('[onclick="inativarEmpresaFromPopup()"]').style.display = 'none';
            document.getElementById('btn-ativar-popup').style.display = 'inline-flex';
        }
        
        document.getElementById('editPopup').style.display = 'flex';
    }

    function closeEditPopup() {
        document.getElementById('editPopup').style.display = 'none';
        currentEmpresa = null;
    }

    function switchTab(tabName) {
        // Desativar todas as abas
        document.querySelectorAll('.popup-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.popup-tab-content').forEach(content => content.classList.remove('active'));
        
        // Ativar a aba selecionada
        document.querySelector(`.popup-tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    function toggleMainDropdown() {
        document.getElementById('mainExportDropdown').classList.toggle('show');
    }

    // Função para ordenar a tabela
    function sortTable(column) {
        const table = document.getElementById('empresasTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        // Determinar direção da ordenação
        if (currentSort.column === column) {
            currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.column = column;
            currentSort.direction = 'asc';
        }
        
        // Remover todos os sort-icons
        document.querySelectorAll('.sortable .sort-icon').forEach(icon => icon.remove());
        
        // Adicionar sort-icon apenas na coluna ordenada
        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        const icon = document.createElement('span');
        icon.className = `sort-icon active ${currentSort.direction}`;
        currentHeader.appendChild(icon);
        
        // Ordenar as linhas
        rows.sort((a, b) => {
            let aValue = a.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            let bValue = b.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            
            // Converter para número se for numérico
            if (column === 'codigo_empresa') {
                aValue = parseFloat(aValue) || 0;
                bValue = parseFloat(bValue) || 0;
            }
            
            if (currentSort.direction === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        
        // Reordenar as linhas na tabela
        rows.forEach(row => tbody.appendChild(row));
    }
    
    function getColumnIndex(column) {
        const columnMap = {
            'codigo_empresa': 1,
            'nome_empresa': 2,
            'fantasia_empresa': 3,
            'cnpj': 4,
            'contrato': 5,
            'regiao': 6,
            'status': 7
        };
        return columnMap[column];
    }
    
    // Adicionar listeners para ordenação
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                sortTable(column);
            });
        });
    });

    function exportToExcel() {
        const table = document.getElementById('empresasTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const wb = XLSX.utils.table_to_book(clonedTable, {sheet: "Empresas"});
        XLSX.writeFile(wb, 'empresas.xlsx');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    function exportToPDF() {
        const table = document.getElementById('empresasTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const doc = new jspdf.jsPDF('l', 'pt', 'a4');
        doc.autoTable({ html: clonedTable });
        doc.save('empresas.pdf');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    const searchVisible = document.getElementById('searchVisible');
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('search');
    const searchVisibleBtn = document.getElementById('searchVisibleBtn');
    searchVisible.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchInput.value = searchVisible.value;
            searchForm.submit();
        }
    });
    searchVisibleBtn.addEventListener('click', function() {
        searchInput.value = searchVisible.value;
        searchForm.submit();
    });
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const filterPopupOverlay = document.getElementById('filterPopupOverlay');
    const closeFilterPopup = document.getElementById('closeFilterPopup');
    filterBtn.addEventListener('click', function(e) {
        filterPopup.style.display = 'block';
        filterPopupOverlay.style.display = 'block';
        e.stopPropagation();
    });
    closeFilterPopup.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
    filterPopupOverlay.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
</script>
</body>
</html>

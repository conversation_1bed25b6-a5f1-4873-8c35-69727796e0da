<?php
include 'conexao.php';

// Verificar se o código foi fornecido
if (!isset($_GET['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Código não fornecido']);
    exit;
}

$codigo = $_GET['codigo'];

// Consultar o pedido especial
$stmt = $conn->prepare("SELECT p.*, e.nome_empresa FROM pedidos_especiais p LEFT JOIN empresas e ON p.empresa = e.codigo_empresa WHERE p.codigo_pedido = ?");
$stmt->bind_param("i", $codigo);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Pedido especial não encontrado']);
    exit;
}

$pedido = $result->fetch_assoc();

// Se não existir a coluna status, definir como pendente
if (!isset($pedido['status'])) {
    $pedido['status'] = 'pendente';
}

// Adicionar campo explícito para código da empresa
$pedido['codigo_empresa'] = $pedido['empresa'];

echo json_encode(['success' => true, 'pedido' => $pedido]);
?>


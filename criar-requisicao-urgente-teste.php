<?php
include 'conexao.php';

echo "<h2><PERSON><PERSON><PERSON> Urgente para Teste de Interface</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Usuario Teste Interface', '001', 'TESTE-002', 'Teste de exclusão via interface', '1', 'Teste', 'TI', 'Requisição de teste para exclusão via interface', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✓ Requisição urgente criada com código: $codigo_requisicao</p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'PROD002', 3)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✓ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<h3>Requisição criada com sucesso!</h3>";
    echo "<p>Código da requisição: <strong>$codigo_requisicao</strong></p>";
    echo "<p>Esta requisição deve aparecer com fundo vermelho claro na lista de solicitações.</p>";
    echo "<p>Ao clicar nela, deve mostrar o aviso de pedido urgente e o botão para excluir.</p>";
    
} else {
    echo "<p style='color: red;'>✗ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p><a href='todas-solitacoes-estoque.php'>Ir para Todas as Solicitações</a></p>";

$conn->close();
?>

<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json; charset=utf-8');

session_start();
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Se for só atualização de status
    if (isset($_POST['id'], $_POST['status']) && count($_POST) == 2) {
        $id = intval($_POST['id']);
        $status = $_POST['status'];
        $sql = "UPDATE limites_gastos SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'Erro ao preparar consulta: ' . $conn->error]);
            exit();
        }
        $stmt->bind_param("si", $status, $id);
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Status atualizado com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao atualizar status: ' . $stmt->error]);
        }
        $stmt->close();
        $conn->close();
        exit();
    }

    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $valor_limite = isset($_POST['valor_limite']) ? floatval($_POST['valor_limite']) : null;
    $periodicidade = isset($_POST['periodicidade']) ? intval($_POST['periodicidade']) : null;
    $saldo_aviso = isset($_POST['saldo_aviso']) ? floatval($_POST['saldo_aviso']) : null;

    if (!$id || $valor_limite === null || $periodicidade === null || $saldo_aviso === null) {
        echo json_encode(['success' => false, 'message' => 'Todos os campos são obrigatórios']);
        exit();
    }

    if ($valor_limite <= 0) {
        echo json_encode(['success' => false, 'message' => 'Valor limite deve ser maior que zero']);
        exit();
    }

    $sql = "UPDATE limites_gastos SET valor_limite = ?, periodicidade = ?, saldo_aviso = ?, atualizado_em = NOW() WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Erro ao preparar consulta: ' . $conn->error]);
        exit();
    }
    $stmt->bind_param("disi", $valor_limite, $periodicidade, $saldo_aviso, $id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Limite atualizado com sucesso']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao atualizar limite: ' . $stmt->error]);
    }

    $stmt->close();
    $conn->close();
    exit();
} else {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}
?> 
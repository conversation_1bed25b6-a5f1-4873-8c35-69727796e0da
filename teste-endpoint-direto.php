<?php
include 'conexao.php';

// Buscar uma requisição urgente
$sql = "SELECT codigo_solicitacao FROM requisicoes WHERE requisicao_urgente = 1 ORDER BY codigo_solicitacao DESC LIMIT 1";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $codigo = $row['codigo_solicitacao'];
    
    echo "<h2>Teste Direto do Endpoint</h2>";
    echo "<p><strong>Código da requisição urgente:</strong> $codigo</p>";
    
    // Simular exatamente o que o endpoint faz
    $stmt = $conn->prepare("SELECT r.*, e.nome_empresa FROM requisicoes r LEFT JOIN empresas e ON r.empresa = e.codigo_empresa WHERE r.codigo_solicitacao = ?");
    $stmt->bind_param("i", $codigo);
    $stmt->execute();
    $result_req = $stmt->get_result();
    
    if ($result_req->num_rows > 0) {
        $requisicao = $result_req->fetch_assoc();
        
        // Buscar itens
        $stmt_itens = $conn->prepare("
            SELECT i.*, p.nome as nome_produto 
            FROM itens_solicitacao i
            LEFT JOIN produtos p ON i.produto = p.codigo
            WHERE i.codigo_solicitacao = ?
        ");
        $stmt_itens->bind_param("i", $codigo);
        $stmt_itens->execute();
        $result_itens = $stmt_itens->get_result();
        
        $itens = [];
        while ($item = $result_itens->fetch_assoc()) {
            $itens[] = $item;
        }
        
        // Montar resposta igual ao endpoint
        $response = [
            'success' => true,
            'requisicao' => $requisicao,
            'itens' => $itens
        ];
        
        echo "<h3>Resposta JSON do Endpoint:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; max-height: 400px;'>";
        echo json_encode($response, JSON_PRETTY_PRINT);
        echo "</pre>";
        
        echo "<h3>Verificações JavaScript:</h3>";
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>Campo requisicao_urgente:</strong> " . ($requisicao['requisicao_urgente'] ?? 'NULL') . "</p>";
        echo "<p><strong>data.requisicao.requisicao_urgente:</strong> " . ($requisicao['requisicao_urgente'] ?? 'NULL') . "</p>";
        echo "<p><strong>Condição (== 1):</strong> " . (($requisicao['requisicao_urgente'] ?? 0) == 1 ? 'TRUE' : 'FALSE') . "</p>";
        echo "</div>";
        
        echo "<h3>Teste JavaScript:</h3>";
        echo "<script>";
        echo "const testData = " . json_encode($response) . ";";
        echo "console.log('=== TESTE DIRETO ===');";
        echo "console.log('Dados:', testData);";
        echo "console.log('testData.requisicao.requisicao_urgente:', testData.requisicao.requisicao_urgente);";
        echo "console.log('testData.requisicao.requisicao_urgente == 1:', testData.requisicao.requisicao_urgente == 1);";
        echo "console.log('===================');";
        echo "</script>";
        
    } else {
        echo "<p style='color: red;'>Erro: Requisição não encontrada</p>";
    }
    
} else {
    echo "<h2>Nenhuma requisição urgente encontrada</h2>";
    echo "<p>Criando uma requisição urgente de teste...</p>";
    
    $sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
            VALUES ('Teste Endpoint', '001', 'ENDPOINT', 'Teste endpoint direto', '1', 'Teste', 'TI', 'Teste', 1, 'Limite excedido', NOW())";
    
    if ($conn->query($sql)) {
        $codigo_novo = $conn->insert_id;
        echo "<p style='color: green;'>✅ Requisição criada com código: $codigo_novo</p>";
        echo "<p><a href='teste-endpoint-direto.php'>Recarregar para testar</a></p>";
    }
}

echo "<p style='margin-top: 30px;'>";
echo "<a href='requisicoes-feitas.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Voltar para Requisições Feitas</a>";
echo "</p>";

$conn->close();
?>

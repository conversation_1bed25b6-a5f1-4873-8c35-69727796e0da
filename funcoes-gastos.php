<?php
function registrarGasto($conn, $empresa, $setor, $valor_total, $tipo_operacao, $referencia_id) {
    $data_atual = date('Y-m-d');

    // Verificar se já existe um registro para esta operação (evitar duplicatas)
    $sql_check = "SELECT id FROM gastos_realizados WHERE tipo_operacao = ? AND referencia_id = ?";
    $stmt_check = $conn->prepare($sql_check);
    $stmt_check->bind_param("si", $tipo_operacao, $referencia_id);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows > 0) {
        // Já existe um registro para esta operação, não duplicar
        $stmt_check->close();
        return false;
    }
    $stmt_check->close();

    $limite_id = null;
    $registrado = false;

    // Prioridade 1: Buscar limite de empresa
    $sql_empresa = "SELECT lg.id FROM limites_gastos lg
                    LEFT JOIN empresas e ON lg.id_empresa = e.codigo_empresa
                    WHERE lg.tipo = 'empresa' AND e.nome_empresa = ? AND lg.status = 'ativo'";
    $stmt_empresa = $conn->prepare($sql_empresa);
    $stmt_empresa->bind_param("s", $empresa);
    $stmt_empresa->execute();
    $result_empresa = $stmt_empresa->get_result();

    if ($result_empresa->num_rows > 0) {
        $limite_empresa = $result_empresa->fetch_assoc();
        $limite_id = $limite_empresa['id'];
        $registrado = true;
    }
    $stmt_empresa->close();

    // Prioridade 2: Se não encontrou limite de empresa, buscar limite de setor
    if (!$registrado && $setor) {
        $sql_setor = "SELECT lg.id FROM limites_gastos lg
                      WHERE lg.tipo = 'setor' AND lg.id_setor = ? AND lg.status = 'ativo'";
        $stmt_setor = $conn->prepare($sql_setor);
        $stmt_setor->bind_param("s", $setor);
        $stmt_setor->execute();
        $result_setor = $stmt_setor->get_result();

        if ($result_setor->num_rows > 0) {
            $limite_setor = $result_setor->fetch_assoc();
            $limite_id = $limite_setor['id'];
            $registrado = true;
        }
        $stmt_setor->close();
    }

    // Registrar o gasto apenas uma vez
    if ($registrado && $limite_id) {
        $sql_insert = "INSERT INTO gastos_realizados (limite_id, tipo_operacao, empresa, setor, valor_total, data_operacao, referencia_id)
                       VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt_insert = $conn->prepare($sql_insert);
        $stmt_insert->bind_param("isssdsi", $limite_id, $tipo_operacao, $empresa, $setor, $valor_total, $data_atual, $referencia_id);

        if ($stmt_insert->execute()) {
            $stmt_insert->close();
            return true;
        }
        $stmt_insert->close();
    }

    return false;
}
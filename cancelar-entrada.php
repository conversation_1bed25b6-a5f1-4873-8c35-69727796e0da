<?php
include 'conexao.php';

// Receber dados do POST
$data = json_decode(file_get_contents("php://input"), true);
$entrada_id = $data['entrada_id'];

// Iniciar transação para garantir integridade dos dados
$conn->begin_transaction();

try {
    // Verificar se a entrada existe
    $stmt = $conn->prepare("SELECT * FROM entradas_estoque WHERE id = ?");
    $stmt->bind_param("i", $entrada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("Entrada não encontrada");
    }
    
    $entrada = $result->fetch_assoc();
    if (isset($entrada['status']) && $entrada['status'] === 'cancelada') {
        throw new Exception("Esta entrada já foi cancelada");
    }
    
    // Obter produtos da entrada
    $stmt = $conn->prepare("SELECT * FROM produtos_entrada WHERE entrada_id = ?");
    $stmt->bind_param("i", $entrada_id);
    $stmt->execute();
    $produtos = $stmt->get_result();
    
    // Atualizar estoque para cada produto
    while ($produto = $produtos->fetch_assoc()) {
        // Subtrair a quantidade que foi adicionada anteriormente
        $stmt = $conn->prepare("UPDATE produtos SET quantidade = quantidade - ? WHERE codigo = ?");
        $stmt->bind_param("is", $produto['quantidade'], $produto['codigo']);
        $stmt->execute();
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("Erro ao atualizar estoque do produto " . $produto['codigo']);
        }
    }
    
    // Marcar entrada como cancelada
    $stmt = $conn->prepare("UPDATE entradas_estoque SET status = 'cancelada' WHERE id = ?");
    $stmt->bind_param("i", $entrada_id);
    $stmt->execute();
    
    if ($stmt->affected_rows === 0) {
        throw new Exception("Erro ao atualizar status da entrada");
    }
    
    // Commit da transação
    $conn->commit();
    
    // Retornar sucesso
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>

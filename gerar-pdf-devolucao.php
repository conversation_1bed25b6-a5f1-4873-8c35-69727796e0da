<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

require_once 'conexao.php';

if (!isset($_GET['id']) || !isset($_GET['tipo'])) {
    die('Parâmetros inválidos');
}

$id = intval($_GET['id']);
$tipo = $_GET['tipo'];

try {
    // Buscar dados da devolução
    if ($tipo === 'funcionario') {
        $sql = "
            SELECT 
                d.*,
                u.nome as usuario_nome,
                p.posto,
                p.setor,
                p.funcao
            FROM devolucoes_epi d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            LEFT JOIN pessoas p ON d.pessoa_id = p.id
            WHERE d.id = ?
        ";
    } else {
        $sql = "
            SELECT 
                d.*,
                u.nome as usuario_nome,
                NULL as posto,
                NULL as setor,
                NULL as funcao
            FROM devolucoes_rapidas d
            LEFT JOIN usuarios u ON d.usuario_id = u.id
            WHERE d.id = ?
        ";
    }
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if (!$result || $result->num_rows === 0) {
        die('Devolução não encontrada');
    }
    
    $devolucao = $result->fetch_assoc();
    
    // Buscar informações do produto
    $sql_produto = "SELECT * FROM produtos WHERE codigo = ?";
    $stmt_produto = $conn->prepare($sql_produto);
    $stmt_produto->bind_param("s", $devolucao['produto_id']);
    $stmt_produto->execute();
    $result_produto = $stmt_produto->get_result();
    $produto = $result_produto->fetch_assoc();
    
} catch (Exception $e) {
    die('Erro ao buscar dados: ' . $e->getMessage());
}

// Configurar cabeçalhos para PDF
header('Content-Type: application/pdf');
header('Content-Disposition: inline; filename="devolucao_' . $id . '_' . date('Y-m-d') . '.pdf"');

// Função para formatar estado
function formatarEstado($estado) {
    $estados = [
        'novo' => 'Novo / Sem Uso',
        'usado_bom' => 'Usado – Bom Estado',
        'usado_leve' => 'Usado – Desgaste leve',
        'danificado_reparo' => 'Danificado – Reparo Possível',
        'danificado_irrecuperavel' => 'Danificado – Irrecuperável',
        'vencido' => 'Vencido / Fora da Validade',
        'descarte' => 'Descarte'
    ];
    return $estados[$estado] ?? $estado;
}

// Gerar HTML para conversão em PDF
$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comprovante de Devolução</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .company-name { font-size: 24px; font-weight: bold; color: #007cba; }
        .document-title { font-size: 18px; margin-top: 10px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .info-section { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .info-section h3 { margin: 0 0 10px 0; color: #007cba; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        .info-row { margin: 8px 0; }
        .info-label { font-weight: bold; display: inline-block; width: 120px; }
        .signature-section { margin-top: 40px; border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
        .signature-box { border: 1px solid #ccc; height: 100px; margin: 10px 0; text-align: center; line-height: 100px; color: #666; }
        .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">SISTEMA DE CONTROLE DE EPIs</div>
        <div class="document-title">COMPROVANTE DE DEVOLUÇÃO</div>
        <div style="margin-top: 10px; font-size: 14px;">
            Documento Nº: ' . str_pad($id, 6, '0', STR_PAD_LEFT) . ' | 
            Data: ' . date('d/m/Y H:i', strtotime($devolucao['data_devolucao'])) . '
        </div>
    </div>

    <div class="info-grid">
        <div class="info-section">
            <h3>Informações da Devolução</h3>
            <div class="info-row">
                <span class="info-label">Tipo:</span>
                ' . ($tipo === 'funcionario' ? 'Devolução por Funcionário' : 'Devolução Rápida') . '
            </div>
            <div class="info-row">
                <span class="info-label">Data/Hora:</span>
                ' . date('d/m/Y H:i:s', strtotime($devolucao['data_devolucao'])) . '
            </div>
            <div class="info-row">
                <span class="info-label">Usuário:</span>
                ' . ($devolucao['usuario_nome'] ?? 'N/A') . '
            </div>
            ' . ($devolucao['pessoa_nome'] ? '
            <div class="info-row">
                <span class="info-label">Funcionário:</span>
                ' . $devolucao['pessoa_nome'] . '
            </div>
            ' . ($devolucao['posto'] ? '<div class="info-row"><span class="info-label">Posto:</span> ' . $devolucao['posto'] . '</div>' : '') . '
            ' . ($devolucao['setor'] ? '<div class="info-row"><span class="info-label">Setor:</span> ' . $devolucao['setor'] . '</div>' : '') . '
            ' : '') . '
        </div>

        <div class="info-section">
            <h3>Produto Devolvido</h3>
            <div class="info-row">
                <span class="info-label">Código:</span>
                ' . $devolucao['produto_id'] . '
            </div>
            <div class="info-row">
                <span class="info-label">Nome:</span>
                ' . $devolucao['produto_nome'] . '
            </div>
            ' . ($produto && $produto['categoria'] ? '
            <div class="info-row">
                <span class="info-label">Categoria:</span>
                ' . $produto['categoria'] . '
            </div>
            ' : '') . '
            ' . ($produto && $produto['ca'] ? '
            <div class="info-row">
                <span class="info-label">C.A:</span>
                ' . $produto['ca'] . '
            </div>
            ' : '') . '
            <div class="info-row">
                <span class="info-label">Quantidade:</span>
                ' . $devolucao['quantidade'] . '
            </div>
            <div class="info-row">
                <span class="info-label">Estado:</span>
                ' . formatarEstado($devolucao['estado']) . '
            </div>
        </div>
    </div>

    <div class="signature-section">
        <h3>Assinatura de Devolução</h3>';

if (!empty($devolucao['assinatura'])) {
    $html .= '<div style="text-align: center; margin: 20px 0;">
                <img src="data:image/png;base64,' . $devolucao['assinatura'] . '" style="max-width: 400px; border: 1px solid #ddd;">
              </div>';
} else {
    $html .= '<div class="signature-box">Assinatura não registrada</div>';
}

$html .= '
        <div style="margin-top: 20px; text-align: center;">
            <div style="border-top: 1px solid #333; width: 300px; margin: 0 auto; padding-top: 5px;">
                Assinatura do Responsável
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Este documento foi gerado automaticamente pelo Sistema de Controle de EPIs</p>
        <p>Data de geração: ' . date('d/m/Y H:i:s') . '</p>
    </div>
</body>
</html>';

// Para uma implementação simples, vamos usar DomPDF ou similar
// Por enquanto, vamos retornar o HTML que pode ser impresso
header('Content-Type: text/html; charset=UTF-8');
echo $html;

$conn->close();
?>

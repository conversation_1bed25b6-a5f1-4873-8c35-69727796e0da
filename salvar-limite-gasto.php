<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tipo = $_POST['tipo'] ?? '';
    $id_empresa = $_POST['id_empresa'] ?? null;
    $id_setor = $_POST['id_setor'] ?? null;
    $valor_limite = $_POST['valor_limite'] ?? 0;
    $periodicidade = $_POST['periodicidade'] ?? '';
    $saldo_aviso = $_POST['saldo_aviso'] ?? 0;
    
    // Validações
    if (empty($tipo) || empty($valor_limite) || empty($periodicidade)) {
        echo json_encode(['success' => false, 'message' => 'Todos os campos são obrigatórios']);
        exit();
    }
    
    if ($valor_limite <= 0) {
        echo json_encode(['success' => false, 'message' => 'Valor limite deve ser maior que zero']);
        exit();
    }
    
    if ($tipo === 'empresa' && empty($id_empresa)) {
        echo json_encode(['success' => false, 'message' => 'Selecione uma empresa']);
        exit();
    }
    
    if ($tipo === 'setor' && empty($id_setor)) {
        echo json_encode(['success' => false, 'message' => 'Selecione um setor']);
        exit();
    }
    
    // Validar se a empresa/setor existe (para evitar erro de foreign key)
    if ($tipo === 'empresa') {
        $stmt_exists = $conn->prepare("SELECT codigo_empresa FROM empresas WHERE codigo_empresa = ?");
        $stmt_exists->bind_param("s", $id_empresa);
        $stmt_exists->execute();
        $result_exists = $stmt_exists->get_result();

        if ($result_exists->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Empresa não encontrada. Código: ' . $id_empresa]);
            exit();
        }
        $stmt_exists->close();
    } else {
        // Verificar se existe tabela setores
        $result_table = $conn->query("SHOW TABLES LIKE 'setores'");
        if ($result_table->num_rows > 0) {
            $stmt_exists = $conn->prepare("SELECT id FROM setores WHERE id = ?");
            $stmt_exists->bind_param("i", $id_setor);
            $stmt_exists->execute();
            $result_exists = $stmt_exists->get_result();

            if ($result_exists->num_rows === 0) {
                echo json_encode(['success' => false, 'message' => 'Setor não encontrado. ID: ' . $id_setor]);
                exit();
            }
            $stmt_exists->close();
        }
    }

    // Verificar se já existe um limite para esta empresa/setor
    if ($tipo === 'empresa') {
        $sql_check = "SELECT id FROM limites_gastos WHERE tipo = 'empresa' AND id_empresa = ?";
        $stmt_check = $conn->prepare($sql_check);
        $stmt_check->bind_param("s", $id_empresa);
    } else {
        $sql_check = "SELECT id FROM limites_gastos WHERE tipo = 'setor' AND id_setor = ?";
        $stmt_check = $conn->prepare($sql_check);
        $stmt_check->bind_param("i", $id_setor);
    }

    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Já existe um limite para este ' . $tipo]);
        exit();
    }
    $stmt_check->close();
    
    // Inserir novo limite
    try {
        // Preparar valores para inserção
        $empresa_valor = ($tipo === 'empresa') ? $id_empresa : null;
        $setor_valor = ($tipo === 'setor') ? $id_setor : null;

        $sql = "INSERT INTO limites_gastos (tipo, id_empresa, id_setor, valor_limite, periodicidade, saldo_aviso, criado_em, atualizado_em)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
        $stmt = $conn->prepare($sql);

        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'Erro ao preparar consulta: ' . $conn->error]);
            exit();
        }

        // Bind com tipos corretos
        $stmt->bind_param("ssidsd", $tipo, $empresa_valor, $setor_valor, $valor_limite, $periodicidade, $saldo_aviso);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Limite salvo com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao salvar limite: ' . $stmt->error]);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Erro na inserção: ' . $e->getMessage()]);
        if (isset($stmt) && $stmt) {
            $stmt->close();
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
}

$conn->close();
?> 
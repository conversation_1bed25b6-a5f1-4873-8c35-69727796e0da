<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Obter o próximo código de pedido automaticamente
$result = $conn->query("SELECT MAX(codigo_pedido) AS max_codigo FROM pedidos_mensais");
$row = $result->fetch_assoc();
$codigo_pedido = $row['max_codigo'] + 1;
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pedido Mensal</title>
    <link rel="stylesheet" href="style.css">
    <script>
        function adicionarProduto() {
            const container = document.getElementById('itens-container');
            const div = document.createElement('div');
            div.classList.add('item');
            div.innerHTML = `
                <input type="number" name="quantidade[]" placeholder="Quantidade" required>
                <input type="text" name="produto[]" placeholder="Produto" required>
                <button type="button" class="search-btn" onclick="abrirPopupProdutos(this); event.preventDefault();" title="Pesquisar Produto">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                        <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                    </svg>
                </button>
                <button type="button" class="remove-btn" onclick="removerProduto(this)" title="Remover Produto">
                    <svg class="remove-icon" viewBox="0 0 20 20">
                        <line x1="5" y1="5" x2="15" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                        <line x1="15" y1="5" x2="5" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </button>
            `;
            container.appendChild(div);
        }
        
        function removerProduto(botao) {
            botao.parentElement.remove();
        }

        function abrirPopupEmpresas() {
            document.getElementById("popupEmpresasOverlay").classList.add("active");
        }

        function fecharPopupEmpresas() {
            document.getElementById("popupEmpresasOverlay").classList.remove("active");
        }

        function selecionarEmpresa(codigo, nome, contrato) {
            document.getElementById("empresaSelecionada").value = codigo;
            document.getElementById("empresa").value = nome;
            document.getElementById("contrato").value = contrato;
            fecharPopupEmpresas();
        }

        function filtrarEmpresas() {
            const termo = document.getElementById("pesquisaEmpresa").value.toLowerCase();
            const linhas = document.querySelectorAll("#listaEmpresas tr");
            linhas.forEach(tr => {
                const nome = tr.children[1].textContent.toLowerCase();
                tr.style.display = nome.includes(termo) ? "" : "none";
            });
        }

        function abrirPopupDestinatarios() {
            // Verificar se uma empresa foi selecionada
            const empresaCodigo = document.getElementById("empresaSelecionada").value;

            if (!empresaCodigo) {
                alert("Por favor, selecione uma empresa primeiro!");
                return;
            }

            // Carregar destinatários da empresa selecionada
            carregarDestinatariosPorEmpresa(empresaCodigo);

            document.getElementById("popupDestinatariosOverlay").classList.add("active");
        }

        function fecharPopupDestinatarios() {
            document.getElementById("popupDestinatariosOverlay").classList.remove("active");
        }

        // Função para carregar destinatários por empresa
        function carregarDestinatariosPorEmpresa(empresaCodigo) {
            const listaDestinatarios = document.getElementById("listaDestinatarios");

            // Mostrar loading
            listaDestinatarios.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px;">Carregando destinatários...</td></tr>';

            // Fazer requisição AJAX
            fetch(`buscar-funcionarios-por-empresa.php?empresa_codigo=${encodeURIComponent(empresaCodigo)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.funcionarios.length > 0) {
                            let html = '';
                            data.funcionarios.forEach(funcionario => {
                                html += `
                                    <tr>
                                        <td>${funcionario.id}</td>
                                        <td>${funcionario.nome}</td>
                                        <td>${funcionario.cargo}</td>
                                        <td><button onclick="selecionarDestinatario('${funcionario.id}', '${funcionario.nome}', '${funcionario.cargo}', '${funcionario.setor}')">Selecionar</button></td>
                                    </tr>
                                `;
                            });
                            listaDestinatarios.innerHTML = html;

                            // Atualizar título do popup para mostrar a empresa
                            const popupHeader = document.querySelector("#popupDestinatariosOverlay .popup-header h3");
                            popupHeader.textContent = `Destinatários - ${data.empresa.nome}`;
                        } else {
                            listaDestinatarios.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#888;">Nenhum destinatário encontrado para esta empresa.</td></tr>';
                        }
                    } else {
                        listaDestinatarios.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#dc2626;">Erro ao carregar destinatários: ' + data.message + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    listaDestinatarios.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#dc2626;">Erro ao carregar destinatários.</td></tr>';
                });
        }

        function selecionarDestinatario(id, nome, funcao, setor) {
            document.getElementById("destinatario").value = nome;
            document.getElementById("id_destinatario").value = id;
            document.getElementById("funcao").value = funcao;
            if (setor) {
                document.getElementById("setor").value = setor;
            }
            fecharPopupDestinatarios();
        }

        function filtrarDestinatarios() {
            const termo = document.getElementById("pesquisaDestinatario").value.toLowerCase();
            const linhas = document.querySelectorAll("#listaDestinatarios tr");
            linhas.forEach(tr => {
                const nome = tr.children[1].textContent.toLowerCase();
                tr.style.display = nome.includes(termo) ? "" : "none";
            });
        }

        function abrirPopupProdutos(button) {
            const itemDiv = button.parentElement;
            const produtoInput = itemDiv.querySelector('input[name="produto[]"]');
            const quantidadeInput = itemDiv.querySelector('input[name="quantidade[]"]');

            document.getElementById("popupProdutosOverlay").classList.add("active");

            // Armazenar referência ao campo de produto
            window.selectedProdutoInput = produtoInput;
            window.selectedQuantidadeInput = quantidadeInput;
        }

        function fecharPopupProdutos() {
            document.getElementById("popupProdutosOverlay").classList.remove("active");
        }

        function selecionarProduto(codigo, nome) {
            if (window.selectedProdutoInput) {
                // Verificar se o produto já foi selecionado
                if (produtoJaSelecionado(codigo)) {
                    mostrarPopupAviso("Produto já inserido");
                    return;
                }

                // Criar um campo oculto para armazenar o código do produto
                const codigoProdutoInput = document.createElement('input');
                codigoProdutoInput.type = 'hidden';
                codigoProdutoInput.name = 'codigo_produto[]';
                codigoProdutoInput.value = codigo;
                window.selectedProdutoInput.parentNode.appendChild(codigoProdutoInput);

                // Mostrar o nome do produto no campo visível
                window.selectedProdutoInput.value = nome;
                window.selectedProdutoInput.setAttribute('readonly', true);
            }
            fecharPopupProdutos();
        }

        // Função para verificar se o produto já foi selecionado
        function produtoJaSelecionado(codigo) {
            const codigosInputs = document.querySelectorAll('input[name="codigo_produto[]"]');
            for (let input of codigosInputs) {
                if (input.value === codigo) {
                    return true;
                }
            }
            return false;
        }

        // Função para mostrar popup de aviso
        function mostrarPopupAviso(mensagem) {
            // Criar o popup se não existir
            let popup = document.getElementById('popup-aviso-produto');
            if (!popup) {
                popup = document.createElement('div');
                popup.id = 'popup-aviso-produto';
                popup.className = 'popup-aviso-produto';
                popup.innerHTML = `
                    <div class="popup-aviso-content">
                        <span class="popup-aviso-texto">${mensagem}</span>
                    </div>
                `;
                document.body.appendChild(popup);
            } else {
                popup.querySelector('.popup-aviso-texto').textContent = mensagem;
            }

            // Mostrar o popup
            popup.style.display = 'flex';

            // Esconder após 2 segundos
            setTimeout(() => {
                popup.style.display = 'none';
            }, 2000);
        }

        // Função para verificar limites antes do envio
        function verificarLimitesAntesEnvio(empresa, setor, valorTotal, callback) {
            fetch('verificar-limite-antes-envio.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    empresa: empresa,
                    setor: setor,
                    valor_total: valorTotal
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (!data.tem_limite) {
                        callback(true);
                    } else if (data.status === 'limite_excedido') {
                        mostrarPopupLimite(data.message, 'bloqueado', false, callback);
                    } else if (data.status === 'aviso') {
                        mostrarPopupLimite(data.message, 'aviso', true, callback);
                    } else {
                        callback(true);
                    }
                } else {
                    console.error('Erro ao verificar limite:', data.message);
                    callback(true);
                }
            })
            .catch(error => {
                console.error('Erro na requisição:', error);
                callback(true);
            });
        }

        // Função para mostrar popup de limite
        function mostrarPopupLimite(mensagem, tipo, podeConfirmar, callback) {
            let popup = document.getElementById('popup-limite-overlay');
            if (!popup) {
                popup = document.createElement('div');
                popup.id = 'popup-limite-overlay';
                popup.className = 'popup-limite-overlay';
                document.body.appendChild(popup);
            }

            const icone = tipo === 'aviso' ? '⚠️' : '🚫';
            let titulo, botoes;

            if (tipo === 'aviso') {
                titulo = 'Aviso de Limite';
                botoes = `
                    <button class="popup-limite-btn continuar" onclick="confirmarEnvioComLimite(true, false)">Continuar Mesmo Assim</button>
                    <button class="popup-limite-btn cancelar" onclick="confirmarEnvioComLimite(false, false)">Cancelar</button>
                `;
            } else {
                // Limite excedido - oferecer opção de pedido urgente
                titulo = 'Limite Excedido';
                botoes = `
                    <button class="popup-limite-btn continuar" onclick="confirmarEnvioComLimite(true, true)">Sim, Enviar como Urgente</button>
                    <button class="popup-limite-btn cancelar" onclick="confirmarEnvioComLimite(false, false)">Cancelar</button>
                `;
                mensagem = "Deseja enviar como pedido urgente?";
            }

            popup.innerHTML = `
                <div class="popup-limite-content">
                    <div class="popup-limite-icon ${tipo}">${icone}</div>
                    <div class="popup-limite-title">${titulo}</div>
                    <div class="popup-limite-message">${mensagem}</div>
                    <div class="popup-limite-buttons">
                        ${botoes}
                    </div>
                </div>
            `;

            popup.style.display = 'flex';
            window.limiteCallback = callback;
        }

        // Função para confirmar envio com limite
        function confirmarEnvioComLimite(prosseguir, isUrgente = false) {
            document.getElementById('popup-limite-overlay').style.display = 'none';
            if (window.limiteCallback) {
                window.limiteCallback(prosseguir, isUrgente);
                window.limiteCallback = null;
            }
        }

        function filtrarProdutos() {
            const termo = document.getElementById("pesquisaProduto").value.toLowerCase();
            const linhas = document.querySelectorAll("#listaProdutos tr");
            linhas.forEach(tr => {
                const nome = tr.children[1].textContent.toLowerCase();
                tr.style.display = nome.includes(termo) ? "" : "none";
            });
        }

        function mostrarPopupSucesso() {
            const popup = document.getElementById('popup-sucesso');
            popup.classList.add('show');
            setTimeout(() => {
                popup.classList.remove('show');
            }, 3000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    // Obter empresa e setor
                    const empresa = document.getElementById('empresa').value; // Nome da empresa
                    let setor = '';
                    if (document.getElementById('funcao')) {
                        setor = document.getElementById('funcao').value;
                    }
                    // Calcular valor total dos produtos
                    let valorTotal = 0;
                    const codigosProdutos = document.querySelectorAll('input[name="codigo_produto[]"]');
                    const quantidades = document.querySelectorAll('input[name="quantidade[]"]');

                    for (let i = 0; i < codigosProdutos.length; i++) {
                        const codigo = codigosProdutos[i].value;
                        const quantidade = parseInt(quantidades[i].value || '0', 10);
                        valorTotal += quantidade * 15; // Valor médio estimado por produto
                    }

                    // Verificar limites antes de enviar
                    verificarLimitesAntesEnvio(empresa, setor, valorTotal, function(podeEnviar, isUrgente = false) {
                        if (podeEnviar) {
                            // Enviar o formulário via AJAX
                            const formData = new FormData(form);

                            // Adicionar flag de urgente se necessário
                            if (isUrgente) {
                                formData.append('pedido_urgente', '1');
                                formData.append('motivo_urgente', 'Limite excedido');
                            }

                            fetch('salvar-pedido-mensal.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    mostrarPopupSucesso();
                                    form.reset();
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 3000);
                                } else {
                                    alert('Erro: ' + data.message);
                                }
                            })
                            .catch(error => {
                                alert('Ocorreu um erro ao salvar o pedido.');
                            });
                        }
                    });
                });
            }
        });

        function abrirPopupGrupos() {
            document.getElementById('popupGruposOverlay').classList.add('active');
            fetch('listar-grupos-pedido-mensal.php')
                .then(r => r.json())
                .then(data => {
                    const tbody = document.getElementById('grupos-pedidos-tbody');
                    tbody.innerHTML = '';
                    data.forEach(grupo => {
                        tbody.innerHTML += `<tr><td>${grupo.codigo}</td><td>${grupo.nome}</td><td>${grupo.produtos.map(p => p.nome + ' (' + p.quantidade + ')').join(', ')}</td><td><button type='button' onclick='selecionarGrupoProdutos(${JSON.stringify(grupo.produtos).replace(/'/g, "&apos;")})'>Selecionar</button></td></tr>`;
                    });
                });
        }

        function fecharPopupGrupos() {
            document.getElementById('popupGruposOverlay').classList.remove('active');
        }

        function selecionarGrupoProdutos(produtos) {
            // Limpa os itens atuais
            const container = document.getElementById('itens-container');
            container.innerHTML = '';
            produtos.forEach(p => {
                const div = document.createElement('div');
                div.classList.add('item');
                div.innerHTML = `
                    <input type="number" name="quantidade[]" placeholder="Quantidade" required value="${p.quantidade}">
                    <input type="text" name="produto[]" placeholder="Produto" required value="${p.nome}" readonly>
                    <input type="hidden" name="codigo_produto[]" value="${p.codigo}">
                    <button type="button" class="search-btn" onclick="abrirPopupProdutos(this); event.preventDefault();" title="Pesquisar Produto">
                        <svg class="search-icon" viewBox="0 0 24 24">
                            <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                            <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <button type="button" class="remove-btn" onclick="removerProduto(this)" title="Remover Produto">
                        <svg class="remove-icon" viewBox="0 0 20 20">
                            <line x1="5" y1="5" x2="15" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                            <line x1="15" y1="5" x2="5" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                `;
                container.appendChild(div);
            });
            fecharPopupGrupos();
        }
    </script>
    <style>
        /* Copiado de requisicoes.php */
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        h1 {
            font-size: 1.7rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 28px;
            text-align: center;
        }
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .form-row {
            display: flex;
            gap: 18px;
            flex-wrap: wrap;
            margin-bottom: 0;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            flex: 1 1 220px;
            margin-bottom: 18px;
            min-width: 180px;
        }
        .form-group label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .form-group input,
        .form-group textarea {
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            transition: border 0.2s;
        }
        .form-group input:focus,
        .form-group textarea:focus {
            border-color: #2563eb;
            outline: none;
        }
        .form-group textarea {
            min-height: 60px;
            resize: vertical;
        }
        .divider {
            width: 100%;
            height: 1px;
            background: #d1d5db;
            margin: 18px 0 24px 0;
            border-radius: 1px;
        }
        .itens-title {
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 45px;
            margin-top: 18px;
        }
        .item {
            display: flex;
            gap: 10px;
            margin-bottom: 8px;
        }
        .item input {
            flex: 1;
            padding: 8px 10px;
            border-radius: 6px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
        }
        .item input:focus {
            border-color: #2563eb;
        }
        .item input[name="quantidade[]"] {
            width: 120px;
            flex: none;
        }
        .item input[name="produto[]"] {
            min-width: 180px;
        }
        .item button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            width: 32px;
            height: 32px;
            min-width: 32px;
            min-height: 32px;
            max-width: 32px;
            max-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
            padding: 0;
            font-size: 18px;
        }
        .item button[onclick^="abrirPopupProdutos"]:hover,
        .item button[onclick^="removerProduto"]:hover {
            background: #1d4ed8;
            color: #fff;
        }
        .item button[onclick^="removerProduto"] {
            background: #fff;
            color: #dc2626;
            border: 1.5px solid #dc2626;
        }
        .item button[onclick^="removerProduto"]:hover {
            color: #fff;
            background: #dc2626;
        }
        .adicionar-produto-btn {
            background: #10b981;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 10px 18px;
            font-size: 15px;
            font-weight: 600;
            margin-top: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .adicionar-produto-btn:hover {
            background: #059669;
        }
        .solicitar-btn {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 12px 28px;
            font-size: 16px;
            font-weight: 600;
            margin-top: 18px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .solicitar-btn:hover {
            background: #1d4ed8;
        }
        .popup-sucesso {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 99999;
            font-size: 16px;
            font-weight: bold;
            transform: translateX(400px);
            transition: all 0.4s ease-in-out;
            opacity: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            pointer-events: none;
        }
        .popup-sucesso.show {
            transform: translateX(0);
            opacity: 1;
        }
        .popup-sucesso::before {
            content: "✓";
            font-size: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        /* Popup overlay e centralização */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active {
            display: flex;
        }
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .popup-header h3 {
            margin: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        .popup-close {
            position: absolute;
            right: 18px;
            top: 18px;
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            transition: background 0.2s, color 0.2s;
        }
        .popup-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .popup-content {
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: flex-start !important;
        }
        .popup-search {
            width: 100%;
            max-width: 320px;
            margin: 0 auto 18px auto;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 15px;
            background: #f8fafc;
            color: #222;
            outline: none;
            transition: border 0.2s;
            display: block;
        }
        .popup-search:focus {
            border-color: #2563eb;
            background: #fff;
        }
        .popup-table-container {
            width: 100%;
            overflow-x: auto;
        }
        .popup-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        .popup-table th, .popup-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #f3f4f6;
            text-align: left;
        }
        .popup-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
        }
        .popup-table tr:last-child td {
            border-bottom: none;
        }
        .popup-table tr:hover {
            background: #f9fafb;
        }
        .popup-table button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 6px 14px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .popup-table button:hover {
            background: #1d4ed8;
        }
        .remove-btn {
            background: #fff;
            border: 1.5px solid #dc2626;
            border-radius: 7px;
            width: 40px;
            height: 40px;
            min-width: 40px;
            min-height: 40px;
            max-width: 40px;
            max-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
            padding: 0;
        }
        .remove-btn:hover {
            background: #dc2626;
        }
        .remove-icon {
            width: 18px;
            height: 18px;
            display: block;
        }
        .input-search-group {
            display: flex;
            align-items: stretch;
            width: 100%;
        }
        .input-search-group input[type="text"] {
            height: 40px !important;
            line-height: 40px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            margin-right: 0;
            flex: 1;
            padding-right: 12px;
        }
        .input-search-btn-wrapper {
            height: 40px;
            display: flex;
            align-items: center;
        }
        .input-search-group .search-btn {
            width: 40px;
            min-width: 40px;
            max-width: 40px;
            height: 40px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            margin-left: 0;
            position: static;
            padding: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .input-search-group .search-icon {
            width: 16px;
            height: 16px;
            stroke: #2563eb;
        }
        .item .search-btn .search-icon {
            width: 16px;
            height: 16px;
            stroke: #fff;
        }
        .search-btn {
            background: #2563eb;
            color: #fff;
        }
        .search-btn:hover {
            background: #1d4ed8;
            color: #fff;
        }
        .search-btn .search-icon {
            stroke: #fff;
            width: 18px;
            height: 18px;
        }
        .item .search-btn .search-icon {
            stroke: #fff;
            width: 16px;
            height: 16px;
        }

        /* Popup de aviso para produto duplicado */
        .popup-aviso-produto {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .popup-aviso-content {
            background: #dc2626;
            color: white;
            padding: 20px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            animation: popup-bounce 0.3s ease-out;
        }

        @keyframes popup-bounce {
            0% {
                transform: scale(0.7);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Popup de aviso de limite */
        .popup-limite-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10001;
        }

        .popup-limite-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
            animation: popup-bounce 0.3s ease-out;
        }

        .popup-limite-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .popup-limite-icon.aviso {
            color: #f59e0b;
        }

        .popup-limite-icon.bloqueado {
            color: #dc2626;
        }

        .popup-limite-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #111827;
        }

        .popup-limite-message {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .popup-limite-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .popup-limite-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .popup-limite-btn.continuar {
            background: #059669;
            color: white;
        }

        .popup-limite-btn.continuar:hover {
            background: #047857;
        }

        .popup-limite-btn.cancelar {
            background: #6b7280;
            color: white;
        }

        .popup-limite-btn.cancelar:hover {
            background: #4b5563;
        }

        .popup-limite-btn.ok {
            background: #2563eb;
            color: white;
        }

        .popup-limite-btn.ok:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>  
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <h1>Pedido Mensal</h1>
        <form action="salvar-pedido-mensal.php" method="POST">
            <div class="form-row">
                <div class="form-group" style="margin-right: 32px;">
                    <label for="codigo_pedido">Código do Pedido:</label>
                    <input type="number" id="codigo_pedido" name="codigo_pedido" value="<?php echo $codigo_pedido; ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="solicitante">Quem está solicitando:</label>
                    <input type="text" id="solicitante" name="solicitante" value="<?php echo htmlspecialchars($_SESSION['nome_usuario']); ?>" readonly required style="max-width:88%;">
                </div>
            </div>
            <div class="divider"></div>
            <div class="form-row">
                <div class="form-group" style="flex:2.8;">
                    <label for="empresa">Destino (Empresa):</label>
                    <div class="input-search-group">
                        <input type="text" id="empresa" name="empresa" required>
                        <input type="hidden" id="empresaSelecionada" name="empresaSelecionada">
                        <div class="input-search-btn-wrapper">
                            <button type="button" class="search-btn" onclick="abrirPopupEmpresas(); event.preventDefault();" title="Pesquisar Empresa">
                                <svg class="search-icon" viewBox="0 0 24 24">
                                    <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                                    <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="contrato">Contrato:</label>
                    <input type="text" id="contrato" name="contrato" required style="max-width:90%;">
                </div>
            </div>
            <div class="divider"></div>
            <div class="form-row">
                <div class="form-group" style="flex:2.8;">
                    <label for="destinatario">Destinatário:</label>
                    <div class="input-search-group">
                        <input type="text" id="destinatario" name="destinatario" required>
                        <input type="hidden" id="id_destinatario" name="id_destinatario">
                        <div class="input-search-btn-wrapper">
                            <button type="button" class="search-btn" onclick="abrirPopupDestinatarios(); event.preventDefault();" title="Pesquisar Destinatário">
                                <svg class="search-icon" viewBox="0 0 24 24">
                                    <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                                    <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="funcao">Função:</label>
                    <input type="text" id="funcao" name="funcao" required style="max-width:90%;">
                </div>
                <div class="form-group">
                    <label for="setor">Setor:</label>
                    <input type="text" id="setor" name="setor" required style="max-width:90%;">
                </div>
            </div>
            <div class="divider"></div>
            <div class="form-group">
                <label for="finalidade">Finalidade:</label>
                <input type="text" id="finalidade" name="finalidade" required style="max-width:95%;">
            </div>
            <div class="form-group">
                <label for="observacao">Observação:</label>
                <textarea id="observacao" name="observacao"></textarea>
            </div>
            <div class="divider"></div>
            <div class="itens-title">Itens do Pedido</div>
            <button type="button" id="btn-upload-grupo" style="margin-bottom:18px; background:#2196F3; color:#fff; border:none; border-radius:7px; padding:10px 18px; font-size:15px; font-weight:600; cursor:pointer; transition:background 0.2s;" onclick="abrirPopupGrupos()">Upload Grupo</button>
            <div id="itens-container">
                <div class="item">
                    <input type="number" name="quantidade[]" placeholder="Quantidade" required>
                    <input type="text" name="produto[]" placeholder="Produto" required>
                    <button type="button" class="search-btn" onclick="abrirPopupProdutos(this); event.preventDefault();" title="Pesquisar Produto">
                        <svg class="search-icon" viewBox="0 0 24 24">
                            <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                            <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <button type="button" class="remove-btn" onclick="removerProduto(this)" title="Remover Produto">
                        <svg class="remove-icon" viewBox="0 0 20 20">
                            <line x1="5" y1="5" x2="15" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                            <line x1="15" y1="5" x2="5" y2="15" stroke="#dc2626" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            <button type="button" class="adicionar-produto-btn" onclick="adicionarProduto()">Adicionar Produto</button>
            <button type="submit" class="solicitar-btn">Enviar Pedido</button>
        </form>
        <div id="popup-sucesso" class="popup-sucesso">
            Pedido feito com sucesso!
        </div>

        <!-- Pop-up de Empresas -->
        <div class="popup-overlay" id="popupEmpresasOverlay">
            <div class="popup" id="popupEmpresas">
                <div class="popup-header">
                    <h3>Selecionar Empresa</h3>
                    <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
                </div>
                <div class="popup-content">
                    <input type="text" class="popup-search" id="pesquisaEmpresa" placeholder="Pesquisar empresa..." oninput="filtrarEmpresas()">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Contrato</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="listaEmpresas">
                                <?php
                                $result = $conn->query("SELECT codigo_empresa, nome_empresa, contrato FROM empresas WHERE status = 'ativo'");
                                while ($row = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= $row['codigo_empresa'] ?></td>
                                        <td><?= $row['nome_empresa'] ?></td>
                                        <td><?= htmlspecialchars($row['contrato'], ENT_QUOTES) ?></td>
                                        <td><button onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= $row['nome_empresa'] ?>', '<?= htmlspecialchars($row['contrato'], ENT_QUOTES) ?>')">Selecionar</button></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pop-up de Destinatários -->
        <div class="popup-overlay" id="popupDestinatariosOverlay">
            <div class="popup" id="popupDestinatarios">
                <div class="popup-header">
                    <h3>Selecionar Destinatário</h3>
                    <button class="popup-close" onclick="fecharPopupDestinatarios()">&times;</button>
                </div>
                <div class="popup-content">
                    <input type="text" class="popup-search" id="pesquisaDestinatario" placeholder="Pesquisar destinatário..." oninput="filtrarDestinatarios()">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Função</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="listaDestinatarios">
                                <?php
                                $result = $conn->query("SELECT id, nome, funcao, setor FROM pessoas WHERE status = 'ativo'"); 
                                while ($row = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= $row['id'] ?></td>
                                        <td><?= $row['nome'] ?></td>
                                        <td><?= htmlspecialchars($row['funcao'], ENT_QUOTES) ?></td>
                                        <td><button onclick="selecionarDestinatario('<?= $row['id'] ?>', '<?= $row['nome'] ?>', '<?= htmlspecialchars($row['funcao'], ENT_QUOTES) ?>', '<?= htmlspecialchars($row['setor'], ENT_QUOTES) ?>')">Selecionar</button></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pop-up de Produtos -->
        <div class="popup-overlay" id="popupProdutosOverlay">
            <div class="popup" id="popupProdutos">
                <div class="popup-header">
                    <h3>Selecionar Produto</h3>
                    <button class="popup-close" onclick="fecharPopupProdutos()">&times;</button>
                </div>
                <div class="popup-content">
                    <input type="text" class="popup-search" id="pesquisaProduto" placeholder="Pesquisar produto..." oninput="filtrarProdutos()">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="listaProdutos">
                                <?php
                                $result = $conn->query("SELECT codigo, nome FROM produtos WHERE status = 'ativo'");
                                while ($row = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= $row['codigo'] ?></td>
                                        <td><?= $row['nome'] ?></td>
                                        <td><button onclick="selecionarProduto('<?= $row['codigo'] ?>', '<?= $row['nome'] ?>')">Selecionar</button></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popup de grupos de produtos -->
        <div class="popup-overlay" id="popupGruposOverlay">
            <div class="popup" id="popupGrupos">
                <div class="popup-header">
                    <h3>Selecionar Grupo de Produtos</h3>
                    <button class="popup-close" onclick="fecharPopupGrupos()">&times;</button>
                </div>
                <div class="popup-content">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Produtos</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="grupos-pedidos-tbody"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>







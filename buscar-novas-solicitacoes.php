<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Não autenticado']);
    exit();
}
include 'conexao.php';

$maior_codigo_requisicao = isset($_GET['maior_codigo_requisicao']) ? intval($_GET['maior_codigo_requisicao']) : 0;
$maior_codigo_pedido_mensal = isset($_GET['maior_codigo_pedido_mensal']) ? intval($_GET['maior_codigo_pedido_mensal']) : 0;
$maior_codigo_pedido_especial = isset($_GET['maior_codigo_pedido_especial']) ? intval($_GET['maior_codigo_pedido_especial']) : 0;

// Detectar nome da coluna de data em cada tabela
$check_requisicoes_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
$check_pedidos_mensais_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
$check_pedidos_especiais_data = $conn->query("SHOW COLUMNS FROM pedidos_especiais LIKE '%data%'");

$data_requisicoes = "NOW() as data_solicitacao";
$data_pedidos_mensais = "NOW() as data_solicitacao";
$data_pedidos_especiais = "NOW() as data_solicitacao";

if ($check_requisicoes_data && $check_requisicoes_data->num_rows > 0) {
    $col = $check_requisicoes_data->fetch_assoc();
    $data_requisicoes = $col['Field'] . " as data_solicitacao";
}
if ($check_pedidos_mensais_data && $check_pedidos_mensais_data->num_rows > 0) {
    $col = $check_pedidos_mensais_data->fetch_assoc();
    $data_pedidos_mensais = $col['Field'] . " as data_solicitacao";
}
if ($check_pedidos_especiais_data && $check_pedidos_especiais_data->num_rows > 0) {
    $col = $check_pedidos_especiais_data->fetch_assoc();
    $data_pedidos_especiais = $col['Field'] . " as data_solicitacao";
}

// Consultas para buscar novas solicitações
$sql_requisicoes = "SELECT
    codigo_solicitacao as codigo,
    'requisicao' as tipo,
    solicitante,
    empresa,
    contrato,
    finalidade,
    funcionario,
    funcao,
    observacao,
    COALESCE(requisicao_urgente, 0) as pedido_urgente,
    COALESCE(motivo_urgente, '') as motivo_urgente,
    $data_requisicoes,
    status
FROM requisicoes WHERE codigo_solicitacao > $maior_codigo_requisicao";

$sql_pedidos_mensais = "SELECT
    codigo_pedido as codigo,
    'pedido_mensal' as tipo,
    solicitante,
    empresa,
    contrato,
    finalidade,
    destinatario as funcionario,
    funcao,
    observacao,
    COALESCE(pedido_urgente, 0) as pedido_urgente,
    COALESCE(motivo_urgente, '') as motivo_urgente,
    $data_pedidos_mensais,
    status
FROM pedidos_mensais WHERE codigo_pedido > $maior_codigo_pedido_mensal";

$sql_pedidos_especiais = "SELECT
    codigo_pedido as codigo,
    'pedido_especial' as tipo,
    solicitante,
    empresa,
    contrato,
    finalidade,
    '' as funcionario,
    '' as funcao,
    detalhes as observacao,
    0 as pedido_urgente,
    '' as motivo_urgente,
    $data_pedidos_especiais,
    status
FROM pedidos_especiais WHERE codigo_pedido > $maior_codigo_pedido_especial";

// Unir as consultas
$sql = "($sql_requisicoes) UNION ($sql_pedidos_mensais) UNION ($sql_pedidos_especiais) ORDER BY data_solicitacao DESC";

$result = $conn->query($sql);

$novas = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        // Só retorna pendentes
        if (!isset($row['status']) || $row['status'] !== 'concluido') {
            $novas[] = $row;
        }
    }
    echo json_encode(['success' => true, 'novas' => $novas]);
} else {
    echo json_encode(['success' => false, 'message' => 'Erro na consulta', 'erro_sql' => $conn->error]);
} 
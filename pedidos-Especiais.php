<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Obter o próximo código de pedido automaticamente
$result = $conn->query("SELECT MAX(codigo_pedido) AS max_codigo FROM pedidos_especiais");
$row = $result->fetch_assoc();
$codigo_pedido = $row['max_codigo'] + 1;
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pedido Especial</title>
    <link rel="stylesheet" href="style.css">
    <script>
        function abrirPopupEmpresas() {
            document.getElementById("popupEmpresasOverlay").classList.add("active");
        }

        function fecharPopupEmpresas() {
            document.getElementById("popupEmpresasOverlay").classList.remove("active");
        }

        function selecionarEmpresa(codigo, nome, contrato) {
            document.getElementById("empresaSelecionada").value = codigo;
            document.getElementById("empresa").value = nome;
            document.getElementById("contrato").value = contrato;
            fecharPopupEmpresas();
        }

        function filtrarEmpresas() {
            const termo = document.getElementById("pesquisaEmpresa").value.toLowerCase();
            const linhas = document.querySelectorAll("#listaEmpresas tr");
            linhas.forEach(tr => {
                const nome = tr.children[1].textContent.toLowerCase();
                tr.style.display = nome.includes(termo) ? "" : "none";
            });
        }

        // Função para pré-visualizar a imagem selecionada
        function previewImage(event) {
            const reader = new FileReader();
            reader.onload = function() {
                const output = document.getElementById('imagePreview');
                output.src = reader.result;
                output.style.display = 'block';
            }
            reader.readAsDataURL(event.target.files[0]);
        }
    </script>
    <style>
        /* Copiado de requisicoes.php */
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        h1 {
            font-size: 1.7rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 28px;
            text-align: center;
        }
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .form-row {
            display: flex;
            gap: 18px;
            flex-wrap: wrap;
            margin-bottom: 0;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            flex: 1 1 220px;
            margin-bottom: 18px;
            min-width: 180px;
        }
        .form-group label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .form-group input,
        .form-group textarea {
            padding: 10px 12px;
            border-radius: 7px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
            color: #222;
            transition: border 0.2s;
        }
        .form-group input:focus,
        .form-group textarea:focus {
            border-color: #2563eb;
            outline: none;
        }
        .form-group textarea {
            min-height: 60px;
            resize: vertical;
        }
        .divider {
            width: 100%;
            height: 1px;
            background: #d1d5db;
            margin: 18px 0 24px 0;
            border-radius: 1px;
        }
        .itens-title {
            font-size: 24px;
            font-weight: bold;
            color: #111827;
            margin-bottom: 45px;
            margin-top: 18px;
        }
        .item {
            display: flex;
            gap: 10px;
            margin-bottom: 8px;
        }
        .item input {
            flex: 1;
            padding: 8px 10px;
            border-radius: 6px;
            border: 1.5px solid #e5e7eb;
            background: #fff;
            font-size: 15px;
        }
        .item input:focus {
            border-color: #2563eb;
        }
        .item input[name="quantidade[]"] {
            width: 120px;
            flex: none;
        }
        .item input[name="produto[]"] {
            min-width: 180px;
        }
        .item button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            width: 32px;
            height: 32px;
            min-width: 32px;
            min-height: 32px;
            max-width: 32px;
            max-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
            padding: 0;
            font-size: 18px;
        }
        .item button[onclick^="abrirPopupProdutos"]:hover,
        .item button[onclick^="removerProduto"]:hover {
            background: #1d4ed8;
            color: #fff;
        }
        .item button[onclick^="removerProduto"] {
            background: #fff;
            color: #dc2626;
            border: 1.5px solid #dc2626;
        }
        .item button[onclick^="removerProduto"]:hover {
            color: #fff;
            background: #dc2626;
        }
        .adicionar-produto-btn {
            background: #10b981;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 10px 18px;
            font-size: 15px;
            font-weight: 600;
            margin-top: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .adicionar-produto-btn:hover {
            background: #059669;
        }
        .solicitar-btn {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 7px;
            padding: 12px 28px;
            font-size: 16px;
            font-weight: 600;
            margin-top: 18px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .solicitar-btn:hover {
            background: #1d4ed8;
        }
        .popup-sucesso {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 99999;
            font-size: 16px;
            font-weight: bold;
            transform: translateX(400px);
            transition: all 0.4s ease-in-out;
            opacity: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            pointer-events: none;
        }
        .popup-sucesso.show {
            transform: translateX(0);
            opacity: 1;
        }
        .popup-sucesso::before {
            content: "✓";
            font-size: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        /* Popup overlay e centralização */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.active {
            display: flex;
        }
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .popup-header h3 {
            margin: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        .popup-close {
            position: absolute;
            right: 18px;
            top: 18px;
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            transition: background 0.2s, color 0.2s;
        }
        .popup-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .popup-content {
            padding: 24px;
            display: flex;
            flex-direction: column;
            align-items: flex-start !important;
        }
        .popup-search {
            width: 100%;
            max-width: 320px;
            margin: 0 auto 18px auto;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 15px;
            background: #f8fafc;
            color: #222;
            outline: none;
            transition: border 0.2s;
            display: block;
        }
        .popup-search:focus {
            border-color: #2563eb;
            background: #fff;
        }
        .popup-table-container {
            width: 100%;
            overflow-x: auto;
        }
        .popup-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        .popup-table th, .popup-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #f3f4f6;
            text-align: left;
        }
        .popup-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
        }
        .popup-table tr:last-child td {
            border-bottom: none;
        }
        .popup-table tr:hover {
            background: #f9fafb;
        }
        .popup-table button {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 6px 14px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .popup-table button:hover {
            background: #1d4ed8;
        }
        .remove-btn {
            background: #fff;
            border: 1.5px solid #dc2626;
            border-radius: 7px;
            width: 40px;
            height: 40px;
            min-width: 40px;
            min-height: 40px;
            max-width: 40px;
            max-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 6px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
            padding: 0;
        }
        .remove-btn:hover {
            background: #dc2626;
        }
        .remove-icon {
            width: 18px;
            height: 18px;
            display: block;
        }
        .input-search-group {
            display: flex;
            align-items: stretch;
            width: 100%;
        }
        .input-search-group input[type="text"] {
            height: 40px !important;
            line-height: 40px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            margin-right: 0;
            flex: 1;
            padding-right: 12px;
        }
        .input-search-btn-wrapper {
            height: 40px;
            display: flex;
            align-items: center;
        }
        .input-search-group .search-btn {
            width: 40px;
            min-width: 40px;
            max-width: 40px;
            height: 40px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
            margin-left: 0;
            position: static;
            padding: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .input-search-group .search-icon {
            width: 16px;
            height: 16px;
            stroke: #2563eb;
        }
        .item .search-btn .search-icon {
            width: 16px;
            height: 16px;
            stroke: #fff;
        }
        .search-btn {
            background: #2563eb;
            color: #fff;
        }
        .search-btn:hover {
            background: #1d4ed8;
            color: #fff;
        }
        .search-btn .search-icon {
            stroke: #fff;
            width: 18px;
            height: 18px;
        }
        .item .search-btn .search-icon {
            stroke: #fff;
            width: 16px;
            height: 16px;
        }
        .image-preview-container {
            margin-top: 10px;
            margin-bottom: 20px;
            width: 100%;
            display: flex;
            justify-content: center;
        }
        #imagePreview {
            max-width: 100%;
            max-height: 260px;
            width: auto;
            height: auto;
            display: none;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            object-fit: contain;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        }
    </style>
</head>
<body>  
    <?php include 'topbar.php'; ?>
    <div class="content-container">
        <h1>Pedido Especial</h1>
        <form action="salvar-pedido-especial.php" method="POST" enctype="multipart/form-data">
            <div class="form-row">
                <div class="form-group" style="margin-right: 32px;">
                    <label for="codigo_pedido">Código do Pedido:</label>
                    <input type="number" id="codigo_pedido" name="codigo_pedido" value="<?php echo $codigo_pedido; ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="solicitante">Quem está solicitando:</label>
                    <input type="text" id="solicitante" name="solicitante" value="<?php echo htmlspecialchars($_SESSION['nome_usuario']); ?>" readonly required style="max-width:88%;">
                </div>
            </div>
            <div class="divider"></div>
            <div class="form-row">
                <div class="form-group" style="flex:2.8;">
                    <label for="empresa">Destino (Empresa):</label>
                    <div class="input-search-group">
                        <input type="text" id="empresa" name="empresa" required>
                        <div class="input-search-btn-wrapper">
                            <button type="button" class="search-btn" onclick="abrirPopupEmpresas(); event.preventDefault();" title="Pesquisar Empresa">
                                <svg class="search-icon" viewBox="0 0 24 24">
                                    <circle cx="11" cy="11" r="7.2" stroke="currentColor" stroke-width="1.4" fill="none" />
                                    <line x1="16.2" y1="16.2" x2="21" y2="21" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="contrato">Contrato:</label>
                    <input type="text" id="contrato" name="contrato" required style="max-width:90%;">
                </div>
            </div>
            <div class="divider"></div>
            <div class="form-group">
                <label for="finalidade">Finalidade:</label>
                <input type="text" id="finalidade" name="finalidade" required style="max-width:95%;">
            </div>
            <div class="form-group">
                <label for="detalhes">Detalhes (pode incluir links):</label>
                <textarea id="detalhes" name="detalhes"></textarea>
            </div>
            <div class="form-group">
                <label for="imagem">Imagem (opcional):</label>
                <input type="file" name="imagem" accept="image/*" onchange="previewImage(event)" style="max-width:95%; width:100%;">
                <div class="image-preview-container">
                    <img id="imagePreview" src="#" alt="Pré-visualização da imagem">
                </div>
            </div>
            <input type="hidden" id="empresaSelecionada" name="empresaSelecionada">
            <button type="submit" class="solicitar-btn">Enviar Pedido Especial</button>
        </form>

        <!-- Pop-up de Empresas -->
        <div class="popup-overlay" id="popupEmpresasOverlay">
            <div class="popup" id="popupEmpresas">
                <div class="popup-header">
                    <h3>Selecionar Empresa</h3>
                    <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
                </div>
                <div class="popup-content">
                    <input type="text" class="popup-search" id="pesquisaEmpresa" placeholder="Pesquisar empresa..." oninput="filtrarEmpresas()">
                    <div class="popup-table-container">
                        <table class="popup-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Nome</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody id="listaEmpresas">
                                <?php
                                $result = $conn->query("SELECT codigo_empresa, nome_empresa, contrato FROM empresas WHERE status = 'ativo'");
                                while ($row = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= $row['codigo_empresa'] ?></td>
                                        <td><?= $row['nome_empresa'] ?></td>
                                        <td><button onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= $row['nome_empresa'] ?>', '<?= htmlspecialchars($row['contrato'], ENT_QUOTES) ?>')">Selecionar</button></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div id="popup-sucesso" class="popup-sucesso">
            Pedido especial enviado com sucesso!
        </div>
    </div>
</body>
</html>

<script>
    function mostrarPopupSucesso() {
        const popup = document.getElementById('popup-sucesso');
        popup.classList.add('show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                fetch('salvar-pedido-especial.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        mostrarPopupSucesso();
                        this.reset();
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else {
                        alert('Erro: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Ocorreu um erro ao salvar o pedido especial.');
                });
            });
        }
    });
</script>



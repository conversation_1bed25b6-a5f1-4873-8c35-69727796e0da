body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #000;
    color: #333;
    min-height: 100vh;
}

header {
    background-color: #007BFF;
    padding: 15px;
    color: white;
    text-align: center;
}

main {
    padding: 30px;
    margin-left: 270px; /* Espaço para a topbar */
    min-height: 100vh;
    background-color: #fff;
    border-radius: 20px 0 0 20px;
    margin-top: 20px;
    margin-right: 20px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
    position: relative;
    z-index: 1;
}

form {
    max-width: 600px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

input, select, textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #007BFF;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

button {
    padding: 12px;
    background-color: #007BFF;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #0056b3;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 768px) {
    main {
        margin-left: 0;
        margin-right: 0;
        border-radius: 0;
        margin-top: 0;
        padding: 20px;
    }
    
    form {
        margin: 10px;
        padding: 20px;
    }
}
  
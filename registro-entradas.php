<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Buscar produtos da entrada se um ID for fornecido via AJAX
if (isset($_GET['get_produtos']) && isset($_GET['id'])) {
    $entrada_id = $_GET['id'];
    $stmt = $conn->prepare("SELECT * FROM produtos_entrada WHERE entrada_id = ?");
    $stmt->bind_param("i", $entrada_id);
    $stmt->execute();
    $produtos = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $stmt = $conn->prepare("SELECT e.*, em.nome_empresa, em.cnpj FROM entradas_estoque e LEFT JOIN empresas em ON e.codigo_empresa = em.codigo_empresa WHERE e.id = ?");
    $stmt->bind_param("i", $entrada_id);
    $stmt->execute();
    $entrada = $stmt->get_result()->fetch_assoc();
    
    echo json_encode(['produtos' => $produtos, 'entrada' => $entrada]);
    exit;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registros de Entrada</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px; /* 270px da topbar + 20px de margem */
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        
        /* Estilo para o pop-up */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(34, 34, 34, 0.18);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .popup {
            background: #fff;
            padding: 32px 28px 24px;
            border-radius: 14px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.10);
            width: 95vw;
            max-width: 420px;
            min-width: 320px;
        }

        .popup h3 {
            margin-top: 0;
            color: #222;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 18px;
            letter-spacing: 0.01em;
        }

        .info-section {
            margin-bottom: 18px;
            padding: 0;
            background: none;
            border-radius: 0;
            border: none;
        }

        .info-section p {
            margin: 0 0 7px 0;
            color: #222;
            font-size: 15px;
        }

        .popup table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 18px;
            background: #fafbfc;
            border-radius: 8px;
            overflow: hidden;
        }

        .popup th, .popup td {
            border: none;
            padding: 10px 8px;
            text-align: left;
            font-size: 14px;
        }
        
        .popup th {
            background: #f4f6fa;
            color: #2563eb;
            font-weight: 600;
        }

        .popup tr {
            border-bottom: 1px solid #f0f1f3;
        }
        
        .popup tr:last-child {
            border-bottom: none;
        }

        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }
        
        .btn-modern {
            padding: 8px 18px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            background: #f4f6fa;
            color: #222;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
        }
        
        .btn-modern.btn-confirm {
            background: #2563eb;
            color: #fff;
        }
        
        .btn-modern.btn-cancel {
            background: #e5e7eb;
            color: #222;
        }
        
        .btn-modern.btn-danger {
            background: #f3f4f6;
            color: #d32f2f;
        }
        
        .btn-modern:hover {
            background: #e0e7ef;
        }
        
        .status-ativa {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .status-cancelada {
            color: #e74c3c;
            font-weight: bold;
        }

        /* Popup de sucesso/cancelamento */
        .popup-sucesso {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 99999;
            font-size: 16px;
            font-weight: bold;
            transform: translateX(400px);
            transition: all 0.4s ease-in-out;
            opacity: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            pointer-events: none;
        }
        .popup-sucesso.show {
            transform: translateX(0);
            opacity: 1;
        }
        .popup-sucesso::before {
            content: "✓";
            font-size: 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .popup-sucesso.cancelada {
            background-color: #e74c3c;
        }
        .popup-sucesso.cancelada::before {
            content: "✗";
            background-color: rgba(255,255,255,0.2);
        }
        
        /* Responsividade para dispositivos móveis */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
        }
        
        /* Estilos modernos para a tabela principal */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        
        /* Status badges minimalistas */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-ativa {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-cancelada {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 6px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
        }
        
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        /* Título minimalista */
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
<h1>Registros de Entrada</h1>

<div class="modern-table-container">
    <table class="modern-table">
        <thead>
            <tr>
                <th>Código</th>
                <th>Empresa</th>
                <th>Data e Hora</th>
                <th>Status</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $result = $conn->query("SELECT e.*, em.nome_empresa FROM entradas_estoque e LEFT JOIN empresas em ON e.codigo_empresa = em.codigo_empresa ORDER BY e.id DESC");
            while ($row = $result->fetch_assoc()): 
                $status = isset($row['status']) ? $row['status'] : 'ativa';
                $statusClass = $status === 'ativa' ? 'status-ativa' : 'status-cancelada';
            ?>
                <tr>
                    <td><?= $row['id'] ?></td>
                    <td><?= htmlspecialchars($row['nome_empresa'] ?? '-') ?></td>
                    <td><?= date('d/m/Y H:i:s', strtotime($row['data_hora'])) ?></td>
                    <td><span class="status-badge <?= $statusClass ?>"><?= ucfirst($status) ?></span></td>
                    <td><button class="btn-modern btn-confirm" onclick="verDetalhes(<?= $row['id'] ?>)">Ver detalhes</button></td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>

<!-- Pop-up de detalhes -->
<div id="detalhesPopup" class="popup-overlay">
    <div class="popup">
        <h3>Entrada #<span id="entrada-id"></span></h3>
        
        <div class="info-section">
            <p><strong>Empresa:</strong> <span id="empresa-nome"></span></p>
            <p><strong>CNPJ:</strong> <span id="empresa-cnpj"></span></p>
            <p><strong>Data e Hora:</strong> <span id="data-hora"></span></p>
            <p><strong>Status:</strong> <span id="status-entrada"></span></p>
            <p><strong>Responsável:</strong> <span id="responsavel-entrada"></span></p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Quantidade</th>
                </tr>
            </thead>
            <tbody id="produtos-lista">
                <!-- Será preenchido dinamicamente -->
            </tbody>
        </table>
        
        <div class="form-buttons" id="botoes-acao">
            <!-- Botões serão adicionados dinamicamente -->
        </div>
    </div>
</div>

<!-- Pop-up de confirmação de cancelamento -->
<div id="cancelarPopup" class="popup-overlay">
    <div class="popup">
        <h3>Confirmar Cancelamento</h3>
        <p>Tem certeza que deseja cancelar esta entrada? Esta ação irá:</p>
        <ul>
            <li>Remover as quantidades adicionadas ao estoque</li>
            <li>Marcar esta entrada como cancelada</li>
        </ul>
        <p><strong>Esta ação não pode ser desfeita!</strong></p>
        
        <div class="form-buttons">
            <button class="btn-modern btn-cancel" onclick="fecharCancelamento()">Não, Voltar</button>
            <button class="btn-modern btn-danger" onclick="cancelarEntrada()">Sim, Cancelar Entrada</button>
        </div>
    </div>
</div>

<div id="popup-sucesso" class="popup-sucesso">
    <span id="popup-sucesso-msg">Entrada cancelada com sucesso!</span>
</div>
</div>

<script>
    let entradaAtualId = null;
    
    function verDetalhes(id) {
        entradaAtualId = id;
        
        // Fazer requisição AJAX para obter os detalhes da entrada
        fetch(`registro-entradas.php?get_produtos=1&id=${id}`)
            .then(response => response.json())
            .then(data => {
                // Preencher os detalhes da entrada
                document.getElementById('entrada-id').textContent = id;
                document.getElementById('empresa-nome').textContent = data.entrada.nome_empresa || '-';
                document.getElementById('empresa-cnpj').textContent = data.entrada.cnpj || '-';
                document.getElementById('data-hora').textContent = formatarData(data.entrada.data_hora);
                document.getElementById('responsavel-entrada').textContent = data.entrada.responsavel || 'Não informado';
                
                const status = data.entrada.status || 'ativa';
                const statusElement = document.getElementById('status-entrada');
                statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusElement.className = status === 'ativa' ? 'status-ativa' : 'status-cancelada';
                
                // Preencher a lista de produtos
                const produtosLista = document.getElementById('produtos-lista');
                produtosLista.innerHTML = '';
                
                data.produtos.forEach(produto => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${produto.codigo}</td>
                        <td>${produto.nome}</td>
                        <td>${produto.quantidade}</td>
                    `;
                    produtosLista.appendChild(tr);
                });
                
                // Configurar botões de ação
                const botoesAcao = document.getElementById('botoes-acao');
                botoesAcao.innerHTML = '';
                
                // Só mostrar botão de cancelar se a entrada estiver ativa
                if (status === 'ativa') {
                    const btnCancelar = document.createElement('button');
                    btnCancelar.className = 'btn-modern btn-danger';
                    btnCancelar.textContent = 'Cancelar Entrada';
                    btnCancelar.onclick = confirmarCancelamento;
                    botoesAcao.appendChild(btnCancelar);
                }
                
                const btnFechar = document.createElement('button');
                btnFechar.className = 'btn-modern btn-cancel';
                btnFechar.textContent = 'Fechar';
                btnFechar.onclick = fecharDetalhes;
                botoesAcao.appendChild(btnFechar);
                
                // Exibir o popup
                document.getElementById('detalhesPopup').style.display = 'flex';
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao carregar os detalhes da entrada.');
            });
    }
    
    function formatarData(dataString) {
        const data = new Date(dataString);
        return data.toLocaleString('pt-BR');
    }
    
    function fecharDetalhes() {
        document.getElementById('detalhesPopup').style.display = 'none';
    }
    
    function confirmarCancelamento() {
        document.getElementById('detalhesPopup').style.display = 'none';
        document.getElementById('cancelarPopup').style.display = 'flex';
    }
    
    function fecharCancelamento() {
        document.getElementById('cancelarPopup').style.display = 'none';
        document.getElementById('detalhesPopup').style.display = 'flex';
    }
    
    function cancelarEntrada() {
        // Enviar solicitação para cancelar a entrada
        fetch('cancelar-entrada.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                entrada_id: entradaAtualId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarPopupSucesso('Entrada cancelada com sucesso!', 'cancelada');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                alert('Erro ao cancelar entrada: ' + data.message);
                fecharCancelamento();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao processar a solicitação.');
            fecharCancelamento();
        });
    }

    function mostrarPopupSucesso(mensagem, tipo = 'sucesso') {
        const popup = document.getElementById('popup-sucesso');
        const msg = document.getElementById('popup-sucesso-msg');
        msg.textContent = mensagem;
        if (tipo === 'cancelada') {
            popup.classList.add('cancelada');
        } else {
            popup.classList.remove('cancelada');
        }
        popup.classList.add('show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }
</script>

</body>
</html>

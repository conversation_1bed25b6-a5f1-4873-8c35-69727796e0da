<?php
include 'conexao.php';

// Verificar se os dados foram enviados
if (!isset($_POST['tipo']) || !isset($_POST['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$tipo = $_POST['tipo'];
$codigo = $_POST['codigo'];
$itens = isset($_POST['itens']) ? json_decode($_POST['itens'], true) : [];

// Iniciar transação
$conn->begin_transaction();

try {
    // Determinar a tabela e o campo de código corretos com base no tipo
    $tabela_principal = '';
    $campo_codigo = '';
    $tabela_itens = '';
    
    switch ($tipo) {
        case 'requisicao':
            $tabela_principal = 'requisicoes';
            $campo_codigo = 'codigo_solicitacao';
            $tabela_itens = 'itens_solicitacao';
            break;
        case 'pedido_mensal':
            $tabela_principal = 'pedidos_mensais';
            $campo_codigo = 'codigo_pedido';
            $tabela_itens = 'itens_pedido_mensal';
            break;
        case 'pedido_especial':
            $tabela_principal = 'pedidos_especiais';
            $campo_codigo = 'codigo_pedido';
            $tabela_itens = ''; // Pedidos especiais não têm itens
            break;
        default:
            throw new Exception('Tipo de solicitação inválido');
    }
    
    // Verificar se a solicitação existe
    $stmt = $conn->prepare("SELECT * FROM $tabela_principal WHERE $campo_codigo = ?");
    $stmt->bind_param("i", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Solicitação não encontrada');
    }
    
    // Verificar se a solicitação já está concluída
    $solicitacao = $result->fetch_assoc();
    if (isset($solicitacao['status']) && $solicitacao['status'] === 'concluido') {
        throw new Exception('Não é possível alterar uma solicitação concluída');
    }
    
    // Atualizar itens apenas se não for pedido especial e se houver tabela de itens
    if ($tipo !== 'pedido_especial' && !empty($tabela_itens) && !empty($itens)) {
        // Excluir itens existentes
        $stmt = $conn->prepare("DELETE FROM $tabela_itens WHERE $campo_codigo = ?");
        $stmt->bind_param("i", $codigo);
        $stmt->execute();
        
        // Inserir novos itens
        $stmt = $conn->prepare("INSERT INTO $tabela_itens ($campo_codigo, produto, quantidade) VALUES (?, ?, ?)");
        
        foreach ($itens as $item) {
            $stmt->bind_param("isi", $codigo, $item['produto'], $item['quantidade']);
            $stmt->execute();
            
            if ($stmt->affected_rows === 0) {
                throw new Exception("Erro ao inserir item {$item['produto']}");
            }
        }
    }
    
    // Commit da transação
    $conn->commit();
    
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>

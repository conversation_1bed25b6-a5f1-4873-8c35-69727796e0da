<?php
include 'conexao.php';

echo "<h2>Verificando e corrigindo campos de pedido urgente</h2>";

// Verificar se a tabela requisicoes tem os campos necessários
$result = $conn->query("SHOW COLUMNS FROM requisicoes LIKE 'requisicao_urgente'");
if ($result->num_rows === 0) {
    echo "<p>Adicionando campo 'requisicao_urgente' na tabela requisicoes...</p>";
    $sql = "ALTER TABLE requisicoes ADD COLUMN requisicao_urgente TINYINT(1) DEFAULT 0";
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>✓ Campo 'requisicao_urgente' adicionado com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar campo 'requisicao_urgente': " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ Campo 'requisicao_urgente' já existe na tabela requisicoes</p>";
}

$result = $conn->query("SHOW COLUMNS FROM requisicoes LIKE 'motivo_urgente'");
if ($result->num_rows === 0) {
    echo "<p>Adicionando campo 'motivo_urgente' na tabela requisicoes...</p>";
    $sql = "ALTER TABLE requisicoes ADD COLUMN motivo_urgente VARCHAR(255) DEFAULT ''";
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>✓ Campo 'motivo_urgente' adicionado com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar campo 'motivo_urgente': " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ Campo 'motivo_urgente' já existe na tabela requisicoes</p>";
}

// Verificar se a tabela pedidos_mensais tem os campos necessários
$result = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE 'pedido_urgente'");
if ($result->num_rows === 0) {
    echo "<p>Adicionando campo 'pedido_urgente' na tabela pedidos_mensais...</p>";
    $sql = "ALTER TABLE pedidos_mensais ADD COLUMN pedido_urgente TINYINT(1) DEFAULT 0";
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>✓ Campo 'pedido_urgente' adicionado com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar campo 'pedido_urgente': " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ Campo 'pedido_urgente' já existe na tabela pedidos_mensais</p>";
}

$result = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE 'motivo_urgente'");
if ($result->num_rows === 0) {
    echo "<p>Adicionando campo 'motivo_urgente' na tabela pedidos_mensais...</p>";
    $sql = "ALTER TABLE pedidos_mensais ADD COLUMN motivo_urgente VARCHAR(255) DEFAULT ''";
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>✓ Campo 'motivo_urgente' adicionado com sucesso!</p>";
    } else {
        echo "<p style='color: red;'>✗ Erro ao adicionar campo 'motivo_urgente': " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✓ Campo 'motivo_urgente' já existe na tabela pedidos_mensais</p>";
}

echo "<h3>Estrutura atual da tabela requisicoes:</h3>";
$result = $conn->query("DESCRIBE requisicoes");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>Teste de requisições urgentes:</h3>";
$result = $conn->query("SELECT codigo_solicitacao, solicitante, requisicao_urgente, motivo_urgente FROM requisicoes WHERE requisicao_urgente = 1 LIMIT 5");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Código</th><th>Solicitante</th><th>Urgente</th><th>Motivo</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['codigo_solicitacao'] . "</td>";
        echo "<td>" . $row['solicitante'] . "</td>";
        echo "<td>" . $row['requisicao_urgente'] . "</td>";
        echo "<td>" . $row['motivo_urgente'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>Nenhuma requisição urgente encontrada ainda.</p>";
}

echo "<h3>Correção concluída!</h3>";
echo "<p><a href='todas-solitacoes-estoque.php'>Ir para Todas as Solicitações</a></p>";
echo "<p><a href='requisicoes.php'>Ir para Requisições</a></p>";

$conn->close();
?>

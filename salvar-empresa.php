<?php
include 'conexao.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $codigo_empresa = $_POST['codigo_empresa'];
    $nome_empresa = $_POST['nome_empresa'];
    $fantasia_empresa = $_POST['fantasia_empresa']; // Novo campo
    $cnpj = $_POST['cnpj'];
    $contrato = $_POST['contrato']; // Novo campo
    $regiao = $_POST['regiao'];
    $contratos = $_POST['contratos']; // Novo campo

    // Verificar se o código da empresa já existe no banco
    $verifica_sql = "SELECT codigo_empresa FROM empresas WHERE codigo_empresa = '$codigo_empresa'";
    $resultado = $conn->query($verifica_sql);

    if ($resultado->num_rows > 0) {
        // Redireciona para a página de cadastro com erro de código duplicado e mantém os dados
        $params = http_build_query([
            'status' => 'error',
            'message' => 'codigo_empresa_duplicado',
            'codigo_empresa' => $codigo_empresa,
            'nome_empresa' => $nome_empresa,
            'fantasia_empresa' => $fantasia_empresa,
            'cnpj' => $cnpj,
            'contrato' => $contrato,
            'regiao' => $regiao,
            'contratos' => $contratos
        ]);
        header("Location: cadastro-empresas.php?" . $params);
        exit();
    }

    // Verificar se o nome da empresa já existe no banco
    $verifica_nome_sql = "SELECT nome_empresa FROM empresas WHERE nome_empresa = '" . $conn->real_escape_string($nome_empresa) . "'";
    $resultado_nome = $conn->query($verifica_nome_sql);
    if ($resultado_nome->num_rows > 0) {
        $params = http_build_query([
            'status' => 'error',
            'message' => 'nome_duplicado',
            'codigo_empresa' => $codigo_empresa,
            'nome_empresa' => $nome_empresa,
            'fantasia_empresa' => $fantasia_empresa,
            'cnpj' => $cnpj,
            'contrato' => $contrato,
            'regiao' => $regiao,
            'contratos' => $contratos
        ]);
        header("Location: cadastro-empresas.php?" . $params);
        exit();
    }

    // Inserir a empresa no banco com os novos campos
    $sql = "INSERT INTO empresas (codigo_empresa, nome_empresa, fantasia_empresa, cnpj, contrato, regiao, contratos) 
            VALUES ('$codigo_empresa', '$nome_empresa', '$fantasia_empresa', '$cnpj', '$contrato', '$regiao', '$contratos')";

    if ($conn->query($sql) === TRUE) {
        // Redireciona para a página de cadastro com sucesso
        header("Location: cadastro-empresas.php?status=success");
        exit();
    } else {
        // Redireciona para a página de cadastro com erro genérico
        header("Location: cadastro-empresas.php?status=error");
        exit();
    }

    $conn->close();
}
?>

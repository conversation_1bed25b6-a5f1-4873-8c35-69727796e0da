<?php
include 'conexao.php';
$id = $_POST['id'];
$conn->begin_transaction();
try {
    $conn->query("DELETE FROM grupo_produtos_pedidos_mensais WHERE grupo_id = $id");
    $conn->query("DELETE FROM grupos_pedidos_mensais WHERE id = $id");
    $conn->commit();
    echo json_encode(['success'=>true]);
} catch(Exception $e) {
    $conn->rollback();
    echo json_encode(['success'=>false, 'message'=>$e->getMessage()]);
} 
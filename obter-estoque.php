<?php
include 'conexao.php';

if (isset($_GET['codigo'])) {
    $codigo = $_GET['codigo'];
    
    // Preparar a consulta para evitar injeção SQL
    $stmt = $conn->prepare("SELECT quantidade, estoque_minimo FROM produtos WHERE codigo = ?");
    $stmt->bind_param("s", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $produto = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'quantidade' => $produto['quantidade'],
            'estoque_minimo' => $produto['estoque_minimo']
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Produto não encontrado']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Código do produto não fornecido']);
}
?>


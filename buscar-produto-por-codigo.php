<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

include 'conexao.php';

if (!isset($_GET['codigo']) || empty($_GET['codigo'])) {
    echo json_encode(['success' => false, 'message' => 'Código não informado']);
    exit();
}

$codigo = $_GET['codigo'];

try {
    $sql = "SELECT nome FROM produtos WHERE codigo = ? AND status = 'ativo'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo json_encode(['success' => true, 'nome' => $row['nome']]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Produto não encontrado']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro interno: ' . $e->getMessage()]);
}
?>

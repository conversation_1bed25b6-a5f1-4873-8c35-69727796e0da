<?php
include 'conexao.php';
$codigo = $_POST['codigo'];
$nome = $_POST['nome'];
$produtos = json_decode($_POST['produtos'], true);
$conn->begin_transaction();
try {
    $stmt = $conn->prepare("INSERT INTO grupos_pedidos_mensais (codigo, nome) VALUES (?, ?)");
    $stmt->bind_param('ss', $codigo, $nome);
    $stmt->execute();
    $grupo_id = $stmt->insert_id;
    foreach ($produtos as $p) {
        $stmt2 = $conn->prepare("INSERT INTO grupo_produtos_pedidos_mensais (grupo_id, produto_id, quantidade) VALUES (?, ?, ?)");
        $stmt2->bind_param('iii', $grupo_id, $p['codigo'], $p['quantidade']);
        $stmt2->execute();
    }
    $conn->commit();
    echo json_encode(['success'=>true]);
} catch(Exception $e) {
    $conn->rollback();
    echo json_encode(['success'=>false, 'message'=>$e->getMessage()]);
} 
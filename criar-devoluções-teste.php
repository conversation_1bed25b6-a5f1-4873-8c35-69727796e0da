<?php
require_once 'conexao.php';

echo "<h2><PERSON><PERSON><PERSON>e</h2>";

try {
    // 1. Verificar se existem pessoas e produtos
    $result_pessoas = $conn->query("SELECT id, nome FROM pessoas WHERE status = 'ativo' LIMIT 3");
    $result_produtos = $conn->query("SELECT codigo, nome FROM produtos LIMIT 3");
    
    $pessoas = [];
    $produtos = [];
    
    while ($row = $result_pessoas->fetch_assoc()) {
        $pessoas[] = $row;
    }
    
    while ($row = $result_produtos->fetch_assoc()) {
        $produtos[] = $row;
    }
    
    if (empty($pessoas)) {
        echo "<p>❌ Nenhuma pessoa encontrada. <a href='criar-dados-teste-devolucao.php'>Criar dados de teste</a></p>";
        exit();
    }
    
    if (empty($produtos)) {
        echo "<p>❌ Nenhum produto encontrado. <a href='criar-dados-teste-devolucao.php'>Criar dados de teste</a></p>";
        exit();
    }
    
    // 2. Criar tabelas se não existirem
    $conn->query("
        CREATE TABLE IF NOT EXISTS devolucoes_epi (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pessoa_id INT NOT NULL,
            pessoa_nome VARCHAR(255) NOT NULL,
            produto_id VARCHAR(50) NOT NULL,
            produto_nome VARCHAR(255) NOT NULL,
            quantidade INT NOT NULL,
            estado VARCHAR(50) NOT NULL,
            data_devolucao DATETIME DEFAULT CURRENT_TIMESTAMP,
            usuario_id INT NOT NULL,
            assinatura LONGTEXT DEFAULT NULL,
            INDEX idx_pessoa_id (pessoa_id),
            INDEX idx_produto_id (produto_id),
            INDEX idx_data_devolucao (data_devolucao)
        )
    ");
    
    $conn->query("
        CREATE TABLE IF NOT EXISTS devolucoes_rapidas (
            id INT AUTO_INCREMENT PRIMARY KEY,
            produto_id VARCHAR(50) NOT NULL,
            produto_nome VARCHAR(255) NOT NULL,
            quantidade INT NOT NULL,
            estado VARCHAR(50) NOT NULL,
            data_devolucao DATETIME DEFAULT CURRENT_TIMESTAMP,
            usuario_id INT NOT NULL,
            assinatura LONGTEXT DEFAULT NULL,
            INDEX idx_produto_id (produto_id),
            INDEX idx_data_devolucao (data_devolucao)
        )
    ");
    
    echo "<p>✅ Tabelas criadas/verificadas</p>";
    
    // 3. Inserir devoluções por funcionário
    echo "<h3>Criando devoluções por funcionário:</h3>";
    $estados = ['novo', 'usado_bom', 'usado_leve', 'danificado_reparo', 'vencido'];
    
    foreach ($pessoas as $pessoa) {
        foreach ($produtos as $index => $produto) {
            if ($index >= 2) break; // Máximo 2 produtos por pessoa
            
            $quantidade = rand(1, 3);
            $estado = $estados[array_rand($estados)];
            $data = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
            
            $sql = "INSERT INTO devolucoes_epi (pessoa_id, pessoa_nome, produto_id, produto_nome, quantidade, estado, data_devolucao, usuario_id) VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("isssiss", $pessoa['id'], $pessoa['nome'], $produto['codigo'], $produto['nome'], $quantidade, $estado, $data);
            
            if ($stmt->execute()) {
                echo "<p>✅ {$pessoa['nome']} → {$produto['nome']} (Qtd: $quantidade, Estado: $estado)</p>";
            }
        }
    }
    
    // 4. Inserir devoluções rápidas
    echo "<h3>Criando devoluções rápidas:</h3>";
    
    foreach ($produtos as $produto) {
        $quantidade = rand(1, 5);
        $estado = $estados[array_rand($estados)];
        $data = date('Y-m-d H:i:s', strtotime('-' . rand(1, 15) . ' days'));
        
        $sql = "INSERT INTO devolucoes_rapidas (produto_id, produto_nome, quantidade, estado, data_devolucao, usuario_id) VALUES (?, ?, ?, ?, ?, 1)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssiss", $produto['codigo'], $produto['nome'], $quantidade, $estado, $data);
        
        if ($stmt->execute()) {
            echo "<p>✅ Devolução rápida: {$produto['nome']} (Qtd: $quantidade, Estado: $estado)</p>";
        }
    }
    
    // 5. Verificar resultados
    echo "<h3>Resumo:</h3>";
    
    $count_func = $conn->query("SELECT COUNT(*) as total FROM devolucoes_epi")->fetch_assoc()['total'];
    $count_rapida = $conn->query("SELECT COUNT(*) as total FROM devolucoes_rapidas")->fetch_assoc()['total'];
    
    echo "<p>Devoluções por funcionário: $count_func</p>";
    echo "<p>Devoluções rápidas: $count_rapida</p>";
    echo "<p>Total: " . ($count_func + $count_rapida) . "</p>";
    
    if ($count_func > 0 || $count_rapida > 0) {
        echo "<p style='color: green;'><strong>✅ Dados de teste criados com sucesso!</strong></p>";
        echo "<p>Agora você pode testar a aba 'Histórico de Devoluções'</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<h3>Próximos passos:</h3>";
echo "<ol>";
echo "<li><a href='obter-historico-devolucoes.php' target='_blank'>Testar endpoint do histórico</a></li>";
echo "<li><a href='devolucao.php' target='_blank'>Testar aba 'Histórico de Devoluções'</a></li>";
echo "</ol>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='devolucao.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Ir para Devolução</a>";
echo "</p>";

$conn->close();
?>

<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';
include_once 'periodos-limite-helper.php';

$id_limite = isset($_GET['id_limite']) ? intval($_GET['id_limite']) : (isset($_POST['id_limite']) ? intval($_POST['id_limite']) : 0);
if (!$id_limite) {
    echo json_encode(['success' => false, 'message' => 'id_limite não informado']);
    exit();
}

$periodos = listarPeriodosFinalizados($id_limite);

// Retornar apenas dados essenciais
$dados = [];
foreach ($periodos as $p) {
    $dados[] = [
        'id' => $p['id'],
        'data_inicio' => $p['data_inicio'],
        'data_fim' => $p['data_fim'],
        'criado_em' => $p['criado_em']
    ];
}

echo json_encode(['success' => true, 'periodos' => $dados]); 
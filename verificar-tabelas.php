<?php
include 'conexao.php';

// Verificar se a tabela requisicoes existe e tem a estrutura correta
$result = $conn->query("SHOW TABLES LIKE 'requisicoes'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE requisicoes (
        codigo_solicitacao INT AUTO_INCREMENT PRIMARY KEY,
        solicitante VARCHAR(100) NOT NULL,
        empresa VARCHAR(100) NOT NULL,
        contrato VARCHAR(50) NOT NULL,
        finalidade VARCHAR(255) NOT NULL,
        funcionario VARCHAR(100) NOT NULL,
        funcao VARCHAR(100) NOT NULL,
        observacao TEXT,
        data_solicitacao DATETIME NOT NULL,
        status VARCHAR(20) DEFAULT 'pendente'
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela requisicoes criada com sucesso!<br>";
    } else {
        echo "Erro ao criar tabela requisicoes: " . $conn->error . "<br>";
        echo "Erro ao criar tabela requisicoes: " . $conn->error . "<br>";
    }
} else {
    // Verificar se a coluna status existe
    $result = $conn->query("SHOW COLUMNS FROM requisicoes LIKE 'status'");
    if ($result->num_rows === 0) {
        // A coluna não existe, vamos adicioná-la
        $sql = "ALTER TABLE requisicoes ADD COLUMN status VARCHAR(20) DEFAULT 'pendente'";
        
        if ($conn->query($sql) === TRUE) {
            echo "Coluna status adicionada à tabela requisicoes com sucesso!<br>";
        } else {
            echo "Erro ao adicionar coluna status à tabela requisicoes: " . $conn->error . "<br>";
        }
    }
}

// Verificar se a tabela itens_solicitacao existe
$result = $conn->query("SHOW TABLES LIKE 'itens_solicitacao'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE itens_solicitacao (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo_solicitacao INT NOT NULL,
        produto VARCHAR(50) NOT NULL,
        quantidade INT NOT NULL,
        INDEX (codigo_solicitacao),
        INDEX (produto)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela itens_solicitacao criada com sucesso!<br>";
    } else {
        echo "Erro ao criar tabela itens_solicitacao: " . $conn->error . "<br>";
    }
}

// Verificar se a tabela itens_requisicao existe
$result = $conn->query("SHOW TABLES LIKE 'itens_requisicao'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE itens_requisicao (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo_solicitacao INT NOT NULL,
        produto VARCHAR(50) NOT NULL,
        quantidade INT NOT NULL,
        INDEX (codigo_solicitacao),
        INDEX (produto)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela itens_requisicao criada com sucesso!<br>";
    } else {
        echo "Erro ao criar tabela itens_requisicao: " . $conn->error . "<br>";
    }
}

// Verificar se a tabela itens_pedido_mensal existe
$result = $conn->query("SHOW TABLES LIKE 'itens_pedido_mensal'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE itens_pedido_mensal (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo_pedido INT NOT NULL,
        produto VARCHAR(50) NOT NULL,
        quantidade INT NOT NULL,
        INDEX (codigo_pedido),
        INDEX (produto)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela itens_pedido_mensal criada com sucesso!<br>";
    } else {
        echo "Erro ao criar tabela itens_pedido_mensal: " . $conn->error . "<br>";
    }
}

// Verificar se a tabela produtos existe
$result = $conn->query("SHOW TABLES LIKE 'produtos'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE produtos (
        codigo VARCHAR(50) PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        quantidade INT DEFAULT 0,
        unidade VARCHAR(20),
        categoria VARCHAR(50),
        data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela produtos criada com sucesso!<br>";
        
        // Inserir alguns produtos de exemplo
        $sql = "INSERT INTO produtos (codigo, nome, quantidade, unidade, categoria) VALUES
            ('P001', 'Caneta Esferográfica Azul', 100, 'UN', 'Material de Escritório'),
            ('P002', 'Papel A4 (Resma)', 50, 'UN', 'Material de Escritório'),
            ('P003', 'Grampeador', 20, 'UN', 'Material de Escritório'),
            ('P004', 'Clips (Caixa)', 30, 'UN', 'Material de Escritório'),
            ('P005', 'Pasta Suspensa', 40, 'UN', 'Material de Escritório')";
        
        if ($conn->query($sql) === TRUE) {
            echo "Produtos de exemplo inseridos com sucesso!<br>";
        } else {
            echo "Erro ao inserir produtos de exemplo: " . $conn->error . "<br>";
        }
    } else {
        echo "Erro ao criar tabela produtos: " . $conn->error . "<br>";
    }
}

// Verificar se a coluna quantidade existe na tabela produtos
$result = $conn->query("SHOW COLUMNS FROM produtos LIKE 'quantidade'");
if ($result->num_rows === 0) {
    // A coluna não existe, vamos adicioná-la
    $sql = "ALTER TABLE produtos ADD COLUMN quantidade INT DEFAULT 0";
    if ($conn->query($sql) === TRUE) {
        echo "Coluna quantidade adicionada à tabela produtos com sucesso!<br>";
    } else {
        echo "Erro ao adicionar coluna quantidade à tabela produtos: " . $conn->error . "<br>";
    }
}

// Verificar se a tabela estoque existe
$result = $conn->query("SHOW TABLES LIKE 'estoque'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE estoque (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo_produto VARCHAR(50) NOT NULL,
        quantidade INT NOT NULL DEFAULT 0,
        data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (codigo_produto)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela estoque criada com sucesso!<br>";
        
        // Inserir os produtos existentes na tabela estoque
        $sql = "INSERT INTO estoque (codigo_produto, quantidade)
                SELECT codigo, quantidade FROM produtos";
        
        if ($conn->query($sql) === TRUE) {
            echo "Produtos inseridos na tabela estoque com sucesso!<br>";
        } else {
            echo "Erro ao inserir produtos na tabela estoque: " . $conn->error . "<br>";
        }
    } else {
        echo "Erro ao criar tabela estoque: " . $conn->error . "<br>";
    }
}

// Verificar se a tabela saidas_estoque existe
$result = $conn->query("SHOW TABLES LIKE 'saidas_estoque'");
if ($result->num_rows === 0) {
    // A tabela não existe, vamos criá-la
    $sql = "CREATE TABLE saidas_estoque (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo_produto VARCHAR(50) NOT NULL,
        quantidade INT NOT NULL,
        tipo_saida VARCHAR(50) NOT NULL,
        referencia_saida VARCHAR(50) NOT NULL,
        data_saida DATETIME NOT NULL,
        status VARCHAR(20) DEFAULT 'ativa',
        INDEX (codigo_produto),
        INDEX (tipo_saida),
        INDEX (referencia_saida)
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "Tabela saidas_estoque criada com sucesso!<br>";
    } else {
        echo "Erro ao criar tabela saidas_estoque: " . $conn->error . "<br>";
    }
}

// Verificar se há itens em alguma das tabelas
$result = $conn->query("SELECT COUNT(*) as total FROM itens_solicitacao");
$row = $result->fetch_assoc();
echo "Total de itens na tabela itens_solicitacao: " . $row['total'] . "<br>";

$result = $conn->query("SELECT COUNT(*) as total FROM itens_requisicao");
if ($result) {
    $row = $result->fetch_assoc();
    echo "Total de itens na tabela itens_requisicao: " . $row['total'] . "<br>";
} else {
    echo "Erro ao contar itens na tabela itens_requisicao: " . $conn->error . "<br>";
}

$result = $conn->query("SELECT COUNT(*) as total FROM itens_pedido_mensal");
if ($result) {
    $row = $result->fetch_assoc();
    echo "Total de itens na tabela itens_pedido_mensal: " . $row['total'] . "<br>";
} else {
    echo "Erro ao contar itens na tabela itens_pedido_mensal: " . $conn->error . "<br>";
}

echo "Verificação concluída!";
?>





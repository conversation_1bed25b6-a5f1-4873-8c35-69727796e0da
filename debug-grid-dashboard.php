<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['usuario_nome'] = 'Administrador';
}

echo "<h2>🔍 Debug - Estrutura do Grid Dashboard</h2>";

// Ler o arquivo index.php e analisar a estrutura
$conteudo = file_get_contents('index.php');

// Contar ocorrências de dashboard-grid
$count_grid_open = substr_count($conteudo, 'dashboard-grid');
$count_modern_card = substr_count($conteudo, 'modern-card');

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📊 Análise da Estrutura:</h3>";
echo "<ul>";
echo "<li><strong>Ocorrências de 'dashboard-grid':</strong> $count_grid_open</li>";
echo "<li><strong>Ocorrências de 'modern-card':</strong> $count_modern_card</li>";
echo "</ul>";
echo "</div>";

// Verificar se há fechamento correto do grid
$lines = explode("\n", $conteudo);
$grid_opens = 0;
$grid_closes = 0;
$card_count = 0;
$inside_grid = false;

echo "<h3>🔍 Análise Linha por Linha:</h3>";
echo "<div style='background: white; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";

foreach ($lines as $num => $line) {
    $line_num = $num + 1;
    
    if (strpos($line, 'dashboard-grid') !== false) {
        if (strpos($line, '<div') !== false) {
            $grid_opens++;
            $inside_grid = true;
            echo "<div style='color: green; font-weight: bold;'>Linha $line_num: GRID ABERTO - " . htmlspecialchars(trim($line)) . "</div>";
        }
        if (strpos($line, '</div>') !== false && strpos($line, 'dashboard-grid') !== false) {
            $grid_closes++;
            $inside_grid = false;
            echo "<div style='color: red; font-weight: bold;'>Linha $line_num: GRID FECHADO - " . htmlspecialchars(trim($line)) . "</div>";
        }
    }
    
    if ($inside_grid && strpos($line, 'modern-card') !== false && strpos($line, '<div') !== false) {
        $card_count++;
        echo "<div style='color: blue; margin-left: 20px;'>Linha $line_num: CARD $card_count - " . htmlspecialchars(trim($line)) . "</div>";
    }
}

echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📈 Resumo:</h3>";
echo "<ul>";
echo "<li><strong>Grid aberto:</strong> $grid_opens vez(es)</li>";
echo "<li><strong>Grid fechado:</strong> $grid_closes vez(es)</li>";
echo "<li><strong>Cards dentro do grid:</strong> $card_count</li>";
echo "</ul>";

if ($grid_opens == $grid_closes && $grid_opens == 1) {
    echo "<p style='color: green; font-weight: bold;'>✅ Estrutura do grid parece correta!</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Problema na estrutura do grid detectado!</p>";
}
echo "</div>";

// Verificar CSS do grid
echo "<h3>🎨 CSS do Grid:</h3>";
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 8px; font-family: monospace;'>";

$css_lines = [];
$in_grid_css = false;
foreach ($lines as $line) {
    if (strpos($line, '.dashboard-grid') !== false) {
        $in_grid_css = true;
    }
    if ($in_grid_css) {
        $css_lines[] = htmlspecialchars($line);
        if (strpos($line, '}') !== false) {
            $in_grid_css = false;
        }
    }
}

foreach ($css_lines as $css_line) {
    echo $css_line . "<br>";
}
echo "</div>";

// Teste de largura da tela
echo "<h3>📱 Teste de Responsividade:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
echo "<p>Abra o console do navegador (F12) e execute:</p>";
echo "<code style='background: #f8f9fa; padding: 10px; display: block; border-radius: 4px;'>";
echo "console.log('Largura da tela:', window.innerWidth);<br>";
echo "console.log('Grid columns:', getComputedStyle(document.querySelector('.dashboard-grid')).gridTemplateColumns);<br>";
echo "console.log('Total de cards:', document.querySelectorAll('.modern-card').length);";
echo "</code>";
echo "</div>";

echo "<h3>🔗 Links para Teste:</h3>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0 10px;'>🏠 Dashboard Principal</a>";
echo "<a href='teste-grid-layout.html' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 0 10px;'>🧪 Teste Grid Isolado</a>";
echo "</div>";

// JavaScript para debug em tempo real
echo "<script>";
echo "window.addEventListener('load', function() {";
echo "  const grid = document.querySelector('.dashboard-grid');";
echo "  const cards = document.querySelectorAll('.modern-card');";
echo "  ";
echo "  if (grid) {";
echo "    console.log('✅ Grid encontrado:', grid);";
echo "    console.log('📊 Estilo do grid:', getComputedStyle(grid).display);";
echo "    console.log('📐 Colunas do grid:', getComputedStyle(grid).gridTemplateColumns);";
echo "    console.log('📏 Gap do grid:', getComputedStyle(grid).gap);";
echo "  } else {";
echo "    console.log('❌ Grid não encontrado!');";
echo "  }";
echo "  ";
echo "  console.log('🎴 Total de cards:', cards.length);";
echo "  cards.forEach((card, index) => {";
echo "    console.log(`Card ${index + 1}:`, card.querySelector('.card-title')?.textContent);";
echo "  });";
echo "});";
echo "</script>";
?>

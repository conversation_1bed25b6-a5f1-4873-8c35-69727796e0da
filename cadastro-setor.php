<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Cadastro de Setor</title>
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        .form-group {
            margin-bottom: 24px;
        }
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        input, select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        input:hover, select:hover {
            border-color: #9ca3af;
        }
        .search-btn {
            width: 48px;
            height: 41px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
            padding: 0 !important;
            margin-top: 0 !important;
        }
        .search-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        .search-btn:active {
            transform: translateY(0);
        }
        .search-icon {
            width: 24px !important;
            height: 24px !important;
            stroke: white !important;
            fill: none !important;
            display: block !important;
        }
        button[type="submit"] {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        button[type="submit"]:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        button[type="submit"]:active {
            transform: translateY(0);
        }
        .popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2563eb;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            border: 1px solid #1d4ed8;
        }
        .popup.show {
            opacity: 1;
            transform: translateY(0);
        }
        .popup.error {
            background: #dc2626;
            border-color: #b91c1c;
        }
        .popup-container {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffffff;
            padding: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            z-index: 1000;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .popup-container h2 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        .popup-container .search-box {
            margin-bottom: 20px;
        }
        .popup-container .search-box input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }
        .popup-container .table-wrapper {
            max-height: calc(80vh - 200px);
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .popup-container table {
            width: 100%;
            border-collapse: collapse;
        }
        .popup-container th, .popup-container td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        .popup-container th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
        }
        .popup-container tr:hover {
            background: #f9fafb;
        }
        .popup-container button {
            padding: 8px 16px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .popup-container button:hover {
            background: #1d4ed8;
        }
        .popup-container .close-btn {
            background: #6b7280;
            padding: 10px 20px;
            font-size: 14px;
        }
        .popup-container .close-btn:hover {
            background: #4b5563;
        }
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 999;
        }
        .popup-overlay.active {
            display: block;
        }
        .form-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            form {
                padding: 24px;
            }
            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>
<?php include 'conexao.php'; ?>
<?php
// Buscar próximo id do setor
$prox_id = 1;
$res = $conn->query("SELECT MAX(id) as max_id FROM setor");
if ($res && $row = $res->fetch_assoc()) {
    $prox_id = $row['max_id'] + 1;
    if ($prox_id < 1) $prox_id = 1;
}
$prox_id_formatado = str_pad($prox_id, 2, '0', STR_PAD_LEFT);
?>
<div class="content-container">
    <h1>Cadastro de Setor</h1>
    <form id="form-setor" action="salvar-setor.php" method="POST">
        <div class="form-group">
            <label for="codigo">Código do Setor</label>
            <input type="text" id="codigo" value="<?php echo htmlspecialchars($prox_id_formatado); ?>" readonly>
        </div>
        <div class="form-group">
            <label for="nome">Nome do Setor</label>
            <input type="text" id="nome" name="nome">
        </div>
        <div class="form-group">
            <label for="empresa">Empresa</label>
            <div class="form-row">
                <div style="flex: 1; display: flex; align-items: center; gap: 8px;">
                    <input type="text" id="empresa" name="empresa" readonly placeholder="Código da empresa">
                    <span id="nome_empresa_exibicao" style="color: #2563eb; font-weight: 500;"></span>
                </div>
                <button type="button" class="search-btn" onclick="abrirPopup()" title="Pesquisar Empresa">
                    <svg class="search-icon" viewBox="0 0 24 24">
                        <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                        <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>
        <button type="submit">Cadastrar Setor</button>
    </form>

<!-- Pop-up para pesquisa de empresas -->
<div id="popupOverlay" class="popup-overlay"></div>
<div id="popup" class="popup-container">
    <h2>Selecionar Empresa</h2>
    <div class="search-box">
        <input type="text" id="pesquisa" placeholder="Pesquisar empresa..." onkeyup="filtrarEmpresas()">
    </div>
    <div class="table-wrapper">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nome</th>
                    <th>Ação</th>
                </tr>
            </thead>
            <tbody id="lista-empresas">
                <?php
                $result = $conn->query("SELECT * FROM empresas WHERE status = 'ativo'");
                while ($row = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['codigo_empresa'] ?></td>
                        <td><?= $row['nome_empresa'] ?></td>
                        <td><button onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= htmlspecialchars($row['nome_empresa'], ENT_QUOTES) ?>')">Selecionar</button></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    </div>
    <button class="close-btn" onclick="fecharPopup()">Fechar</button>
</div>

<script>
    function abrirPopup() {
        document.getElementById('popupOverlay').classList.add('active');
        document.getElementById('popup').style.display = 'block';
    }
    function fecharPopup() {
        document.getElementById('popupOverlay').classList.remove('active');
        document.getElementById('popup').style.display = 'none';
    }
    function selecionarEmpresa(codigo, nome) {
        document.getElementById('empresa').value = codigo;
        document.getElementById('nome_empresa_exibicao').textContent = nome;
        fecharPopup();
    }
    function filtrarEmpresas() {
        let input = document.getElementById('pesquisa').value.toLowerCase();
        let linhas = document.getElementById('lista-empresas').getElementsByTagName('tr');
        for (let i = 0; i < linhas.length; i++) {
            let nome = linhas[i].getElementsByTagName('td')[1];
            if (nome) {
                let txtValue = nome.textContent || nome.innerText;
                if (txtValue.toLowerCase().indexOf(input) > -1) {
                    linhas[i].style.display = '';
                } else {
                    linhas[i].style.display = 'none';
                }
            }
        }
    }
    // Validação no envio do formulário
    const form = document.getElementById('form-setor');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        const inputs = form.querySelectorAll('input');
        inputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('field-error');
                // Adicionar placeholder de erro baseado no nome do campo
                const fieldName = input.getAttribute('name');
                let errorText = '';
                switch(fieldName) {
                    case 'nome':
                        errorText = 'Preencha o campo Nome do Setor';
                        break;
                    case 'empresa':
                        errorText = 'Selecione uma empresa';
                        break;
                    default:
                        errorText = 'Campo obrigatório';
                }
                input.setAttribute('placeholder', errorText);
            } else {
                input.classList.remove('field-error');
                const originalPlaceholder = input.getAttribute('data-original-placeholder');
                if (originalPlaceholder) {
                    input.setAttribute('placeholder', originalPlaceholder);
                } else {
                    input.removeAttribute('placeholder');
                }
            }
        });
        if (!isValid) {
            e.preventDefault();
            return;
        }
        // Envio AJAX
        e.preventDefault();
        const formData = new FormData(form);
        fetch('salvar-setor.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarPopupSucesso();
                form.reset();
                document.getElementById('nome_empresa_exibicao').textContent = '';
            } else {
                mostrarPopupErro();
            }
        })
        .catch(() => {
            mostrarPopupErro();
        });
    });
    // Remover classe de erro quando o usuário começar a digitar
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.classList.contains('field-error')) {
                this.classList.remove('field-error');
            }
        });
        input.addEventListener('change', function() {
            if (this.classList.contains('field-error')) {
                this.classList.remove('field-error');
            }
        });
    });
    function mostrarPopupSucesso() {
        const popup = document.getElementById('popup-msg');
        popup.textContent = 'Setor cadastrado com sucesso!';
        popup.classList.remove('error');
        popup.classList.add('show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
        // Atualizar o campo de código para o próximo id
        atualizarProximoCodigo();
    }
    // Função para buscar o próximo id do setor e atualizar o campo de código
    function atualizarProximoCodigo() {
        fetch('obter-proximo-id-setor.php')
            .then(response => response.json())
            .then(data => {
                if (data.proximo_id) {
                    document.getElementById('codigo').value = data.proximo_id;
                }
            });
    }
    function mostrarPopupErro() {
        const popup = document.getElementById('popup-msg');
        popup.textContent = 'Erro ao cadastrar setor.';
        popup.classList.add('error', 'show');
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }
</script>
<!-- Popup para mensagens -->
<div id="popup-msg" class="popup">Setor cadastrado com sucesso!</div>
</div>
</body>
</html> 
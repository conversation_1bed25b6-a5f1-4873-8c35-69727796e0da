<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Verificar se a coluna 'status' existe na tabela 'saidas_estoque'
$result = $conn->query("SHOW COLUMNS FROM saidas_estoque LIKE 'status'");
if ($result->num_rows === 0) {
    // A coluna não existe, vamos criá-la
    $conn->query("ALTER TABLE saidas_estoque ADD COLUMN status VARCHAR(20) DEFAULT 'ativa'");
}

// Consultar todas as saídas de estoque
$result = $conn->query("SELECT * FROM saidas_estoque ORDER BY id DESC");
if (!$result) { echo $conn->error; }
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Saídas</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px; /* 270px da topbar + 20px de margem */
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        
        .status-ativa { color: green; font-weight: bold; }
        .status-cancelada { color: red; font-weight: bold; }
        
        /* Estilos para o popup */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(34, 34, 34, 0.18);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .popup {
            background: #fff;
            padding: 32px 28px 24px;
            border-radius: 14px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.10);
            width: 95vw;
            max-width: 420px;
            min-width: 320px;
        }
        
        .popup h3 {
            margin-top: 0;
            color: #222;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 18px;
            letter-spacing: 0.01em;
        }
        
        .info-section {
            margin-bottom: 18px;
            padding: 0;
            background: none;
            border-radius: 0;
            border: none;
        }
        
        .info-section p {
            margin: 0 0 7px 0;
            color: #222;
            font-size: 15px;
        }
        
        .popup table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 18px;
            background: #fafbfc;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .popup th, .popup td {
            border: none;
            padding: 10px 8px;
            text-align: left;
            font-size: 14px;
        }
        
        .popup th {
            background: #f4f6fa;
            color: #2563eb;
            font-weight: 600;
        }
        
        .popup tr {
            border-bottom: 1px solid #f0f1f3;
        }
        
        .popup tr:last-child {
            border-bottom: none;
        }
        
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
        }
        
        .btn-modern {
            padding: 8px 18px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            background: #f4f6fa;
            color: #222;
            transition: background 0.2s, color 0.2s;
            box-shadow: none;
        }
        
        .btn-modern.btn-confirm {
            background: #2563eb;
            color: #fff;
        }
        
        .btn-modern.btn-cancel {
            background: #e5e7eb;
            color: #222;
        }
        
        .btn-modern.btn-danger {
            background: #f3f4f6;
            color: #d32f2f;
        }
        
        .btn-modern:hover {
            background: #e0e7ef;
        }
        
        /* Estilos para notificações */
        .notificacao {
            position: fixed;
            top: 20px;
            right: -300px;
            width: 280px;
            padding: 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transition: right 0.5s ease;
        }

        .notificacao.success { background-color: #4CAF50; }
        .notificacao.error { background-color: #f44336; }
        
        /* Responsividade para dispositivos móveis */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
        }
        
        /* Estilos modernos para a tabela principal */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        
        /* Status badges minimalistas */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-ativa {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-cancelada {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 6px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
        }
        
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        /* Título minimalista */
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .dropdown-menu {
            background: #f3f4f6 !important;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.10);
            min-width: 220px;
            z-index: 1002;
            padding: 6px 0;
        }
        .dropdown-item {
            width: 100%;
            background: none;
            border: none;
            color: #222;
            text-align: left;
            font-size: 15px;
            padding: 12px 20px;
            cursor: pointer;
            border-radius: 0;
            transition: background 0.18s;
        }
        .dropdown-item:hover {
            background: #e5e7eb !important;
        }
        .dropdown-item.btn-danger {
            color: #b91c1c;
        }
        .dropdown-toggle-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 6px;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
<h1>Registro de Saídas de Estoque</h1>

<div class="notificacao" id="notificacao"></div>

<div class="modern-table-container">
    <table class="modern-table">
        <thead>
            <tr>
                <th>Código</th>
                <th>Data</th>
                <th>Responsável</th>
                <th>Empresa Destino</th>
                <th>Motivo</th>
                <th>Status</th>
                <th>Ações</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($dados as $row): ?>
                <?php 
                    $status = isset($row['status']) ? $row['status'] : 'ativa';
                    $statusClass = $status === 'ativa' ? 'status-ativa' : 'status-cancelada';
                ?>
                <tr>
                    <td><?= $row['id'] ?></td>
                    <td><?= date('d/m/Y', strtotime($row['data_saida'])) ?></td>
                    <td><?= $row['responsavel'] ?></td>
                    <td><?= $row['empresa_destino'] ?></td>
                    <td><?= $row['motivo'] ?></td>
                    <td><span class="status-badge <?= $statusClass ?>"><?= ucfirst($status) ?></span></td>
                    <td><button class="btn-modern btn-confirm" onclick="verDetalhes(<?= $row['id'] ?>)">Ver detalhes</button></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Pop-up de detalhes -->
<div id="detalhesPopup" class="popup-overlay">
    <div class="popup">
        <div id="limiteAviso" style="display:none; margin-bottom:16px; padding:12px; border-radius:8px; font-weight:bold;"></div>
        <div style="display: flex; justify-content: space-between; align-items: flex-start; position:relative;">
            <h3>Saída #<span id="saida-id"></span></h3>
            <div style="display:flex; align-items:center; gap:2px;">
                <div class="dropdown-container" style="position: relative;">
                    <button id="dropdown-toggle" class="dropdown-toggle-btn" style="background: none; border: none; cursor: pointer; padding: 6px;">
                        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#374151" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9"/>
                        </svg>
                    </button>
                    <div id="dropdown-menu" class="dropdown-menu" style="display: none; position: absolute; right: 0; top: 36px; background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; box-shadow: 0 4px 16px rgba(0,0,0,0.10); min-width: 220px; z-index: 1002;">
                        <button class="dropdown-item" onclick="gerarPDFSaida()">Gerar PDF (Assinatura Manual)</button>
                        <button class="dropdown-item" onclick="abrirAssinaturaEletronica()">Assinatura Eletrônica</button>
                        <button class="dropdown-item" onclick="abrirUploadAssinatura()">Upload de Assinatura</button>
                        <button class="dropdown-item btn-danger" onclick="cancelarSaida()">Cancelar Saída</button>
                    </div>
                </div>
                <button id="btn-fechar-popup" title="Fechar" style="background:none; border:none; font-size:24px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; margin-left:2px; transition:background 0.2s, color 0.2s; display:flex; align-items:center; justify-content:center;">
                    &times;
                </button>
            </div>
        </div>
        
        <div class="info-section">
            <p><strong>Data:</strong> <span id="data-saida"></span></p>
            <p><strong>Responsável:</strong> <span id="responsavel"></span></p>
            <p><strong>Empresa Destino:</strong> <span id="empresa-destino"></span></p>
            <p><strong>Destinatário:</strong> <span id="destinatario"></span></p>
            <p><strong>Setor do Destinatário:</strong> <span id="setor-destinatario"></span></p>
            <p><strong>Motivo:</strong> <span id="motivo"></span></p>
            <p><strong>Observações:</strong> <span id="observacoes"></span></p>
            <p><strong>Status:</strong> <span id="status-saida"></span></p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Quantidade</th>
                </tr>
            </thead>
            <tbody id="produtos-lista">
                <!-- Será preenchido dinamicamente -->
            </tbody>
        </table>
    </div>
</div>

<!-- Pop-up de confirmação de cancelamento -->
<div id="cancelarPopup" class="popup-overlay">
    <div class="popup">
        <h3>Confirmar Cancelamento</h3>
        <p>Tem certeza que deseja cancelar esta saída? Esta ação irá:</p>
        <ul>
            <li>Devolver as quantidades ao estoque</li>
            <li>Marcar esta saída como cancelada</li>
        </ul>
        <p><strong>Esta ação não pode ser desfeita!</strong></p>
        
        <div class="form-buttons">
            <button class="btn-modern btn-cancel" onclick="fecharCancelamento()">Não, Voltar</button>
            <button class="btn-modern btn-danger" onclick="cancelarSaida()">Sim, Cancelar Saída</button>
        </div>
    </div>
</div>

<!-- Pop-up de upload de assinatura -->
<div id="uploadAssinaturaPopup" class="popup-overlay" style="display:none; z-index:2000;">
    <div class="popup" style="max-width:400px;">
        <h3>Upload de Assinatura</h3>
        <form id="formUploadAssinatura" enctype="multipart/form-data">
            <input type="hidden" name="saida_id" id="upload-saida-id">
            <input type="file" name="assinatura_arquivo" id="assinatura_arquivo" accept="image/png,image/jpeg,application/pdf" required style="margin-bottom:16px;">
            <div style="display:flex; gap:10px; justify-content:flex-end;">
                <button type="button" class="btn-modern btn-cancel" onclick="fecharUploadAssinatura()">Cancelar</button>
                <button type="submit" class="btn-modern btn-confirm">Enviar</button>
            </div>
            <div id="uploadAssinaturaMsg" style="margin-top:10px; color:#d32f2f; font-size:14px;"></div>
        </form>
    </div>
</div>
</div>

<script>
    let saidaAtualId = null;
    
    function verDetalhes(id) {
        saidaAtualId = id;
        
        // Fazer requisição AJAX para obter detalhes da saída
        fetch(`detalhes-saida.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                // Preencher informações básicas
                document.getElementById('saida-id').textContent = data.saida.id;
                document.getElementById('data-saida').textContent = data.saida.data_saida;
                document.getElementById('responsavel').textContent = data.saida.responsavel;
                document.getElementById('empresa-destino').textContent = data.saida.empresa_destino;
                document.getElementById('destinatario').textContent = data.saida.destinatario || '-';
                document.getElementById('setor-destinatario').textContent = data.saida.setor_destinatario || '-';
                document.getElementById('motivo').textContent = data.saida.motivo;
                document.getElementById('observacoes').textContent = data.saida.observacoes || '-';
                
                const status = data.saida.status || 'ativa';
                document.getElementById('status-saida').textContent = status.charAt(0).toUpperCase() + status.slice(1);
                document.getElementById('status-saida').className = status === 'ativa' ? 'status-ativa' : 'status-cancelada';
                
                // Preencher lista de produtos
                const produtosLista = document.getElementById('produtos-lista');
                produtosLista.innerHTML = '';
                
                data.produtos.forEach(produto => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${produto.codigo}</td>
                        <td>${produto.nome}</td>
                        <td>${produto.quantidade}</td>
                    `;
                    produtosLista.appendChild(tr);
                });
                
                // Após preencher a tabela de produtos, adicionar a barra de separação apenas uma vez
                let hrAssinatura = document.getElementById('hr-assinatura-separador');
                if (!hrAssinatura) {
                    hrAssinatura = document.createElement('hr');
                    hrAssinatura.id = 'hr-assinatura-separador';
                    hrAssinatura.style.margin = '18px 0 12px 0';
                    hrAssinatura.style.border = 'none';
                    hrAssinatura.style.borderTop = '1.5px solid #e5e7eb';
                    produtosLista.parentElement.parentElement.appendChild(hrAssinatura);
                }
                
                // Exibir assinatura se existir
                const assinaturaBoxId = 'assinatura-eletronica-box';
                let assinaturaBox = document.getElementById(assinaturaBoxId);
                if (!assinaturaBox) {
                    assinaturaBox = document.createElement('div');
                    assinaturaBox.id = assinaturaBoxId;
                    assinaturaBox.style.marginTop = '18px';
                    produtosLista.parentElement.parentElement.appendChild(assinaturaBox);
                }
                assinaturaBox.innerHTML = '';
                if (data.saida.tem_assinatura && data.saida.assinatura_url) {
                    // Exibir assinatura
                    if (data.saida.tipo_assinatura === 'imagem') {
                        assinaturaBox.innerHTML = `<div style='text-align:center;'><strong>Assinatura:</strong><br><img src='${data.saida.assinatura_url}' alt='Assinatura' style='max-width:320px; max-height:120px; border:1.5px solid #222; border-radius:8px; margin-top:8px;'></div>`;
                    } else if (data.saida.tipo_assinatura === 'pdf') {
                        assinaturaBox.innerHTML = `<div style='text-align:center;'><strong>Assinatura:</strong><br><a href='${data.saida.assinatura_url}' target='_blank' style='color:#2563eb; text-decoration:underline;'>Visualizar PDF da Assinatura</a></div>`;
                    }
                    if (data.saida.data_assinatura_eletronica) {
                        // Formatar para dd/mm/yyyy HH:MM
                        const dataAssinatura = new Date(data.saida.data_assinatura_eletronica.replace(' ', 'T'));
                        const dia = String(dataAssinatura.getDate()).padStart(2, '0');
                        const mes = String(dataAssinatura.getMonth() + 1).padStart(2, '0');
                        const ano = dataAssinatura.getFullYear();
                        const hora = String(dataAssinatura.getHours()).padStart(2, '0');
                        const min = String(dataAssinatura.getMinutes()).padStart(2, '0');
                        const dataFormatada = `${dia}/${mes}/${ano} ${hora}:${min}`;
                        assinaturaBox.innerHTML += `<div style='text-align:center; color:#555; font-size:14px; margin-top:8px;'>Assinado em: ${dataFormatada}</div>`;
                    }
                } else {
                    assinaturaBox.innerHTML = `<div style='text-align:center; color:#b91c1c; font-size:15px; margin-top:8px;'>Não possui nenhuma assinatura</div>`;
                }
                
                // Mostrar o popup
                document.getElementById('detalhesPopup').style.display = 'flex';

                const empresaLimite = data.saida.empresa_destino;
                const setorLimite = data.saida.setor_destinatario || '';
                let valorTotal = 0;
                if (data.produtos && data.produtos.length > 0) {
                    data.produtos.forEach(produto => {
                        valorTotal += parseInt(produto.quantidade) * 10; // Valor estimado
                    });
                }
                fetch(`verificar-limite-gasto.php?empresa=${encodeURIComponent(empresaLimite)}&setor=${encodeURIComponent(setorLimite)}&valor=${valorTotal}`)
                    .then(response => response.json())
                    .then(dataLimite => {
                        const avisoDiv = document.getElementById('limiteAviso');
                        if (dataLimite.success && dataLimite.tem_aviso) {
                            let mensagem = '';
                            let cor = '#f59e42';
                            dataLimite.avisos.forEach(aviso => {
                                if (aviso.tipo === 'excedido') {
                                    mensagem += `🚨 <b>LIMITE EXCEDIDO</b> para ${aviso.categoria.toUpperCase()}: ${aviso.nome}<br>`;
                                    mensagem += `Limite: R$ ${aviso.limite.toFixed(2)}<br>`;
                                    mensagem += `Gasto atual: R$ ${aviso.gasto_atual.toFixed(2)}<br>`;
                                    mensagem += `Gasto projetado: R$ ${aviso.gasto_projetado.toFixed(2)}<br>`;
                                    mensagem += `Excesso: R$ ${aviso.excesso.toFixed(2)}<br>`;
                                    mensagem += `Período: ${aviso.periodo}<br><br>`;
                                    cor = '#e53935';
                                } else {
                                    mensagem += `⚠️ <b>AVISO</b> para ${aviso.categoria.toUpperCase()}: ${aviso.nome}<br>`;
                                    mensagem += `Limite: R$ ${aviso.limite.toFixed(2)}<br>`;
                                    mensagem += `Gasto atual: R$ ${aviso.gasto_atual.toFixed(2)}<br>`;
                                    mensagem += `Gasto projetado: R$ ${aviso.gasto_projetado.toFixed(2)}<br>`;
                                    mensagem += `Saldo restante: R$ ${aviso.saldo_restante.toFixed(2)}<br>`;
                                    mensagem += `Período: ${aviso.periodo}<br><br>`;
                                }
                            });
                            avisoDiv.innerHTML = mensagem;
                            avisoDiv.style.display = 'block';
                            avisoDiv.style.background = cor;
                            avisoDiv.style.color = '#fff';
                        } else {
                            avisoDiv.style.display = 'none';
                        }
                    });
            })
            .catch(error => {
                console.error('Erro:', error);
                mostrarNotificacao('Erro ao carregar detalhes da saída', 'error');
            });
    }
    
    function fecharDetalhes() {
        document.getElementById('detalhesPopup').style.display = 'none';
    }
    
    function confirmarCancelamento() {
        document.getElementById('detalhesPopup').style.display = 'none';
        document.getElementById('cancelarPopup').style.display = 'flex';
    }
    
    function fecharCancelamento() {
        document.getElementById('cancelarPopup').style.display = 'none';
        document.getElementById('detalhesPopup').style.display = 'flex';
    }
    
    function cancelarSaida() {
        // Enviar solicitação para cancelar a saída
        fetch('cancelar-saida.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                saida_id: saidaAtualId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarNotificacao('Saída cancelada com sucesso!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                mostrarNotificacao('Erro ao cancelar saída: ' + data.message, 'error');
                fecharCancelamento();
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            mostrarNotificacao('Erro ao processar a solicitação.', 'error');
            fecharCancelamento();
        });
    }
    
    // Função para mostrar notificações
    function mostrarNotificacao(mensagem, tipo) {
        const notificacao = document.getElementById('notificacao');
        notificacao.textContent = mensagem;
        notificacao.className = 'notificacao ' + tipo;
        notificacao.style.right = '20px';
        
        setTimeout(() => {
            notificacao.style.right = '-300px';
        }, 5000);
    }
    
    // Fechar popups ao clicar fora deles
    document.getElementById('detalhesPopup').addEventListener('click', function(e) {
        if (e.target === this) {
            fecharDetalhes();
        }
    });
    
    document.getElementById('cancelarPopup').addEventListener('click', function(e) {
        if (e.target === this) {
            fecharCancelamento();
        }
    });

    // Dropdown minimalista
    const dropdownToggle = document.getElementById('dropdown-toggle');
    const dropdownMenu = document.getElementById('dropdown-menu');
    dropdownToggle.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
    });
    document.addEventListener('click', function() {
        dropdownMenu.style.display = 'none';
    });

    // Funções placeholder
    function gerarPDFSaida() {
        // Coletar dados do popup
        const saidaId = document.getElementById('saida-id').textContent;
        const dataSaida = document.getElementById('data-saida').textContent;
        const responsavel = document.getElementById('responsavel').textContent;
        const empresaDestino = document.getElementById('empresa-destino').textContent;
        const destinatario = document.getElementById('destinatario').textContent;
        const setorDestinatario = document.getElementById('setor-destinatario').textContent;
        const motivo = document.getElementById('motivo').textContent;
        const observacoes = document.getElementById('observacoes').textContent;
        const status = document.getElementById('status-saida').textContent;
        const produtos = Array.from(document.querySelectorAll('#produtos-lista tr')).map(tr =>
            Array.from(tr.children).map(td => td.textContent)
        );
        // Montar HTML para impressão
        let html = `
            <html><head><title>Saída #${saidaId}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; color: #222; }
                h2 { text-align: center; margin-bottom: 24px; }
                .info { margin-bottom: 24px; }
                .info p { margin: 4px 0; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 32px; }
                th, td { border: 1px solid #bbb; padding: 8px 12px; text-align: left; }
                th { background: #f3f4f6; }
                .assinatura { margin-top: 48px; }
                .assinatura-label { margin-bottom: 32px; font-size: 16px; }
                .linha-assinatura { margin-top: 40px; border-top: 1.5px solid #222; width: 320px; height: 2px; }
                .assinatura-nome { margin-top: 8px; font-size: 14px; color: #555; }
            </style></head><body>
            <h2>Saída de Estoque #${saidaId}</h2>
            <div class='info'>
                <p><strong>Data:</strong> ${dataSaida}</p>
                <p><strong>Responsável:</strong> ${responsavel}</p>
                <p><strong>Empresa Destino:</strong> ${empresaDestino}</p>
                <p><strong>Destinatário:</strong> ${destinatario}</p>
                <p><strong>Setor do Destinatário:</strong> ${setorDestinatario}</p>
                <p><strong>Motivo:</strong> ${motivo}</p>
                <p><strong>Observações:</strong> ${observacoes}</p>
                <p><strong>Status:</strong> ${status}</p>
            </div>
            <table><thead><tr><th>Código</th><th>Nome</th><th>Quantidade</th></tr></thead><tbody>
        `;
        produtos.forEach(cols => {
            html += `<tr><td>${cols[0]}</td><td>${cols[1]}</td><td>${cols[2]}</td></tr>`;
        });
        html += `</tbody></table>
            <div class='assinatura'>
                <div class='assinatura-label'>Assinatura do Destinatário:</div>
                <div class='linha-assinatura'></div>
                <div class='assinatura-nome'>&nbsp;</div>
            </div>
            </body></html>`;
        // Abrir nova janela e imprimir
        const win = window.open('', '_blank');
        win.document.write(html);
        win.document.close();
        win.focus();
        setTimeout(() => { win.print(); }, 500);
    }
    function abrirAssinaturaEletronica() {
        const saidaId = document.getElementById('saida-id').textContent;
        window.open('assinatura-eletronica-saida.php?id=' + encodeURIComponent(saidaId), '_blank');
    }
    function abrirUploadAssinatura() {
        document.getElementById('upload-saida-id').value = saidaAtualId;
        document.getElementById('assinatura_arquivo').value = '';
        document.getElementById('uploadAssinaturaMsg').textContent = '';
        document.getElementById('uploadAssinaturaPopup').style.display = 'flex';
    }
    function fecharUploadAssinatura() {
        document.getElementById('uploadAssinaturaPopup').style.display = 'none';
    }

    document.getElementById('formUploadAssinatura').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        document.getElementById('uploadAssinaturaMsg').textContent = '';
        fetch('upload-assinatura-eletronica.php', {
            method: 'POST',
            body: formData
        })
        .then(r => r.json())
        .then(resp => {
            if (resp.success) {
                document.getElementById('uploadAssinaturaMsg').style.color = '#2563eb';
                document.getElementById('uploadAssinaturaMsg').textContent = 'Assinatura enviada com sucesso!';
                setTimeout(() => { fecharUploadAssinatura(); verDetalhes(saidaAtualId); }, 1200);
            } else {
                document.getElementById('uploadAssinaturaMsg').style.color = '#d32f2f';
                document.getElementById('uploadAssinaturaMsg').textContent = resp.message || 'Erro ao enviar assinatura.';
            }
        })
        .catch(() => {
            document.getElementById('uploadAssinaturaMsg').style.color = '#d32f2f';
            document.getElementById('uploadAssinaturaMsg').textContent = 'Erro ao enviar assinatura.';
        });
    });

    document.getElementById('btn-fechar-popup').onclick = fecharDetalhes;
</script>
</body>
</html>




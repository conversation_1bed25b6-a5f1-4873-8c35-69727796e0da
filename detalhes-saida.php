<?php
include 'conexao.php';

header('Content-Type: application/json');

// Verificar se as colunas existem na tabela 'saidas_estoque'
$result = $conn->query("SHOW COLUMNS FROM saidas_estoque LIKE 'destinatario'");
if ($result->num_rows === 0) {
    // A coluna não existe, vamos criá-la
    $conn->query("ALTER TABLE saidas_estoque ADD COLUMN destinatario VARCHAR(100) DEFAULT ''");
}

$result = $conn->query("SHOW COLUMNS FROM saidas_estoque LIKE 'setor_destinatario'");
if ($result->num_rows === 0) {
    // A coluna não existe, vamos criá-la
    $conn->query("ALTER TABLE saidas_estoque ADD COLUMN setor_destinatario VARCHAR(100) DEFAULT ''");
}

// Verificar se a coluna 'status' existe na tabela 'saidas_estoque'
$result = $conn->query("SHOW COLUMNS FROM saidas_estoque LIKE 'status'");
if ($result->num_rows === 0) {
    // A coluna não existe, vamos criá-la
    $conn->query("ALTER TABLE saidas_estoque ADD COLUMN status VARCHAR(20) DEFAULT 'ativa'");
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id'])) {
    echo json_encode(['error' => 'ID não fornecido']);
    exit;
}

$saida_id = $_GET['id'];

// Obter informações da saída
$stmt = $conn->prepare("SELECT * FROM saidas_estoque WHERE id = ?");
$stmt->bind_param("i", $saida_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['error' => 'Saída não encontrada']);
    exit;
}

$saida = $result->fetch_assoc();

// Verificar se há assinatura eletrônica
$tem_assinatura = !empty($saida['assinatura_eletronica']);
$tipo_assinatura = null;
$assinatura_url = null;
if ($tem_assinatura) {
    // Detectar tipo (imagem ou PDF)
    $bin = $saida['assinatura_eletronica'];
    if (strpos(@finfo_buffer(finfo_open(), $bin, FILEINFO_MIME_TYPE), 'pdf') !== false) {
        $tipo_assinatura = 'pdf';
        $assinatura_url = 'ver-assinatura-eletronica.php?id=' . $saida_id;
    } else {
        $tipo_assinatura = 'imagem';
        $assinatura_url = 'ver-assinatura-eletronica.php?id=' . $saida_id;
    }
}

// Remover campos binários antes de retornar o JSON
unset($saida['assinatura_eletronica']);
// Se quiser, pode também remover a data, mas ela é texto:
// unset($saida['data_assinatura_eletronica']);

// Formatar a data
$saida['data_saida'] = date('d/m/Y', strtotime($saida['data_saida']));

// Obter produtos da saída com seus nomes
$stmt = $conn->prepare("
    SELECT ps.codigo, 
           COALESCE(ps.nome, p.nome, 'Produto removido') as nome, 
           ps.quantidade
    FROM produtos_saida ps
    LEFT JOIN produtos p ON ps.codigo = p.codigo
    WHERE ps.saida_id = ?
");
$stmt->bind_param("i", $saida_id);
$stmt->execute();
$produtos_result = $stmt->get_result();

$produtos = [];
while ($produto = $produtos_result->fetch_assoc()) {
    $produtos[] = $produto;
}

// Retornar os dados como JSON
$saida['tem_assinatura'] = $tem_assinatura;
$saida['tipo_assinatura'] = $tipo_assinatura;
$saida['assinatura_url'] = $assinatura_url;
echo json_encode([
    'saida' => $saida,
    'produtos' => $produtos
]);
?>




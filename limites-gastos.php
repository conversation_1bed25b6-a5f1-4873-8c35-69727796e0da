<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';
include_once 'periodos-limite-helper.php';

// Função PHP para mostrar o nome do período
function nomePeriodo($valor) {
    switch ((string)$valor) {
        case '1': return 'Mensal';
        case '2': return 'Bimestral';
        case '6': return 'Semestral';
        case '12': return 'Anual';
        default: return $valor;
    }
}

// Filtros
$where = [];
$params = [];
$types = "";

// Filtro de busca
if (!empty($_GET['search'])) {
    $where[] = "(CASE WHEN lg.tipo = 'empresa' THEN e.nome_empresa ELSE s.nome END LIKE ?)";
    $search = '%' . $_GET['search'] . '%';
    $params[] = $search;
    $types .= "s";
}
// Filtro de status
if (!empty($_GET['status_filter']) && $_GET['status_filter'] != 'todos') {
    $where[] = "lg.status = ?";
    $params[] = $_GET['status_filter'];
    $types .= "s";
}
// Filtro de tipo
if (!empty($_GET['tipo_filter']) && $_GET['tipo_filter'] != 'todos') {
    $where[] = "lg.tipo = ?";
    $params[] = $_GET['tipo_filter'];
    $types .= "s";
}
$sql_limites = "
    SELECT lg.*, 
           CASE WHEN lg.tipo = 'empresa' THEN e.nome_empresa ELSE s.nome END AS nome_alvo
    FROM limites_gastos lg
    LEFT JOIN empresas e ON lg.id_empresa = e.codigo_empresa
    LEFT JOIN setor s ON lg.id_setor = s.id
";
if ($where) {
    $sql_limites .= " WHERE " . implode(" AND ", $where);
}
$sql_limites .= " ORDER BY lg.tipo, nome_alvo";
$stmt = $conn->prepare($sql_limites);
if ($params) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result_limites = $stmt->get_result();

// Fechar período automaticamente para cada limite listado
if ($result_limites && $result_limites->num_rows > 0) {
    // Buscar todos os limites de novo para não consumir o result set
    $stmt->execute();
    $result_limites2 = $stmt->get_result();
    while ($limite = $result_limites2->fetch_assoc()) {
        $dataCriacao = isset($limite['criado_em']) ? $limite['criado_em'] : date('Y-m-d');
        fecharPeriodoSeNecessario($limite['id'], $dataCriacao, $limite['periodicidade']);
    }
    // Reexecutar a query para popular a tabela normalmente
    $stmt->execute();
    $result_limites = $stmt->get_result();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limites de Gastos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #374151;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .btn-add {
            background: #10b981;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin-bottom: 20px;
            transition: background 0.2s;
        }
        
        .btn-add:hover {
            background: #059669;
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
        
        td {
            color: #111827;
            font-size: 14px;
        }
        
        .btn-edit, .btn-delete {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            transition: background 0.2s;
        }
        
        .btn-edit {
            background: #3b82f6;
            color: white;
        }
        
        .btn-edit:hover {
            background: #2563eb;
        }
        
        .btn-delete {
            background: #ef4444;
            color: white;
        }
        
        .btn-delete:hover {
            background: #dc2626;
        }
        
        .status-ativo {
            color: #059669;
            font-weight: 500;
        }
        
        .status-inativo {
            color: #dc2626;
            font-weight: 500;
        }
        
        /* Popup de edição (copiado do produto) */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .popup-overlay.mostrar {
            display: flex !important;
        }
        .popup {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
        }
        .popup h3 {
            margin-top: 0;
            color: #111827;
            margin-bottom: 20px;
            font-size: 20px;
        }
        /* Abas do popup */
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        .popup-tab.active {
            background: white;
            color: #2563eb;
            border-color: #2563eb;
        }
        .popup-tab:hover {
            background: #f3f4f6;
        }
        .popup-tab-content {
            display: none;
        }
        .popup-tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
        }
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        /* Seção de ações */
        .actions-section {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 15px;
        }
        .actions-section h4 {
            margin-top: 0;
            color: #111827;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .search-bar-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            margin: 32px 0 18px 0;
            background: none;
            box-shadow: none;
            border: none;
            padding: 0;
            min-height: 50px;
        }
        .search-bar-wrapper {
            position: relative;
            width: 900px;
            max-width: 98vw;
            display: flex;
            align-items: flex-start;
            height: 50px;
        }
        .search-bar-input {
            width: 100%;
            padding: 13px 44px 13px 14px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 1.08em;
            outline: none;
            background: transparent;
            color: #222;
            transition: border 0.2s;
            box-shadow: none;
            height: 50px;
            margin-right: 0;
            display: flex;
            align-items: center;
        }
        .search-bar-btn-inside {
            position: absolute;
            right: 0px;
            top: 0px;
            height: 50px;
            width: 48px;
            background: none;
            color: #111;
            border: none;
            border-radius: 8px 8px 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 2;
        }
        .search-bar-btn-inside i {
            color: #111;
            font-size: 1.3em;
        }
        .filter-btn {
            height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin-left: 10px;
            margin-bottom: 10px;
            position: relative;
            background: none;
            border: none;
            border-radius: 8px;
            width: 40px;
            font-size: 20px;
            color: #222;
            cursor: pointer;
            transition: background 0.2s;
        }
        .filter-btn:hover {
            background: #f3f4f6;
        }
        .filter-popup-overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.4);
            z-index: 1001;
        }
        .filter-popup {
            display: none;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .filter-popup-form {
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .filter-popup-group label {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 6px;
        }
        .filter-popup-input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 15px;
            background: #fff;
            color: #111827;
        }
        .filter-popup-actions {
            display: flex;
            gap: 12px;
            margin-top: 10px;
        }
        /* Tabela moderna igual tabela-produtos.php */
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            font-size: 14px;
        }
        .modern-table thead {
            background: #f8fafc;
        }
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        /* Status badge igual tabela-produtos.php */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-ativo {
            background: #dcfce7;
            color: #166534;
        }
        .status-inativo {
            background: #fef2f2;
            color: #dc2626;
        }
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
            margin: 2px;
        }
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        .btn-warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        .btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <?php include 'topbar.php'; ?>
    
    <div class="content-container">
        <h1>Limites de Gastos</h1>
        <!-- Barra de busca e filtro -->
        <div class="search-bar-container">
            <form method="GET" id="searchForm" style="display: none;">
                <input type="text" class="search-bar-input" name="search" id="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="Buscar...">
                <button type="submit" class="search-bar-btn" title="Buscar">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <div style="display: flex; align-items: center; gap: 20px; justify-content: center;">
                <div class="search-bar-wrapper">
                    <input type="text" class="search-bar-input" id="searchVisible" placeholder="Buscar..." value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                    <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar">
                        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
                          <circle cx="11" cy="11" r="7" stroke="#111" stroke-width="2.2" fill="none"/>
                          <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="#111" stroke-width="2.2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
                <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>
        <!-- Popup de filtros moderno -->
        <div id="filterPopupOverlay" class="filter-popup-overlay"></div>
        <div id="filterPopup" class="filter-popup">
            <div class="filter-popup-header">
                <span>Filtrar por:</span>
                <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
            </div>
            <form method="get" class="filter-popup-form">
                <div class="filter-popup-group">
                    <label for="status_filter">Status:</label>
                    <select id="status_filter" name="status_filter" class="filter-popup-input">
                        <option value="todos" <?= ($_GET['status_filter'] ?? 'todos') == 'todos' ? 'selected' : '' ?>>Todos</option>
                        <option value="ativo" <?= ($_GET['status_filter'] ?? '') == 'ativo' ? 'selected' : '' ?>>Ativo</option>
                        <option value="inativo" <?= ($_GET['status_filter'] ?? '') == 'inativo' ? 'selected' : '' ?>>Inativo</option>
                    </select>
                </div>
                <div class="filter-popup-group">
                    <label for="tipo_filter">Tipo:</label>
                    <select id="tipo_filter" name="tipo_filter" class="filter-popup-input">
                        <option value="todos" <?= ($_GET['tipo_filter'] ?? 'todos') == 'todos' ? 'selected' : '' ?>>Todos</option>
                        <option value="empresa" <?= ($_GET['tipo_filter'] ?? '') == 'empresa' ? 'selected' : '' ?>>Empresa</option>
                        <option value="setor" <?= ($_GET['tipo_filter'] ?? '') == 'setor' ? 'selected' : '' ?>>Setor</option>
                    </select>
                </div>
                <div class="filter-popup-actions">
                    <a href="limites-gastos.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                    <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
                </div>
            </form>
        </div>
        <!-- Fim da barra de busca/filtro -->
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tipo</th>
                        <th>Nome</th>
                        <th>Valor Limite</th>
                        <th>Período</th>
                        <th>Saldo Aviso</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $result_limites->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo ucfirst($row['tipo']); ?></td>
                        <td><?php if ($row['tipo'] == 'empresa') { ?>
                            <span data-codigo="<?php echo htmlspecialchars($row['id_empresa']); ?>"><?php echo htmlspecialchars($row['nome_alvo']); ?></span>
                        <?php } else { ?>
                            <span data-codigo="<?php echo htmlspecialchars($row['id_setor']); ?>"><?php echo htmlspecialchars($row['nome_alvo']); ?></span>
                        <?php } ?></td>
                        <td>R$ <?php echo number_format($row['valor_limite'], 2, ',', '.'); ?></td>
                        <td><?php echo nomePeriodo($row['periodicidade']); ?></td>
                        <td>R$ <?php echo number_format($row['saldo_aviso'], 2, ',', '.'); ?></td>
                        <td><span class="status-badge <?php echo $row['status'] === 'ativo' ? 'status-ativo' : 'status-inativo'; ?>"><?php echo ucfirst($row['status']); ?></span></td>
                        <td>
                            <button class="btn-detalhes" onclick="abrirDetalhesLimite(<?php echo $row['id']; ?>)">Detalhes</button>
                        </td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn-modern btn-confirm" style="background:#2563eb; color:#fff; border-radius:6px; font-size:14px; padding:7px 18px; min-width:0; height:36px; width:180px; display:block; margin:0 auto; align-items:center; gap:7px;" onclick="abrirModal()">
                <i class="fas fa-plus"></i> Adicionar Limite
            </button>
        </div>
    </div>
    
    <!-- Overlay escurecido para modal de limite -->
    <div id="modalLimiteOverlay" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(17,17,17,0.35); z-index:2000;"></div>
    <!-- Modal para adicionar/editar limite -->
    <div id="modalLimite" class="modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); z-index:2001; background:rgba(0,0,0,0.01);">
        <div class="modal-content" style="min-width:320px; max-width:700px; width:98vw; min-height:220px; border-radius:16px; box-shadow:0 8px 32px rgba(0,0,0,0.18); padding:32px 28px 24px; background:#fff; position:relative;">
            <span class="close" onclick="fecharModal()" style="position:absolute; top:10px; right:10px; font-size:28px; color:#6b7280; cursor:pointer; border-radius:50%; width:36px; height:36px; display:flex; align-items:center; justify-content:center; background:none; border:none; transition:background 0.2s, color 0.2s;">&times;</span>
            <h2 id="modalTitle">Adicionar Limite</h2>
            <form id="formLimite">
                <input type="hidden" id="limite_id" name="id">
                <div class="form-group">
                    <label for="tipo">Tipo:</label>
                    <select id="tipo" name="tipo" required>
                        <option value="empresa">Empresa</option>
                        <option value="setor">Setor</option>
                    </select>
                </div>
                <div class="form-group" style="max-width:400px;">
                    <label for="codigo_alvo">Código:</label>
                    <div class="form-row" style="display:flex; align-items:center; gap:6px;">
                        <div class="form-group" style="margin-bottom:0; max-width:400px; flex:1;">
                            <input type="text" id="codigo_alvo" name="codigo_alvo" required style="background:#fff; max-width:400px; height:40px;" onblur="buscarNomePorCodigo()">
                        </div>
                        <button type="button" class="search-btn" onclick="abrirPopupBuscaAlvo()" title="Pesquisar" style="width:40px; height:40px; min-width:40px; min-height:40px; padding:0; display:flex; align-items:center; justify-content:center; background:#2563eb; border:none; border-radius:6px; height:40px;">
                            <svg class="search-icon" viewBox="0 0 24 24" width="24" height="24">
                                <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                                <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="form-group" style="max-width:377px;">
                    <label for="nome_alvo">Nome:</label>
                    <input type="text" id="nome_alvo" name="nome_alvo" readonly style="background:#fff; max-width:377px;">
                </div>
                <div class="form-group" style="max-width:377px;">
                    <label for="valor_limite">Valor Limite (R$):</label>
                    <input type="number" id="valor_limite" name="valor_limite" step="0.01" min="0" required style="max-width:377px;">
                </div>
                <div class="form-group" style="max-width:377px;">
                    <label for="saldo_aviso">Saldo para Aviso (R$):</label>
                    <input type="number" id="saldo_aviso" name="saldo_aviso" step="0.01" min="0" required style="max-width:377px;">
                </div>
                <div class="form-group" style="max-width:400px;">
                    <label for="periodicidade">Período:</label>
                    <select id="periodicidade" name="periodicidade" required style="max-width:400px;">
                        <option value="1">Mensal</option>
                        <option value="2">Bimestral</option>
                        <option value="6">Semestral</option>
                        <option value="12">Anual</option>
                    </select>
                </div>
                <div class="form-buttons">
                    <button type="submit" class="btn-save">Salvar</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Popup de busca -->
    <div class="popup-overlay" id="popupBuscaAlvo" style="display:none; z-index:2100;">
        <div class="popup">
            <div class="popup-header" style="display:flex; align-items:center; justify-content:space-between; gap:16px;">
                <h3 id="tituloBuscaAlvo" style="margin:0;">Selecionar</h3>
                <button class="popup-close" onclick="fecharPopupBuscaAlvo()" style="background:none; border:none; font-size:28px; color:#aaa; cursor:pointer; border-radius:50%; width:36px; height:36px; display:flex; align-items:center; justify-content:center; transition:background 0.2s, color 0.2s;">&times;</button>
            </div>
            <div class="popup-content">
                <input type="text" id="filtroBuscaAlvo" placeholder="Pesquisar..." onkeyup="filtrarBuscaAlvo()">
                <div class="popup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Nome</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody id="listaBuscaAlvo">
                            <!-- Preenchido via JS -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Popup de detalhes/edição -->
    <div class="popup-overlay" id="popupDetalhesLimite">
        <div class="popup" style="max-width:600px; width:90%; position:relative;">
            <div style="height:20px;"></div>
            <button type="button" class="popup-close" onclick="fecharDetalhesLimite()" style="position:absolute; top:0; right:10px; background:none; border:none; font-size:28px; color:#aaa; cursor:pointer; z-index:10; line-height:1; padding:0; width:32px; height:32px; margin-top:10px;">&times;</button>
            <div style="height:10px;"></div>
            <div style="display:flex; align-items:center; margin-bottom:20px; gap:8px;">
                <h3 style="margin:0; color:#111827; font-size:20px; flex:1;">Detalhes do Limite</h3>
                <button type="button" id="btnEditarLimite" onclick="habilitarEdicaoLimite()" title="Editar" style="background:none; border:none; cursor:pointer; padding:2px; margin-left:auto; width:28px; height:28px; display:flex; align-items:center; justify-content:center;">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 17.25V21h3.75l11.06-11.06-3.75-3.75L3 17.25zM20.71 7.04a1 1 0 0 0 0-1.41l-2.34-2.34a1 1 0 0 0-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z" fill="#111"/>
                    </svg>
                </button>
            </div>
            <div class="popup-tabs">
                <div class="popup-tab active" onclick="switchTabLimite('info')">Informações</div>
                <div class="popup-tab" onclick="switchTabLimite('gastos')">Gastos</div>
                <div class="popup-tab" onclick="switchTabLimite('actions')">Ações</div>
            </div>
            <div id="info-tab-limite" class="popup-tab-content active">
                <form id="formDetalhesLimite">
                    <input type="hidden" id="detalhes_id" name="id">
                    <input type="hidden" id="detalhes_criado_em" name="criado_em">
                    <input type="hidden" id="detalhes_data_inicio" name="data_inicio">
                    <input type="hidden" id="detalhes_data_fim" name="data_fim">
                    <div class="actions-section" style="padding:20px; background:#f8fafc; border-radius:8px; margin-bottom:15px;">
                        <div class="form-group"><label>Tipo:</label><input type="text" id="detalhes_tipo" readonly style="background:#f3f4f6 !important;"></div>
                        <div class="form-group"><label>Código:</label><input type="text" id="detalhes_codigo" readonly style="background:#f3f4f6 !important;"></div>
                        <div class="form-group"><label>Nome:</label><input type="text" id="detalhes_nome" readonly style="background:#f3f4f6 !important;"></div>
                        <div class="form-group"><label>Valor Limite (R$):</label><input type="number" id="detalhes_valor_limite" readonly style="background:#f3f4f6;"></div>
                        <div class="form-group"><label>Período:</label>
                            <select id="detalhes_periodicidade" disabled style="background:#f3f4f6;">
                                <option value="1">Mensal</option>
                                <option value="2">Bimestral</option>
                                <option value="6">Semestral</option>
                                <option value="12">Anual</option>
                            </select>
                        </div>
                        <div class="form-group"><label>Saldo para Aviso (R$):</label><input type="number" id="detalhes_saldo_aviso" readonly style="background:#f3f4f6;"></div>
                    </div>
                    <div class="form-buttons" id="botoesSalvarDetalhes" style="display:none;">
                        <button type="button" class="btn-cancel" onclick="cancelarEdicaoLimite()">Cancelar</button>
                        <button type="button" class="btn-save" onclick="salvarEdicaoLimite()">Salvar alterações</button>
                    </div>
                </form>
            </div>
            <div id="gastos-tab-limite" class="popup-tab-content" style="display:none;">
                <div style="display:flex; gap:18px; align-items:end; margin-bottom:18px; flex-wrap:wrap;">
                    <div class="form-group" style="min-width:180px;">
                        <label for="gastos_data_inicio">Data Inicial:</label>
                        <input type="date" id="gastos_data_inicio" name="gastos_data_inicio" readonly>
                    </div>
                    <div class="form-group" style="min-width:180px;">
                        <label for="gastos_data_fim">Data Final:</label>
                        <input type="date" id="gastos_data_fim" name="gastos_data_fim" readonly>
                    </div>
                    <button type="button" class="btn-modern btn-warning" style="height:40px; margin-top:24px;" onclick="abrirPopupPeriodosLimite()"><i class="fas fa-history"></i> Registros de Períodos</button>
                </div>
                <div class="modern-table-container" style="margin-bottom:0;">
                    <table class="modern-table" id="tabelaGastosLimite">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Origem</th>
                                <th>Produto</th>
                                <th>Quantidade</th>
                                <th>Valor</th>
                                <th>Valor Bruto</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="gastosLimiteBody">
                            <tr><td colspan="7" style="text-align:center; color:#888;">Nenhum gasto encontrado</td></tr>
                        </tbody>
                        <tfoot>
                            <tr style="font-weight:bold; background:#f8fafc;">
                                <td colspan="3" style="text-align:right;">Totais:</td>
                                <td id="gastosTotalQtd">0</td>
                                <td id="gastosTotalValor">R$ 0,00</td>
                                <td id="gastosTotalValorBruto">R$ 0,00</td>
                                <td id="gastosTotalAviso"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div id="actions-tab-limite" class="popup-tab-content">
                <div class="actions-section">
                    <h4 style="margin-top:0; color:#111827; font-size:16px; margin-bottom:15px;">Ações do Limite</h4>
                    <p>Selecione uma ação para este limite:</p>
                    <div class="action-buttons" style="display:flex; gap:10px; flex-wrap:wrap;">
                        <button class="btn-modern btn-danger" onclick="excluirLimiteDetalhes()"><i class="fas fa-trash"></i> Excluir Limite</button>
                        <button id="btnAtivarInativarLimite" class="btn-modern btn-warning" onclick="mostrarConfirmacaoStatus()"><i class="fas fa-pause"></i> Inativar Limite</button>
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-modern btn-cancel" onclick="fecharDetalhesLimite()">Fechar</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Popup de confirmação de status -->
    <div id="popupConfirmacaoStatus" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.25); z-index:2000; justify-content:center; align-items:center;">
        <div style="background:#fff; border-radius:12px; box-shadow:0 8px 32px rgba(0,0,0,0.18); padding:32px 28px 24px; min-width:320px; max-width:95vw; min-height:120px; display:flex; flex-direction:column; align-items:center; position:relative;">
            <span id="textoConfirmacaoStatus" style="font-size:18px; color:#222; margin-bottom:24px; text-align:center;"></span>
            <div style="display:flex; gap:18px;">
                <button id="btnCancelarConfirmacaoStatus" class="btn-modern btn-cancel" style="background:#f44336; color:#fff; border:none; border-radius:6px; padding:10px 18px; font-size:14px; font-weight:600;" onclick="fecharPopupConfirmacaoStatus()">Cancelar</button>
                <button id="btnConfirmarStatus" class="btn-modern btn-confirm" style="background:#2563eb; color:#fff; border:none; border-radius:6px; padding:10px 18px; font-size:14px; font-weight:600;">Confirmar</button>
            </div>
        </div>
    </div>
    
    <!-- Popup de registros de períodos -->
    <div id="popupPeriodosLimite" class="popup-overlay" style="display:none; z-index:3000;">
        <div class="popup" style="max-width:900px; width:96vw; position:relative;">
            <button type="button" class="popup-close" onclick="fecharPopupPeriodosLimite()" style="position:absolute; top:0; right:10px; background:none; border:none; font-size:28px; color:#aaa; cursor:pointer; z-index:10; line-height:1; padding:0; width:32px; height:32px; margin-top:10px;">&times;</button>
            <h3 style="margin:0 0 18px 0; color:#111827; font-size:20px;">Registros de Períodos</h3>
            <div id="periodosLimiteConteudo">Carregando...</div>
        </div>
    </div>
    
    <script>
        let editando = false;
        let statusLimiteAtual = 'ativo';
        
        function abrirModal() {
            editando = false;
            document.getElementById('formLimite').reset();
            document.getElementById('modalTitle').textContent = 'Adicionar Limite';
            document.getElementById('modalLimite').style.display = 'block';
            document.getElementById('modalLimiteOverlay').style.display = 'block';
        }
        
        function fecharModal() {
            document.getElementById('modalLimite').style.display = 'none';
            document.getElementById('modalLimiteOverlay').style.display = 'none';
        }
        
        function editarLimite(id) {
            editando = true;
            // Buscar dados do limite via AJAX
            fetch('obter-limite-gasto.php?id=' + id)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const limite = data.limite;
                        document.getElementById('modalTitle').textContent = 'Editar Limite';
                        document.getElementById('limite_id').value = limite.id;
                        document.getElementById('tipo').value = limite.tipo;
                        document.getElementById('codigo_alvo').value = limite.nome;
                        document.getElementById('valor_limite').value = limite.valor_limite;
                        document.getElementById('periodicidade').value = limite.periodo;
                        document.getElementById('saldo_aviso').value = limite.saldo_aviso;
                        document.getElementById('data_inicio').value = limite.data_inicio;
                        document.getElementById('data_fim').value = limite.data_fim;
                        document.getElementById('status').value = limite.status;
                        document.getElementById('modalLimite').style.display = 'block';
                    } else {
                        alert('Erro ao carregar dados do limite');
                    }
                });
        }
        
        function excluirLimite(id) {
            if (confirm('Tem certeza que deseja excluir este limite?')) {
                fetch('excluir-limite-gasto.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'id=' + id
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Erro ao excluir limite: ' + data.message);
                    }
                });
            }
        }
        
        // Fechar modal ao clicar fora dele
        window.onclick = function(event) {
            const modal = document.getElementById('modalLimite');
            if (event.target === modal) {
                fecharModal();
            }
        }
        
        // Submissão do formulário
        document.getElementById('formLimite').onsubmit = function(e) {
            e.preventDefault();
            // Montar FormData manualmente para garantir que só envie o código correto
            const tipo = document.getElementById('tipo').value;
            const codigo = document.getElementById('codigo_alvo').value;
            const valor_limite = document.getElementById('valor_limite').value;
            const periodicidade = document.getElementById('periodicidade').value;
            const saldo_aviso = document.getElementById('saldo_aviso').value;
            const id_empresa = tipo === 'empresa' ? codigo : '';
            const id_setor = tipo === 'setor' ? codigo : '';
            const formData = new FormData();
            formData.append('tipo', tipo);
            formData.append('id_empresa', id_empresa);
            formData.append('id_setor', id_setor);
            formData.append('valor_limite', valor_limite);
            formData.append('periodicidade', periodicidade);
            formData.append('saldo_aviso', saldo_aviso);
            fetch('salvar-limite-gasto.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                fecharModal();
                if (data.success) {
                    location.reload();
                } else {
                    alert('Erro: ' + data.message);
                }
            });
        };

        function abrirPopupBuscaAlvo() {
            const tipo = document.getElementById('tipo').value;
            document.getElementById('popupBuscaAlvo').style.display = 'flex';
            document.getElementById('popupBuscaAlvo').style.zIndex = 2100;
            document.getElementById('modalLimite').style.zIndex = 2001;
            document.getElementById('modalLimiteOverlay').style.zIndex = 2000;
            document.getElementById('tituloBuscaAlvo').textContent = tipo === 'empresa' ? 'Selecionar Empresa' : 'Selecionar Setor';
            document.getElementById('filtroBuscaAlvo').value = '';
            carregarListaBuscaAlvo('');
        }
        function fecharPopupBuscaAlvo() {
            document.getElementById('popupBuscaAlvo').style.display = 'none';
        }
        function carregarListaBuscaAlvo(filtro) {
            const tipo = document.getElementById('tipo').value;
            let url = tipo === 'empresa' ? 'buscar-empresas.php' : 'buscar-setores.php';
            fetch(url + '?termo=' + encodeURIComponent(filtro))
                .then(r => r.json())
                .then(lista => {
                    let html = '';
                    if (lista.length === 0) {
                        html = '<tr><td colspan="3" style="text-align:center;color:#888;">Nenhum resultado encontrado</td></tr>';
                    } else {
                        lista.forEach(item => {
                            html += `<tr><td>${item.codigo}</td><td>${item.nome}</td><td><button class='btn-select' onclick='selecionarAlvo("${item.codigo}", "${item.nome.replace(/"/g, '&quot;')}")'>Selecionar</button></td></tr>`;
                        });
                    }
                    document.getElementById('listaBuscaAlvo').innerHTML = html;
                });
        }
        function filtrarBuscaAlvo() {
            carregarListaBuscaAlvo(document.getElementById('filtroBuscaAlvo').value);
        }
        function selecionarAlvo(codigo, nome) {
            document.getElementById('codigo_alvo').value = codigo;
            document.getElementById('nome_alvo').value = nome;
            fecharPopupBuscaAlvo();
        }
        // Limpar campos ao trocar tipo
        if (document.getElementById('tipo')) {
            document.getElementById('tipo').onchange = function() {
                document.getElementById('codigo_alvo').value = '';
                document.getElementById('nome_alvo').value = '';
            };
        }

        // Função para limpar todos os campos de detalhes
        function limparCamposDetalhes() {
            const campos = [
                'detalhes_id', 'detalhes_tipo', 'detalhes_codigo', 'detalhes_nome',
                'detalhes_valor_limite', 'detalhes_periodicidade', 'detalhes_saldo_aviso',
                'detalhes_criado_em', 'detalhes_data_inicio', 'detalhes_data_fim',
                'gastos_data_inicio', 'gastos_data_fim'
            ];

            campos.forEach(campo => {
                const elemento = document.getElementById(campo);
                if (elemento) elemento.value = '';
            });

            // Limpar variáveis globais
            window.limiteAtual = null;
            window.limiteDataInicio = '';
            window.limiteDataFim = '';
        }

        // Função para atualizar campos de gastos com dados do limite atual
        function atualizarCamposGastos() {
            if (window.limiteAtual) {
                const campoInicio = document.getElementById('gastos_data_inicio');
                const campoFim = document.getElementById('gastos_data_fim');

                if (campoInicio) campoInicio.value = window.limiteAtual.data_inicio || '';
                if (campoFim) campoFim.value = window.limiteAtual.data_fim || '';

                console.log('📅 Campos de gastos atualizados:', {
                    inicio: window.limiteAtual.data_inicio,
                    fim: window.limiteAtual.data_fim
                });
            }
        }

        function abrirDetalhesLimite(id) {
            console.log('🔍 Abrindo detalhes do limite:', id);

            // Limpar TODOS os campos primeiro
            limparCamposDetalhes();

            fetch('obter-limite-gasto.php?id=' + id)
                .then(r => r.json())
                .then(data => {
                    console.log('📋 Dados do limite recebidos:', data);
                    if (data.success) {
                        const l = data.limite;

                        // Preencher campos básicos
                        document.getElementById('detalhes_id').value = l.id;
                        document.getElementById('detalhes_tipo').value = l.tipo;
                        document.getElementById('detalhes_codigo').value = l.tipo === 'empresa' ? l.id_empresa : l.id_setor;
                        document.getElementById('detalhes_nome').value = l.nome_alvo || l.nome || '';
                        document.getElementById('detalhes_valor_limite').value = l.valor_limite;
                        document.getElementById('detalhes_periodicidade').value = l.periodicidade || l.periodo;
                        document.getElementById('detalhes_saldo_aviso').value = l.saldo_aviso;
                        document.getElementById('detalhes_criado_em').value = l.data_criacao || '';
                        statusLimiteAtual = l.status || 'ativo';

                        // Buscar período vigente via AJAX
                        console.log('🔍 Buscando período vigente para limite:', l.id);
                        fetch('obter-periodo-vigente.php?id_limite=' + l.id)
                            .then(resp => resp.json())
                            .then(periodo => {
                                console.log('📅 Período vigente recebido:', periodo);
                                if (periodo.success) {
                                    document.getElementById('detalhes_data_inicio').value = periodo.data_inicio;
                                    document.getElementById('detalhes_data_fim').value = periodo.data_fim;

                                    // Armazenar dados do limite atual
                                    window.limiteAtual = {
                                        id: l.id,
                                        data_inicio: periodo.data_inicio,
                                        data_fim: periodo.data_fim
                                    };

                                    // Atualizar campos de gastos
                                    atualizarCamposGastos();
                                } else {
                                    console.log('⚠️ Erro ao obter período vigente:', periodo.message);
                                    document.getElementById('detalhes_data_inicio').value = '';
                                    document.getElementById('detalhes_data_fim').value = '';

                                    window.limiteAtual = {
                                        id: l.id,
                                        data_inicio: '',
                                        data_fim: ''
                                    };
                                }
                            })
                            .catch(error => {
                                console.error('❌ Erro ao buscar período vigente:', error);
                                document.getElementById('detalhes_data_inicio').value = '';
                                document.getElementById('detalhes_data_fim').value = '';
                            });
                        document.getElementById('detalhes_valor_limite').readOnly = true;
                        document.getElementById('detalhes_valor_limite').style.background = '#f3f4f6';
                        document.getElementById('detalhes_periodicidade').disabled = true;
                        document.getElementById('detalhes_periodicidade').style.background = '#f3f4f6';
                        document.getElementById('detalhes_saldo_aviso').readOnly = true;
                        document.getElementById('detalhes_saldo_aviso').style.background = '#f3f4f6';
                        document.getElementById('botoesSalvarDetalhes').style.display = 'none';
                        document.getElementById('popupDetalhesLimite').classList.add('mostrar');
                        const btn = document.getElementById('btnAtivarInativarLimite');
                        if (statusLimiteAtual === 'ativo') {
                            btn.innerHTML = '<i class="fas fa-pause"></i> Inativar Limite';
                            btn.classList.remove('btn-success');
                            btn.classList.add('btn-warning');
                        } else {
                            btn.innerHTML = '<i class="fas fa-play"></i> Ativar Limite';
                            btn.classList.remove('btn-warning');
                            btn.classList.add('btn-success');
                        }
                        carregarGastosLimite();
                    }
                });
        }
        function fecharDetalhesLimite() {
            document.getElementById('popupDetalhesLimite').classList.remove('mostrar');
        }
        function habilitarEdicaoLimite() {
            document.getElementById('detalhes_valor_limite').readOnly = false;
            document.getElementById('detalhes_valor_limite').style.background = '#fff';
            document.getElementById('detalhes_periodicidade').disabled = false;
            document.getElementById('detalhes_periodicidade').style.background = '#fff';
            document.getElementById('detalhes_saldo_aviso').readOnly = false;
            document.getElementById('detalhes_saldo_aviso').style.background = '#fff';
            document.getElementById('botoesSalvarDetalhes').style.display = '';
        }
        function cancelarEdicaoLimite() {
            abrirDetalhesLimite(document.getElementById('detalhes_id').value);
        }
        function salvarEdicaoLimite() {
            const id = document.getElementById('detalhes_id').value;
            const valor_limite = document.getElementById('detalhes_valor_limite').value;
            const periodicidade = document.getElementById('detalhes_periodicidade').value;
            const saldo_aviso = document.getElementById('detalhes_saldo_aviso').value;

            // Validação básica no frontend
            if (!id || !valor_limite || !periodicidade || !saldo_aviso) {
                alert('Preencha todos os campos obrigatórios.');
                return;
            }

            const params = new URLSearchParams();
            params.append('id', id);
            params.append('valor_limite', valor_limite);
            params.append('periodicidade', periodicidade);
            params.append('saldo_aviso', saldo_aviso);

            fetch('atualizar-limite-gasto.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: params.toString()
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        fecharDetalhesLimite();
                        location.reload();
                    } else {
                        alert('Erro: ' + data.message);
                    }
                } catch (e) {
                    console.error('Resposta inesperada do servidor:', text);
                    alert('Erro inesperado do servidor. Veja o console para detalhes.');
                }
            })
            .catch(error => {
                console.error('Erro na requisição:', error);
                alert('Erro na requisição: ' + error);
            });
        }
        function excluirLimiteDetalhes() {
            if (confirm('Deseja realmente excluir este limite?')) {
                const id = document.getElementById('detalhes_id').value;
                fetch('excluir-limite-gasto.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: 'id=' + id
                })
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        fecharDetalhesLimite();
                        location.reload();
                    } else if (data.gastos && data.gastos.length > 0) {
                        let msg = 'Não é possível excluir este limite pois existem gastos vinculados.\n\n';
                        msg += 'Produtos vinculados:\n';
                        data.gastos.forEach(g => {
                            msg += `Produto: ${g.nome_produto || '-'} | Quantidade: ${g.valor_total || '-'} | Valor: R$ ${g.valor_total.toFixed(2)}\n`;
                        });
                        msg += `\nValor total: R$ ${data.valor_total.toFixed(2)}`;
                        alert(msg);
                    } else {
                        alert('Erro ao excluir limite: ' + data.message);
                    }
                });
            }
        }
        function mostrarConfirmacaoStatus() {
            const texto = statusLimiteAtual === 'ativo' ? 'Deseja realmente inativar este limite?' : 'Deseja realmente ativar este limite?';
            document.getElementById('textoConfirmacaoStatus').textContent = texto;
            document.getElementById('popupConfirmacaoStatus').style.display = 'flex';
        }
        function fecharPopupConfirmacaoStatus() {
            document.getElementById('popupConfirmacaoStatus').style.display = 'none';
        }
        function alternarStatusLimite() {
            const id = document.getElementById('detalhes_id').value;
            const novoStatus = statusLimiteAtual === 'ativo' ? 'inativo' : 'ativo';
            fetch('atualizar-limite-gasto.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: `id=${id}&status=${novoStatus}`
            })
            .then(r => r.json())
            .then(data => {
                fecharPopupConfirmacaoStatus();
                if (data.success) {
                    fecharDetalhesLimite();
                    location.reload();
                } else {
                    alert('Erro ao alterar status: ' + data.message);
                }
            });
        }
        function switchTabLimite(tab) {
            document.querySelectorAll('.popup-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.popup-tab-content').forEach(c => c.classList.remove('active'));
            if (tab === 'info') {
                document.querySelector('.popup-tab:nth-child(1)').classList.add('active');
                document.getElementById('info-tab-limite').classList.add('active');
                document.getElementById('info-tab-limite').style.display = '';
                document.getElementById('gastos-tab-limite').style.display = 'none';
                document.getElementById('actions-tab-limite').style.display = 'none';
            } else if (tab === 'gastos') {
                document.querySelector('.popup-tab:nth-child(2)').classList.add('active');
                document.getElementById('gastos-tab-limite').classList.add('active');
                document.getElementById('info-tab-limite').style.display = 'none';
                document.getElementById('gastos-tab-limite').style.display = '';
                document.getElementById('actions-tab-limite').style.display = 'none';
                preencherDatasPeriodoVigente(); // Preencher datas ao abrir a aba de gastos
            } else if (tab === 'actions') {
                document.querySelector('.popup-tab:nth-child(3)').classList.add('active');
                document.getElementById('actions-tab-limite').classList.add('active');
                document.getElementById('info-tab-limite').style.display = 'none';
                document.getElementById('gastos-tab-limite').style.display = 'none';
                document.getElementById('actions-tab-limite').style.display = '';
            }
        }
        function toggleFiltroTudoGastos() {
            const tudo = document.getElementById('gastos_tudo').checked;
            document.getElementById('gastos_data_inicio').disabled = tudo;
            document.getElementById('gastos_data_fim').disabled = tudo;
        }
        function carregarGastosLimite() {
            const id = document.getElementById('detalhes_id').value;
            const tipo = document.getElementById('detalhes_tipo').value.toLowerCase();
            const codigo = document.getElementById('detalhes_codigo').value;
            const tudoCheckbox = document.getElementById('gastos_tudo');
            const data_inicio = tudoCheckbox && tudoCheckbox.checked ? '' : document.getElementById('gastos_data_inicio').value;
            const data_fim = tudoCheckbox && tudoCheckbox.checked ? '' : document.getElementById('gastos_data_fim').value;
            const saldoAviso = parseFloat(document.getElementById('detalhes_saldo_aviso').value.replace(',', '.')) || 0;
            const valorLimite = parseFloat(document.getElementById('detalhes_valor_limite').value.replace(',', '.')) || 0;
            // Debug: mostrar parâmetros enviados para gastos-por-limite.php
            console.log('[carregarGastosLimite] tipo:', tipo, '| codigo:', codigo, '| data_inicio:', data_inicio, '| data_fim:', data_fim);

            const params = new URLSearchParams();
            params.append('tipo', tipo);
            params.append('codigo', codigo);
            params.append('data_inicio', data_inicio);
            params.append('data_fim', data_fim);

            fetch('gastos-por-limite.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: params.toString()
            })
            .then(r => r.json())
            .then(data => {
                const tbody = document.getElementById('gastosLimiteBody');
                tbody.innerHTML = '';
                let totalQtd = 0, totalValor = 0, totalValorBruto = 0;
                if (data.success && data.gastos.length > 0) {
                    data.gastos.forEach(g => {
                        const valor = parseFloat(g.valor) || 0;
                        const valorBruto = parseFloat(g.valor_bruto) || 0;
                        const qtd = parseInt(g.quantidade) || 0;
                        totalQtd += qtd;
                        // Os valores já vêm calculados do backend, apenas somar
                        totalValor += valor;
                        totalValorBruto += valorBruto;
                        let btnDetalhes = '';
                        if (g.tipo_origem === 'saida') {
                            btnDetalhes = `<button class='btn-modern btn-info' style='padding:4px 10px;font-size:13px;' onclick='window.open("detalhes-saida.php?id=${g.id_origem}", "_blank")'>Ver Detalhes</button>`;
                        } else if (g.tipo_origem === 'saida_estoque') {
                            btnDetalhes = `<button class='btn-modern btn-info' style='padding:4px 10px;font-size:13px;' onclick='window.open("detalhes-saida-estoque.php?id=${g.id_origem}", "_blank")'>Ver Detalhes</button>`;
                        } else if (g.tipo_origem === 'requisicao') {
                            btnDetalhes = `<button class='btn-modern btn-info' style='padding:4px 10px;font-size:13px;' onclick='window.open("detalhes-solicitacao.php?tipo=requisicao&id=${g.id_origem}", "_blank")'>Ver Detalhes</button>`;
                        } else if (g.tipo_origem === 'pedido_mensal') {
                            btnDetalhes = `<button class='btn-modern btn-info' style='padding:4px 10px;font-size:13px;' onclick='window.open("detalhes-solicitacao.php?tipo=pedido_mensal&id=${g.id_origem}", "_blank")'>Ver Detalhes</button>`;
                        }
                        tbody.innerHTML += `<tr>
                            <td>${g.data ? g.data.split(' ')[0] : ''}</td>
                            <td>${g.origem}</td>
                            <td>${g.produto}</td>
                            <td>${qtd}</td>
                            <td>R$ ${valor.toLocaleString('pt-BR', {minimumFractionDigits:2})}</td>
                            <td>R$ ${valorBruto.toLocaleString('pt-BR', {minimumFractionDigits:2})}</td>
                            <td>${btnDetalhes}</td>
                        </tr>`;
                    });
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align:center; color:#888;">Nenhum gasto encontrado</td></tr>';
                }
                document.getElementById('gastosTotalQtd').textContent = totalQtd;
                document.getElementById('gastosTotalValor').textContent = 'R$ ' + totalValor.toLocaleString('pt-BR', {minimumFractionDigits:2});
                document.getElementById('gastosTotalValorBruto').textContent = 'R$ ' + totalValorBruto.toLocaleString('pt-BR', {minimumFractionDigits:2});
                // Aviso de saldo para aviso
                const totalAvisoTd = document.getElementById('gastosTotalAviso');
                let avisoSaldo = '';
                let avisoLimite = '';
                if (valorLimite > 0 && totalValor >= valorLimite) {
                    // Limite excedido: ícone vermelho
                    avisoLimite = '<span class="aviso-limite" style="color:#dc2626;font-size:18px;vertical-align:middle;display:inline-block;" title="Limite de Gasto Excedido!"><i class="fas fa-exclamation-circle"></i></span>';
                } else if (saldoAviso > 0 && totalValor >= saldoAviso) {
                    // Aviso: ícone amarelo
                    avisoSaldo = '<span class="aviso-saldo" style="color:#f59e42;font-size:18px;vertical-align:middle;display:inline-block;" title="Atenção: Saldo para Aviso atingido!"><i class="fas fa-exclamation-triangle"></i></span>';
                }
                totalAvisoTd.innerHTML = avisoLimite || avisoSaldo;

                // Avisos
                // Remover qualquer aviso anterior
                const valorLimiteInput = document.getElementById('detalhes_valor_limite');
                if (valorLimiteInput.nextElementSibling && valorLimiteInput.nextElementSibling.tagName === 'SPAN') {
                    valorLimiteInput.nextElementSibling.remove();
                }
                // Não adicionar nenhum aviso visual ao lado do campo de valor limite
            })
            .catch(() => {
                document.getElementById('gastosLimiteBody').innerHTML = '<tr><td colspan="7" style="text-align:center; color:#888;">Erro ao buscar gastos</td></tr>';
                document.getElementById('gastosTotalQtd').textContent = '0';
                document.getElementById('gastosTotalValor').textContent = 'R$ 0,00';
                document.getElementById('gastosTotalValorBruto').textContent = 'R$ 0,00';
                document.getElementById('gastosTotalAviso').innerHTML = ''; // Limpar aviso de saldo
            });
        }

        // Filtro popup abrir/fechar
        const filterBtn = document.getElementById('filterBtn');
        const filterPopup = document.getElementById('filterPopup');
        const filterPopupOverlay = document.getElementById('filterPopupOverlay');
        const closeFilterPopup = document.getElementById('closeFilterPopup');

        filterBtn.addEventListener('click', function() {
            filterPopup.style.display = 'block';
            filterPopupOverlay.style.display = 'block';
        });
        closeFilterPopup.addEventListener('click', function() {
            filterPopup.style.display = 'none';
            filterPopupOverlay.style.display = 'none';
        });
        filterPopupOverlay.addEventListener('click', function() {
            filterPopup.style.display = 'none';
            filterPopupOverlay.style.display = 'none';
        });

        // --- INÍCIO: CORREÇÃO DA BARRA DE PESQUISA ---
        // Ao clicar na lupa ou pressionar Enter no campo visível, submeter o formulário de busca
        const searchVisible = document.getElementById('searchVisible');
        const searchVisibleBtn = document.getElementById('searchVisibleBtn');
        const searchForm = document.getElementById('searchForm');
        const searchHidden = document.getElementById('search');
        // Submeter ao clicar na lupa
        searchVisibleBtn.addEventListener('click', function() {
            searchHidden.value = searchVisible.value;
            searchForm.submit();
        });
        // Submeter ao pressionar Enter no campo visível
        searchVisible.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchHidden.value = searchVisible.value;
                searchForm.submit();
            }
        });
        // --- FIM: CORREÇÃO DA BARRA DE PESQUISA ---

        // Garantir que o botão de confirmação sempre chame a função correta
        document.getElementById('btnConfirmarStatus').onclick = function() {
            alternarStatusLimite();
        };

        function buscarNomePorCodigo() {
            const codigo = document.getElementById('codigo_alvo').value.trim();
            const tipo = document.getElementById('tipo').value;
            
            if (!codigo) {
                document.getElementById('nome_alvo').value = '';
                return;
            }
            
            fetch('buscar-por-codigo.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `codigo=${encodeURIComponent(codigo)}&tipo=${encodeURIComponent(tipo)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.nome) {
                    document.getElementById('nome_alvo').value = data.nome;
                } else {
                    document.getElementById('nome_alvo').value = '';
                }
            })
            .catch(error => {
                console.error('Erro ao buscar nome:', error);
                document.getElementById('nome_alvo').value = '';
            });
        }

        function preencherDatasPeriodoLimite() {
            // Buscar data de criação e periodicidade do limite
            const criadoEm = document.getElementById('detalhes_criado_em') ? document.getElementById('detalhes_criado_em').value : '';
            const periodicidade = parseInt(document.getElementById('detalhes_periodicidade').value);
            if (!criadoEm || !periodicidade) return;
            const dataInicial = new Date(criadoEm);
            const dataFinal = new Date(dataInicial);
            dataFinal.setMonth(dataFinal.getMonth() + periodicidade);
            document.getElementById('gastos_data_inicio').value = dataInicial.toISOString().slice(0,10);
            document.getElementById('gastos_data_fim').value = dataFinal.toISOString().slice(0,10);
        }
        function abrirPopupPeriodosLimite() {
            document.getElementById('popupPeriodosLimite').style.display = 'flex';
            carregarPeriodosLimite();
        }
        function fecharPopupPeriodosLimite() {
            document.getElementById('popupPeriodosLimite').style.display = 'none';
        }
        function carregarPeriodosLimite() {
            const id = document.getElementById('detalhes_id').value;
            // Buscar período vigente
            fetch('obter-periodo-vigente.php?id_limite=' + id)
                .then(r => r.json())
                .then(vigente => {
                    // Buscar períodos finalizados
                    fetch('listar-periodos-finalizados.php?id_limite=' + id)
                        .then(r => r.json())
                        .then(data => {
                            let html = '';
                            // Exibir período vigente
                            if (vigente.success) {
                                const inicio = vigente.data_inicio.split('-').reverse().join('/');
                                const fim = vigente.data_fim.split('-').reverse().join('/');
                                html += '<div style="font-weight:bold;color:#2563eb;">Período Vigente (em andamento):</div>';
                                html += `<table class=\"modern-table\" style=\"min-width:700px;margin-bottom:0;\"><thead><tr><th>Período</th><th>Detalhes</th></tr></thead><tbody>`;
                                html += `<tr><td>${inicio} a ${fim}</td><td><button type=\"button\" class=\"btn-modern btn-confirm\" onclick=\"verItensPeriodoVigente()\">Ver Itens</button></td></tr>`;
                                html += '</tbody></table>';
                            }
                            // Exibir períodos fechados
                            html += '<div style="margin-top:18px;font-weight:bold;">Períodos Finalizados:</div>';
                            if (data.success && data.periodos.length > 0) {
                                html += '<table class="modern-table" style="min-width:700px;">';
                                html += '<thead><tr><th>Período</th><th>Detalhes</th></tr></thead><tbody>';
                                data.periodos.forEach(function(p) {
                                    const inicio = p.data_inicio.split('-').reverse().join('/');
                                    const fim = p.data_fim.split('-').reverse().join('/');
                                    html += `<tr><td>${inicio} a ${fim}</td><td><button type=\"button\" class=\"btn-modern btn-confirm\" onclick=\"verDetalhesPeriodoFinalizado(${p.id})\">Ver detalhes</button></td></tr>`;
                                });
                                html += '</tbody></table>';
                            } else {
                                html += '<div style="color:#888;">Nenhum período fechado encontrado.</div>';
                            }
                    document.getElementById('periodosLimiteConteudo').innerHTML = html;
                });
                });
        }
        function verItensPeriodoVigente() {
            const id = document.getElementById('detalhes_id').value;
            fetch('obter-periodo-vigente.php?id_limite=' + id)
                .then(r => r.json())
                .then(data => {
                    if (data.success && data.gastos) {
                        let html = '<h4>Itens do Período Vigente</h4>';
                        html += '<table class="modern-table"><thead><tr><th>Data</th><th>Origem</th><th>Produto</th><th>Qtd</th><th>Valor</th><th>Valor Bruto</th></tr></thead><tbody>';
                        if (data.gastos.length > 0) {
                            data.gastos.forEach(function(g) {
                                html += `<tr><td>${g.data ? g.data.split(' ')[0] : ''}</td><td>${g.origem}</td><td>${g.produto}</td><td>${g.quantidade}</td><td>R$ ${(parseFloat(g.valor)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})}</td><td>R$ ${(parseFloat(g.valor_bruto)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})}</td></tr>`;
                            });
                        } else {
                            html += '<tr><td colspan="6" style="text-align:center;color:#888;">Nenhum item</td></tr>';
                        }
                        html += '</tbody></table>';
                        document.getElementById('periodosLimiteConteudo').innerHTML = html + '<div style="margin-top:18px;"><button class="btn-modern" onclick="carregarPeriodosLimite()">Voltar</button></div>';
                    } else {
                        alert('Erro ao buscar itens do período vigente.');
                    }
                });
        }
        function verDetalhesPeriodoFinalizado(idPeriodo) {
            fetch('obter-detalhes-periodo-finalizado.php?id_periodo=' + idPeriodo)
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        let html = '<h4>Itens do Período</h4>';
                        html += '<table class="modern-table"><thead><tr><th>Data</th><th>Origem</th><th>Produto</th><th>Qtd</th><th>Valor</th><th>Valor Bruto</th></tr></thead><tbody>';
                        if (data.dados_gastos && data.dados_gastos.length > 0) {
                            data.dados_gastos.forEach(function(g) {
                                html += `<tr><td>${g.data ? g.data.split(' ')[0] : ''}</td><td>${g.origem}</td><td>${g.produto}</td><td>${g.quantidade}</td><td>R$ ${(parseFloat(g.valor)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})}</td><td>R$ ${(parseFloat(g.valor_bruto)||0).toLocaleString('pt-BR',{minimumFractionDigits:2})}</td></tr>`;
                            });
                        } else {
                            html += '<tr><td colspan="6" style="text-align:center;color:#888;">Nenhum item</td></tr>';
                        }
                        html += '</tbody></table>';
                        // Exibir em um modal simples ou substituir o conteúdo
                        document.getElementById('periodosLimiteConteudo').innerHTML = html + '<div style="margin-top:18px;"><button class="btn-modern" onclick="carregarPeriodosLimite()">Voltar</button></div>';
                    } else {
                        alert('Erro ao buscar detalhes do período.');
                    }
                });
        }
        function preencherDatasPeriodoVigente() {
            console.log('📅 Preenchendo datas do período vigente');

            // Usar dados já carregados se disponíveis
            if (window.limiteAtual && window.limiteAtual.data_inicio && window.limiteAtual.data_fim) {
                console.log('✅ Usando dados já carregados:', window.limiteAtual);
                atualizarCamposGastos();
                return;
            }

            // Se não há dados carregados, buscar novamente
            const idLimite = document.getElementById('detalhes_id').value;

            if (!idLimite) {
                console.log('❌ ID do limite não encontrado');
                return;
            }

            console.log('🔍 Buscando período vigente para limite:', idLimite);
            fetch('obter-periodo-vigente.php?id_limite=' + idLimite)
                .then(r => r.json())
                .then(data => {
                    console.log('📅 Dados do período vigente recebidos:', data);
                    if (data.success) {
                        // Atualizar dados globais
                        window.limiteAtual = {
                            id: idLimite,
                            data_inicio: data.data_inicio,
                            data_fim: data.data_fim
                        };

                        // Atualizar campos
                        atualizarCamposGastos();
                    } else {
                        console.log('⚠️ Erro ao obter período vigente:', data.message);
                        const campoInicio = document.getElementById('gastos_data_inicio');
                        const campoFim = document.getElementById('gastos_data_fim');
                        if (campoInicio) campoInicio.value = '';
                        if (campoFim) campoFim.value = '';
                    }
                })
                .catch(error => {
                    console.error('❌ Erro na requisição:', error);
                });
        }
    </script>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Para cada linha da tabela principal, buscar o valor gasto e inserir o aviso ao lado do nome
    const linhas = document.querySelectorAll('.modern-table tbody tr');
    linhas.forEach(function(tr) {
        const tds = tr.querySelectorAll('td');
        if (tds.length < 8) return;
        const tipo = tds[1].textContent.trim().toLowerCase();
        const nomeTd = tds[2];
        const valorLimiteStr = tds[3].textContent.replace('R$', '').replace('.', '').replace(',', '.').trim();
        const saldoAvisoStr = tds[5].textContent.replace('R$', '').replace('.', '').replace(',', '.').trim();
        // Buscar o <span> dentro do <td> do nome
        const span = nomeTd.querySelector('span[data-codigo]');
        let codigoBusca = '';
        if (span) {
            codigoBusca = span.getAttribute('data-codigo');
        } else {
            codigoBusca = nomeTd.textContent.trim();
        }
        const valorLimite = parseFloat(valorLimiteStr);
        const saldoAviso = parseFloat(saldoAvisoStr);
        // DEBUG: mostrar o tipo e código enviados
        console.log('[AVISO-ICONES][fetch] tipo:', tipo, '| codigoBusca:', codigoBusca);
        fetch('gastos-por-limite.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `tipo=${encodeURIComponent(tipo)}&codigo=${encodeURIComponent(codigoBusca)}`
        })
        .then(r => r.json())
        .then(data => {
            // DEBUG: mostrar resposta do backend
            console.log('[AVISO-ICONES][resposta]', data);
            if (!data.success) return;
            const totalGasto = parseFloat(data.totalValor) || 0;
            const limiteNum = parseFloat(valorLimite) || 0;
            const avisoNum = parseFloat(saldoAviso) || 0;
            // Remover aviso anterior
            nomeTd.querySelectorAll('.aviso-limite, .aviso-saldo').forEach(e => e.remove());
            // DEBUG: mostrar valores no console
            console.log('[AVISO-ICONES] totalGasto:', totalGasto, '| limiteNum:', limiteNum, '| avisoNum:', avisoNum);
            let avisoNome = '';
            if (limiteNum > 0 && totalGasto >= limiteNum) {
                // Ícone vermelho: excedido ou igual ao limite
                avisoNome = '<span class="aviso-limite" style="color:#dc2626;font-size:18px;vertical-align:middle;display:inline-block;margin-left:6px;" title="Limite de Gasto Excedido!"><i class="fas fa-exclamation-circle"></i></span>';
            } else if (avisoNum > 0 && totalGasto >= avisoNum) {
                // Ícone amarelo: atingiu ou ultrapassou o saldo de aviso
                avisoNome = '<span class="aviso-saldo" style="color:#f59e42;font-size:18px;vertical-align:middle;display:inline-block;margin-left:6px;" title="Atenção: Saldo para Aviso atingido!"><i class="fas fa-exclamation-triangle"></i></span>';
            }
            nomeTd.innerHTML += avisoNome;
        });
    });
});
    </script>
</body>
</html>
<?php
session_start();

// Verificar se o usuário está logado e é administrador
if (!isset($_SESSION['usuario_id']) || $_SESSION['tipo_usuario'] !== 'administrador') {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

$mensagem = "";
$tipo_mensagem = "";

// Processar ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['alterar_status'])) {
        $usuario_id = $_POST['usuario_id'];
        $novo_status = $_POST['novo_status'];
        
        $sql = "UPDATE usuarios SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $novo_status, $usuario_id);
        
        if ($stmt->execute()) {
            $mensagem = "Status do usuário alterado com sucesso!";
            $tipo_mensagem = "sucesso";
        } else {
            $mensagem = "Erro ao alterar status do usuário!";
            $tipo_mensagem = "erro";
        }
    } elseif (isset($_POST['excluir_usuario'])) {
        $usuario_id = $_POST['usuario_id'];
        
        // Não permitir excluir o próprio usuário
        if ($usuario_id == $_SESSION['usuario_id']) {
            $mensagem = "Não é possível excluir seu próprio usuário!";
            $tipo_mensagem = "erro";
        } else {
            $sql = "DELETE FROM usuarios WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $usuario_id);
            
            if ($stmt->execute()) {
                $mensagem = "Usuário excluído com sucesso!";
                $tipo_mensagem = "sucesso";
            } else {
                $mensagem = "Erro ao excluir usuário!";
                $tipo_mensagem = "erro";
            }
        }
    } elseif (isset($_POST['alterar_senha'])) {
        $usuario_id = $_POST['usuario_id'];
        $nova_senha = $_POST['nova_senha'];
        
        if (strlen($nova_senha) < 6) {
            $mensagem = "A nova senha deve ter pelo menos 6 caracteres!";
            $tipo_mensagem = "erro";
        } else {
            $senha_hash = password_hash($nova_senha, PASSWORD_DEFAULT);
            
            $sql = "UPDATE usuarios SET senha = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $senha_hash, $usuario_id);
            
            if ($stmt->execute()) {
                $mensagem = "Senha alterada com sucesso!";
                $tipo_mensagem = "sucesso";
            } else {
                $mensagem = "Erro ao alterar senha!";
                $tipo_mensagem = "erro";
            }
        }
    }
}

// Buscar todos os usuários
$sql = "SELECT id, nome_usuario, tipo_usuario, data_cadastro, status FROM usuarios ORDER BY nome_usuario";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Usuários - Sistema de Estoque</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(120deg, #111 0%, #162447 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .mensagem {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .mensagem.sucesso {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .mensagem.erro {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .usuarios-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .usuarios-table th,
        .usuarios-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .usuarios-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .usuarios-table tr:hover {
            background: #f5f5f5;
        }
        
        .status-ativo {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-inativo {
            color: #dc3545;
            font-weight: bold;
        }
        
        .tipo-administrador {
            background: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .tipo-supervisor {
            background: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .tipo-almoxarife {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin: 2px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 5px;
            width: 80%;
            max-width: 400px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn-novo {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .btn-voltar {
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Gerenciar Usuários</h1>
            <p>Visualize e gerencie todos os usuários do sistema</p>
        </div>
        
        <?php if ($mensagem): ?>
            <div class="mensagem <?php echo $tipo_mensagem; ?>">
                <?php echo $mensagem; ?>
            </div>
        <?php endif; ?>
        
        <a href="cadastro-usuario.php" class="btn-novo">Novo Usuário</a>
        
        <table class="usuarios-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nome de Usuário</th>
                    <th>Tipo</th>
                    <th>Data de Cadastro</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $row['id']; ?></td>
                            <td><?php echo htmlspecialchars($row['nome_usuario']); ?></td>
                            <td>
                                <span class="tipo-<?php echo $row['tipo_usuario']; ?>">
                                    <?php echo ucfirst($row['tipo_usuario']); ?>
                                </span>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($row['data_cadastro'])); ?></td>
                            <td>
                                <span class="status-<?php echo $row['status']; ?>">
                                    <?php echo ucfirst($row['status']); ?>
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-primary" onclick="alterarStatus(<?php echo $row['id']; ?>, '<?php echo $row['status']; ?>')">
                                    <?php echo $row['status'] == 'ativo' ? 'Inativar' : 'Ativar'; ?>
                                </button>
                                <button class="btn btn-warning" onclick="alterarSenha(<?php echo $row['id']; ?>)">
                                    Alterar Senha
                                </button>
                                <button class="btn btn-success" onclick="gerenciarAcessos(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['nome_usuario']); ?>')">
                                    Gerenciar Acessos
                                </button>
                                <?php if ($row['id'] != $_SESSION['usuario_id']): ?>
                                    <button class="btn btn-danger" onclick="excluirUsuario(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['nome_usuario']); ?>')">
                                        Excluir
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" style="text-align: center;">Nenhum usuário encontrado</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        
        <a href="index.php" class="btn-voltar">Voltar ao Início</a>
    </div>
    
    <!-- Modal para alterar status -->
    <div id="modalStatus" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Alterar Status</h3>
                <span class="close" onclick="fecharModal('modalStatus')">&times;</span>
            </div>
            <form method="POST">
                <input type="hidden" name="usuario_id" id="status_usuario_id">
                <input type="hidden" name="alterar_status" value="1">
                <div class="form-group">
                    <label>Novo Status:</label>
                    <select name="novo_status" required>
                        <option value="ativo">Ativo</option>
                        <option value="inativo">Inativo</option>
                    </select>
                </div>
                <div class="btn-container">
                    <button type="submit" class="btn btn-success">Confirmar</button>
                    <button type="button" class="btn btn-danger" onclick="fecharModal('modalStatus')">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Modal para alterar senha -->
    <div id="modalSenha" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Alterar Senha</h3>
                <span class="close" onclick="fecharModal('modalSenha')">&times;</span>
            </div>
            <form method="POST">
                <input type="hidden" name="usuario_id" id="senha_usuario_id">
                <input type="hidden" name="alterar_senha" value="1">
                <div class="form-group">
                    <label>Nova Senha:</label>
                    <input type="password" name="nova_senha" required minlength="6">
                </div>
                <div class="btn-container">
                    <button type="submit" class="btn btn-success">Confirmar</button>
                    <button type="button" class="btn btn-danger" onclick="fecharModal('modalSenha')">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Modal para gerenciar acessos -->
    <div id="modalAcessos" class="modal">
        <div class="modal-content" style="max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <h3>Gerenciar Acessos - <span id="acessosUsuarioNome"></span></h3>
                <span class="close" onclick="fecharModal('modalAcessos')">&times;</span>
            </div>
            <form id="formAcessos" method="POST" onsubmit="return salvarAcessos();">
                <input type="hidden" name="usuario_id" id="acessos_usuario_id">
                <div id="acessosTree" style="max-height: 50vh; overflow-y: auto; padding-right: 8px;">
                    <!-- Árvore de menus/itens será preenchida via JS -->
                </div>
                <div class="btn-container">
                    <button type="submit" class="btn btn-success">Salvar</button>
                    <button type="button" class="btn btn-danger" onclick="fecharModal('modalAcessos')">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function alterarStatus(usuarioId, statusAtual) {
            document.getElementById('status_usuario_id').value = usuarioId;
            document.querySelector('select[name="novo_status"]').value = statusAtual === 'ativo' ? 'inativo' : 'ativo';
            document.getElementById('modalStatus').style.display = 'block';
        }
        
        function alterarSenha(usuarioId) {
            document.getElementById('senha_usuario_id').value = usuarioId;
            document.getElementById('modalSenha').style.display = 'block';
        }
        
        function excluirUsuario(usuarioId, nomeUsuario) {
            if (confirm('Tem certeza que deseja excluir o usuário "' + nomeUsuario + '"?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="usuario_id" value="${usuarioId}">
                    <input type="hidden" name="excluir_usuario" value="1">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function fecharModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Fechar modal ao clicar fora dele
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Estrutura dos menus/itens para montar a árvore
        const ACESSOS_MENUS = [
            {menu: 'cadastros', label: 'Cadastros', itens: [
                {item: 'cadastro-produto', label: 'Cadastro de Produto'},
                {item: 'cadastro-pessoas', label: 'Cadastro de Pessoas'},
                {item: 'cadastro-empresas', label: 'Cadastro de Empresas'}
            ]},
            {menu: 'tabelas', label: 'Tabelas', itens: [
                {item: 'tabela-produtos', label: 'Tabela de Produtos'},
                {item: 'tabela-pessoas', label: 'Tabela de Pessoas'},
                {item: 'tabela-empresas', label: 'Tabela de Empresas'}
            ]},
            {menu: 'entradas', label: 'Entradas', itens: [
                {item: 'entrada-estoque', label: 'Entrada de Estoque'},
                {item: 'registro-entradas', label: 'Registros de Entrada'}
            ]},
            {menu: 'saidas', label: 'Saída', itens: [
                {item: 'saida-estoque', label: 'Saída de Estoque'},
                {item: 'registro-saidas', label: 'Registro de Saídas'}
            ]},
            {menu: 'solicitacoes', label: 'Solicitações', itens: [
                {item: 'todas-solitacoes-estoque', label: 'Todas solicitações'}
            ]},
            {menu: 'epi', label: 'Fichas EPI', itens: [
                {item: 'fichas-epi', label: 'Gerenciar Fichas EPI'}
            ]},
            {menu: 'limites-gastos', label: 'Limites de Gastos', itens: [
                {item: 'configurar-limites', label: 'Configurar Limites'},
                {item: 'relatorio-gastos', label: 'Relatório de Gastos'},
                {item: 'alertas-limites', label: 'Alertas de Limites'}
            ]},
            {menu: 'devolucao', label: 'Devolução', itens: [
                {item: 'devolucao', label: 'Processar Devoluções'},
                {item: 'historico-devolucoes', label: 'Histórico de Devoluções'},
                {item: 'relatorio-devolucoes', label: 'Relatório de Devoluções'}
            ]},
            {menu: 'pedidos', label: 'Fazer Pedido', itens: [
                {item: 'requisicoes', label: 'Requisições'},
                {item: 'pedidos-mensais', label: 'Pedidos mensais'},
                {item: 'produtos-pedido-mensal', label: 'Produtos Pedido Mensal'},
                {item: 'pedidos-especiais', label: 'Pedidos especiais'}
            ]},
            {menu: 'meus-pedidos', label: 'Meus Pedidos', itens: [
                {item: 'requisicoes-feitas', label: 'Pedidos'}
            ]},
            {menu: 'usuarios', label: 'Usuários', itens: [
                {item: 'cadastro-usuario', label: 'Cadastrar Usuário'},
                {item: 'gerenciar-usuarios', label: 'Gerenciar Usuários'}
            ]}
        ];

        function gerenciarAcessos(usuarioId, nomeUsuario) {
            document.getElementById('acessos_usuario_id').value = usuarioId;
            document.getElementById('acessosUsuarioNome').textContent = nomeUsuario;
            // Montar árvore de menus/itens
            let html = '';
            ACESSOS_MENUS.forEach(menu => {
                html += `<div style='margin-bottom:8px;'>`;
                html += `<label><input type='checkbox' class='menu-chk' data-menu='${menu.menu}' onchange='toggleMenuItens(this)'> <b>${menu.label}</b></label>`;
                html += `<div style='margin-left:24px;'>`;
                menu.itens.forEach(item => {
                    html += `<label style='display:block;'><input type='checkbox' class='item-chk' data-menu='${menu.menu}' data-item='${item.item}'> ${item.label}</label>`;
                });
                html += `</div></div>`;
            });
            document.getElementById('acessosTree').innerHTML = html;
            // Carregar permissões via AJAX
            fetch('obter-acessos-usuario.php?usuario_id=' + usuarioId)
                .then(resp => resp.json())
                .then(data => {
                    // Marcar checkboxes conforme permissões
                    Object.entries(data).forEach(([chave, permitido]) => {
                        if (chave.includes('.')) {
                            // item
                            const [menu, item] = chave.split('.');
                            const chk = document.querySelector(`.item-chk[data-menu='${menu}'][data-item='${item}']`);
                            if (chk) chk.checked = permitido == 1;
                        } else {
                            // menu
                            const chk = document.querySelector(`.menu-chk[data-menu='${chave}']`);
                            if (chk) chk.checked = permitido == 1;
                        }
                    });
                });
            document.getElementById('modalAcessos').style.display = 'block';
        }
        function toggleMenuItens(menuChk) {
            const menu = menuChk.getAttribute('data-menu');
            document.querySelectorAll(`.item-chk[data-menu='${menu}']`).forEach(chk => {
                chk.checked = menuChk.checked;
            });
        }
        function salvarAcessos() {
            const usuario_id = document.getElementById('acessos_usuario_id').value;
            const acessos = {};
            document.querySelectorAll('.menu-chk').forEach(chk => {
                acessos[chk.getAttribute('data-menu')] = chk.checked ? 1 : 0;
            });
            document.querySelectorAll('.item-chk').forEach(chk => {
                const menu = chk.getAttribute('data-menu');
                const item = chk.getAttribute('data-item');
                acessos[menu + '.' + item] = chk.checked ? 1 : 0;
            });
            fetch('salvar-acessos-usuario.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({usuario_id, acessos})
            })
            .then(resp => resp.json())
            .then(data => {
                if (data.ok) {
                    alert('Acessos salvos com sucesso!');
                    fecharModal('modalAcessos');
                } else {
                    alert('Erro ao salvar acessos!');
                }
            });
            return false;
        }
    </script>
</body>
</html>

<?php $conn->close(); ?> 
<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

include 'conexao.php';

if (isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    $sql = "SELECT lg.*, 
                CASE 
                    WHEN lg.tipo = 'empresa' THEN e.nome_empresa 
                    WHEN lg.tipo = 'setor' THEN s.nome 
                    ELSE '' 
                END as nome_alvo
            FROM limites_gastos lg
            LEFT JOIN empresas e ON lg.id_empresa = e.codigo_empresa
            LEFT JOIN setor s ON lg.id_setor = s.id
            WHERE lg.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $limite = $result->fetch_assoc();
        $limite['id_empresa'] = isset($limite['id_empresa']) ? (int)$limite['id_empresa'] : null;
        $limite['id_setor'] = isset($limite['id_setor']) ? (int)$limite['id_setor'] : null;
        echo json_encode(['success' => true, 'limite' => $limite]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Limite não encontrado']);
    }
    
    $stmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'ID não fornecido']);
}

$conn->close();
?> 
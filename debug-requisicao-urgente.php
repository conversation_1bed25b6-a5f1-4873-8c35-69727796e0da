<?php
include 'conexao.php';

echo "<h2>Debug - Verificar Requisições Urgentes</h2>";

// Buscar requisições urgentes
$sql = "SELECT codigo_solicitacao, solicitante, requisicao_urgente, motivo_urgente FROM requisicoes WHERE requisicao_urgente = 1 ORDER BY codigo_solicitacao DESC LIMIT 5";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    echo "<h3>Requisições Urgentes Encontradas:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Código</th><th>Solicitante</th><th>Urgente</th><th>Motivo</th><th>Testar Endpoint</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['codigo_solicitacao'] . "</td>";
        echo "<td>" . $row['solicitante'] . "</td>";
        echo "<td>" . $row['requisicao_urgente'] . "</td>";
        echo "<td>" . $row['motivo_urgente'] . "</td>";
        echo "<td><a href='obter-detalhes-requisicao.php?codigo=" . $row['codigo_solicitacao'] . "' target='_blank'>Testar API</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Testar o primeiro resultado
    $first_row = $result->fetch_assoc();
    if (!$first_row) {
        $result->data_seek(0);
        $first_row = $result->fetch_assoc();
    }
    
    if ($first_row) {
        $codigo_teste = $first_row['codigo_solicitacao'];
        echo "<h3>Teste do Endpoint para Código: $codigo_teste</h3>";
        
        // Simular chamada do endpoint
        $stmt = $conn->prepare("SELECT r.*, e.nome_empresa FROM requisicoes r LEFT JOIN empresas e ON r.empresa = e.codigo_empresa WHERE r.codigo_solicitacao = ?");
        $stmt->bind_param("i", $codigo_teste);
        $stmt->execute();
        $result_teste = $stmt->get_result();
        
        if ($result_teste->num_rows > 0) {
            $requisicao = $result_teste->fetch_assoc();
            
            echo "<h4>Dados retornados pelo endpoint:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
            echo "requisicao_urgente: " . ($requisicao['requisicao_urgente'] ?? 'NULL') . "\n";
            echo "motivo_urgente: " . ($requisicao['motivo_urgente'] ?? 'NULL') . "\n";
            echo "\nTodos os campos:\n";
            foreach ($requisicao as $campo => $valor) {
                echo "$campo: $valor\n";
            }
            echo "</pre>";
            
            // Simular resposta JSON
            echo "<h4>Resposta JSON simulada:</h4>";
            $response = ['success' => true, 'requisicao' => $requisicao, 'itens' => []];
            echo "<pre style='background: #e8f5e8; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
            echo json_encode($response, JSON_PRETTY_PRINT);
            echo "</pre>";
        }
    }
    
} else {
    echo "<p style='color: orange;'>Nenhuma requisição urgente encontrada. Vou criar uma para teste...</p>";
    
    // Criar uma requisição urgente de teste
    $sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
            VALUES ('Debug Test', '001', 'DEBUG', 'Teste de debug', '1', 'Debug', 'TI', 'Requisição para debug', 1, 'Limite excedido', NOW())";
    
    if ($conn->query($sql)) {
        $codigo_novo = $conn->insert_id;
        echo "<p style='color: green;'>✅ Requisição urgente criada com código: $codigo_novo</p>";
        echo "<p><a href='debug-requisicao-urgente.php'>Recarregar para ver os dados</a></p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao criar requisição: " . $conn->error . "</p>";
    }
}

echo "<h3>Verificação JavaScript:</h3>";
echo "<p>Abra o console do navegador em requisicoes-feitas.php e procure pelas mensagens:</p>";
echo "<ul>";
echo "<li><code>Mostrando aviso urgente para: requisicao [dados]</code></li>";
echo "<li><code>Ocultando aviso urgente para: requisicao [dados]</code></li>";
echo "</ul>";

echo "<p style='margin-top: 30px;'>";
echo "<a href='requisicoes-feitas.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Ir para Requisições Feitas</a>";
echo "<a href='obter-detalhes-requisicao.php?codigo=" . ($codigo_teste ?? '1') . "' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Testar Endpoint Diretamente</a>";
echo "</p>";

$conn->close();
?>

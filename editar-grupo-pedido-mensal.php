<?php
include 'conexao.php';
$id = $_POST['id'];
$nome = $_POST['nome'];
$produtos = json_decode($_POST['produtos'], true);
$conn->begin_transaction();
try {
    $stmt = $conn->prepare("UPDATE grupos_pedidos_mensais SET nome=? WHERE id=?");
    $stmt->bind_param('si', $nome, $id);
    $stmt->execute();
    $conn->query("DELETE FROM grupo_produtos_pedidos_mensais WHERE grupo_id = $id");
    foreach ($produtos as $p) {
        $stmt2 = $conn->prepare("INSERT INTO grupo_produtos_pedidos_mensais (grupo_id, produto_id, quantidade) VALUES (?, ?, ?)");
        $stmt2->bind_param('iii', $id, $p['codigo'], $p['quantidade']);
        $stmt2->execute();
    }
    $conn->commit();
    echo json_encode(['success'=>true]);
} catch(Exception $e) {
    $conn->rollback();
    echo json_encode(['success'=>false, 'message'=>$e->getMessage()]);
} 
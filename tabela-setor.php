<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}
include 'conexao.php';

// --- Aç<PERSON>es de inativar, ativar e excluir setor ---
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['delete_id_setor']) && !empty($_POST['delete_id_setor'])) {
        $deleteId = $_POST['delete_id_setor'];
        $sql = "DELETE FROM setor WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $deleteId);
        if ($stmt->execute()) {
            header('Location: tabela-setor.php?status=delete-success');
        } else {
            header('Location: tabela-setor.php?status=error&message=' . urlencode('Erro ao excluir o setor.'));
        }
        $stmt->close();
        exit;
    } elseif (isset($_POST['inativar_id_setor']) && !empty($_POST['inativar_id_setor'])) {
        $setorId = $_POST['inativar_id_setor'];
        $status = $_POST['status_setor'];
        // Adiciona a coluna status se não existir
        $result = $conn->query("SHOW COLUMNS FROM setor LIKE 'status'");
        if ($result->num_rows == 0) {
            $conn->query("ALTER TABLE setor ADD COLUMN status VARCHAR(10) DEFAULT 'ativo'");
        }
        $sql = "UPDATE setor SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $status, $setorId);
        if ($stmt->execute()) {
            header('Location: tabela-setor.php?status=success&message=' . urlencode('Status do setor atualizado com sucesso!'));
        } else {
            header('Location: tabela-setor.php?status=error&message=' . urlencode('Erro ao atualizar status do setor.'));
        }
        $stmt->close();
        exit;
    }
}

// Filtros e pesquisa
$search = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'ativo';
$empresaFilter = isset($_GET['empresa_filter']) ? $_GET['empresa_filter'] : '';

// Buscar empresas distintas presentes na tabela setor
$empresasSetor = [];
$sqlEmpresasSetor = "SELECT DISTINCT s.empresa, e.nome_empresa FROM setor s LEFT JOIN empresas e ON s.empresa = e.codigo_empresa WHERE s.empresa IS NOT NULL AND s.empresa != '' ORDER BY e.nome_empresa";
$resEmpSetor = $conn->query($sqlEmpresasSetor);
while ($row = $resEmpSetor->fetch_assoc()) {
    $empresasSetor[] = [
        'codigo' => $row['empresa'],
        'nome' => $row['nome_empresa'] ?: $row['empresa']
    ];
}

$whereClause = [];
if ($search) {
    $whereClause[] = "(s.nome LIKE '%" . $conn->real_escape_string($search) . "%' OR s.empresa LIKE '%" . $conn->real_escape_string($search) . "%')";
}
if ($statusFilter !== 'todos') {
    $whereClause[] = "(s.status = '" . $conn->real_escape_string($statusFilter) . "' OR s.status IS NULL AND 'ativo' = '" . $conn->real_escape_string($statusFilter) . "')";
}
if ($empresaFilter !== '' && $empresaFilter !== 'todos') {
    $whereClause[] = "s.empresa = '" . $conn->real_escape_string($empresaFilter) . "'";
}
$whereSQL = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";

$result = $conn->query("SELECT s.*, e.nome_empresa FROM setor s LEFT JOIN empresas e ON s.empresa = e.codigo_empresa $whereSQL ORDER BY s.id");
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <title>Tabela de Setor</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #111; margin: 0; padding: 0; }
        .content-container { background-color: #fff; border-radius: 20px; margin: 20px 20px 20px 290px; padding: 30px; min-height: calc(100vh - 40px); box-shadow: 0 0 30px rgba(0, 0, 0, 0.4); position: relative; z-index: 1; }
        h1 { color: #111827; font-size: 28px; font-weight: 600; text-align: center; margin-bottom: 30px; }
        .modern-table-container { background: #ffffff; border-radius: 12px; padding: 25px; border: 1px solid #e5e7eb; margin-bottom: 30px; margin-top: 18px; }
        .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: white; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb; }
        .modern-table thead { background: #f8fafc; }
        .modern-table th { color: #374151; font-weight: 600; padding: 16px 20px; text-align: left; font-size: 14px; border: none; border-bottom: 1px solid #e5e7eb; }
        .modern-table tbody tr { transition: background 0.2s ease; border-bottom: 1px solid #f3f4f6; }
        .modern-table tbody tr:hover { background: #f9fafb; }
        .modern-table tbody tr:last-child { border-bottom: none; }
        .modern-table td { padding: 16px 20px; color: #111827; font-size: 14px; border: none; }
        .modern-table td:first-child { font-weight: 600; color: #2563eb; }
        .action-btn { background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 8px 16px; cursor: pointer; font-size: 14px; font-weight: 500; transition: background 0.2s; }
        .action-btn:hover { background: #1d4ed8; }
        .search-bar { display: flex; align-items: center; gap: 12px; margin-bottom: 24px; }
        .search-bar input { flex: 1; padding: 12px 16px; border: 1px solid #d1d5db; border-radius: 8px; font-size: 14px; }
        .search-bar button { padding: 10px 18px; border-radius: 8px; background: #2563eb; color: #fff; border: none; font-size: 15px; font-weight: 600; cursor: pointer; transition: background 0.2s; }
        .search-bar button:hover { background: #1d4ed8; }
        .export-btn { background: #22c55e; color: #fff; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; font-weight: 500; margin-left: 8px; cursor: pointer; transition: background 0.2s; }
        .export-btn:hover { background: #16a34a; }
        .filter-btn {color: #fff; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; font-weight: 500; margin-left: 8px; cursor: pointer; transition: background 0.2s; }
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 99999 !important;
            align-items: center;
            justify-content: center;
        }
        .popup-overlay[style*="display: flex"] {
            display: flex !important;
        }
        .popup {
            background: #fff;
            padding: 32px 36px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.18);
            min-width: 320px;
            max-width: 600px;
            width: 100%;
            z-index: 100000 !important;
            position: relative;
            margin: 0 auto;
        }
        .popup-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 18px; }
        .popup-header h3 { margin: 0; font-size: 20px; color: #111827; font-weight: 600; }
        .popup-close { background: none; border: none; font-size: 28px; color: #6b7280; cursor: pointer; }
        .popup-close:hover { color: #dc2626; }
        .popup-content { margin-bottom: 12px; }
        .popup-actions { display: flex; gap: 12px; justify-content: flex-end; }
        .popup-actions button { padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; border: none; cursor: pointer; }
        .popup-actions .btn-cancel { background: #e5e7eb; color: #374151; }
        .popup-actions .btn-cancel:hover { background: #d1d5db; }
        .popup-actions .btn-edit { background: #2563eb; color: #fff; }
        .popup-actions .btn-edit:hover { background: #1d4ed8; }
        .popup-actions .btn-delete { background: #dc2626; color: #fff; }
        .popup-actions .btn-delete:hover { background: #b91c1c; }
        .main-dropdown {
            position: relative;
            display: inline-block;
        }
        .main-dropdown-toggle {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 14px;
            cursor: pointer;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        .main-dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        .main-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        .main-dropdown-menu.show {
            display: block;
        }
        .main-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        .main-dropdown-item:hover {
            background: #f9fafb;
        }
        .main-dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }
        .main-dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            margin-top: 16px;
        }
        .search-bar-container {
            margin-top: 55px !important;
        }
        .main-title {
            font-size: 28px;
            font-weight: 600;
            color: #111827;
            margin: 0;
            text-align: left;
            margin-top: -5px;
        }
        .main-dropdown-toggle {
            width: auto;
            height: auto;
        }
        .search-bar-wrapper {
            position: relative;
            width: 900px;
            max-width: 98vw;
            display: flex;
            align-items: center;
        }
        .search-bar-input {
            width: 100%;
            padding: 13px 44px 13px 14px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 1.08em;
            outline: none;
            background: transparent;
            color: #222;
            transition: border 0.2s;
            box-shadow: none;
            height: 51px;
            margin-right: 0;
        }
        .search-bar-btn-inside {
            position: absolute;
            right: 0px;
            top: 1px;
            height: 50px;
            width: 55px;
            background: none;
            color: #111;
            border: none;
            border-radius: 8px 8px 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 2;
        }
        .search-bar-btn-inside i {
            color: #111;
            font-size: 1.3em;
        }
        .filter-btn {
            margin-left: 16px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #222;
            background: none;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: -17px;
            background: none;
        }
        .filter-btn:hover, .filter-btn:focus, .filter-btn:active {
            background: none !important;
            border: none !important;
            color: #222 !important;
            box-shadow: none !important;
        }
        /* Abas do popup (copiado do tabela-empresas.php) */
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        .popup-tab.active {
            background: white;
            color: #2563eb;
            border-color: #2563eb;
        }
        .popup-tab:hover {
            background: #f3f4f6;
        }
        .popup-tab-content {
            display: none;
        }
        .popup-tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
        }
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        .actions-section {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 15px;
        }
        .actions-section h4 {
            margin-top: 0;
            color: #111827;
            font-size: 16px;
            margin-bottom: 15px;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn-modern {
            border: none;
            border-radius: 6px;
            padding: 6px 0;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            width: 100%;
            text-align: center;
        }
        .btn-modern.btn-warning {
            background: #f59e0b;
            color: #fff;
            border: 1px solid #f59e0b;
        }
        .btn-modern.btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
            color: #fff;
        }
        .btn-modern.btn-danger {
            background: #dc2626;
            color: #fff;
            border: 1px solid #dc2626;
        }
        .btn-modern.btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
            color: #fff;
        }
        .btn-modern.btn-confirm {
            background: #22c55e;
            color: #fff;
        }
        .btn-modern.btn-confirm:hover {
            background: #16a34a;
            color: #fff;
        }
        .btn-modern i {
            margin-right: 8px;
            font-size: 0.95em;
        }
        .confirm-overlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 200000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .confirm-overlay[style*="display: flex"] {
            display: flex !important;
        }
        .confirm-popup {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            padding: 32px 24px;
            min-width: 280px;
            max-width: 90vw;
            text-align: center;
            z-index: 210000;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-ativo {
            background: #dcfce7;
            color: #166534;
        }
        .status-inativo {
            background: #fef2f2;
            color: #dc2626;
        }
        .confirm-actions {
            display: flex;
            flex-direction: row;
            gap: 12px;
            justify-content: center;
        }
        .confirm-actions button {
            padding: 8px 20px;
        }
        .confirm-title {
            font-weight: 700;
            font-size: 1.25em;
        }
        /* Estilos para o popup de filtros */
        .filter-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99998;
            display: none;
        }
        .filter-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.18);
            min-width: 320px;
            max-width: 95vw;
            z-index: 99999;
            padding: 32px 28px 24px;
            display: none;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 18px;
            padding-bottom: 0;
            border: none;
        }
        .filter-popup-header span {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-right: 10px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s;
            margin-right: -10px;
            margin-top: -10px;
        }
        .close-filter-popup:hover {
            background: none;
            color: #2563eb;
        }
        .filter-popup-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .filter-popup-group {
            display: flex;
            flex-direction: column;
        }
        .filter-popup-group label {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 5px;
        }
        .filter-popup-input {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 15px;
            background: #f9fafb;
            color: #111827;
            transition: border 0.2s;
        }
        .filter-popup-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .filter-popup-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .setor-dropdown-toggle {
            background: white;
            border: 2px solid #bdbdbd;
            border-radius: 8px;
            width: 48px;
            height: 48px;
            margin-top: 8px;
            margin-right: 8px;
            cursor: pointer;
            font-size: 22px;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        .setor-dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        .setor-export-btn {
            background: #fff;
            border: 2px solid #bdbdbd !important;
            border-radius: 8px !important;
            width: 82px !important;
            height: 39px !important;
            margin-top: 8px !important;
            margin-right: 1px !important;
            cursor: pointer;
            font-size: 18px !important;
            color: #6b7280 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            box-sizing: border-box !important;
            transition: all 0.2s ease;
        }
        .setor-export-btn:hover {
            background: #f9fafb !important;
            border-color: #9ca3af !important;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>
<div class="content-container">
    <div class="main-header">
        <h1 class="main-title">Tabela de Setor</h1>
        <div class="main-dropdown">
            <button class="setor-export-btn" onclick="toggleMainDropdown()">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="main-dropdown-menu" id="mainExportDropdown">
                <div class="main-dropdown-item" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Exportar para Excel
                </div>
                <div class="main-dropdown-item" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Exportar para PDF
                </div>
            </div>
        </div>
    </div>
    <div class="search-bar-container">
        <div style="display: flex; align-items: center; gap: 20px; justify-content: center;">
            <div class="search-bar-wrapper">
                <input type="text" class="search-bar-input" id="searchVisible" placeholder="Pesquisar..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados"><i class="fas fa-filter"></i></button>
        </div>
    </div>
    <form id="searchForm" method="get" style="display:none;">
        <input type="hidden" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>">
        <input type="hidden" id="status_filter" name="status_filter" value="<?php echo htmlspecialchars($statusFilter); ?>">
        <input type="hidden" id="empresa_filter" name="empresa_filter" value="<?php echo htmlspecialchars($empresaFilter); ?>">
    </form>
    <!-- Popup de filtros moderno -->
    <div id="filterPopupOverlay" class="filter-popup-overlay"></div>
    <div id="filterPopup" class="filter-popup">
        <div class="filter-popup-header">
            <span>Filtrar por:</span>
            <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
        </div>
        <form method="get" class="filter-popup-form" style="display: flex; flex-direction: column; gap: 18px;">
            <div class="filter-popup-group">
                <label for="status_filter_popup" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Status:</label>
                <select id="status_filter_popup" name="status_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                    <option value="ativo" <?php if ($statusFilter == 'ativo') echo 'selected'; ?>>Ativo</option>
                    <option value="inativo" <?php if ($statusFilter == 'inativo') echo 'selected'; ?>>Inativo</option>
                    <option value="todos" <?php if ($statusFilter == 'todos') echo 'selected'; ?>>Todos</option>
                </select>
            </div>
            <div class="filter-popup-group">
                <label for="empresa_filter_popup" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Empresa:</label>
                <select id="empresa_filter_popup" name="empresa_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                    <option value="todos" <?php if ($empresaFilter == 'todos' || $empresaFilter == '') echo 'selected'; ?>>Todas</option>
                    <?php foreach ($empresasSetor as $emp): ?>
                        <option value="<?php echo htmlspecialchars($emp['codigo']); ?>" <?php if ($empresaFilter == $emp['codigo']) echo 'selected'; ?>><?php echo htmlspecialchars($emp['nome']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="filter-popup-actions" style="display: flex; gap: 12px; margin-top: 10px;">
                <a href="tabela-setor.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
            </div>
        </form>
    </div>
    <!-- Fim da barra de busca/filtro -->
    <div class="modern-table-container">
        <table class="modern-table" id="setorTable">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Cód. Empresa</th>
                    <th>Empresa</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach (
    $dados as $row):
    $status = isset($row['status']) ? $row['status'] : 'ativo';
                    $nomeEmpresa = $row['nome_empresa'] ?: $row['empresa'];
?>
                <tr>
                    <td><?php echo str_pad($row['id'], 2, '0', STR_PAD_LEFT); ?></td>
                    <td><?php echo htmlspecialchars($row['nome']); ?></td>
                    <td><?php echo htmlspecialchars($row['empresa']); ?></td>
                    <td><?php echo htmlspecialchars($nomeEmpresa); ?></td>
                    <td>
                        <?php if ($status === 'ativo'): ?>
                            <span class="status-badge status-ativo">Ativo</span>
                        <?php else: ?>
                            <span class="status-badge status-inativo">Inativo</span>
                        <?php endif; ?>
                    </td>
                    <td><button class="action-btn" onclick="abrirPopupAcoes(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars(addslashes($row['nome'])); ?>', '<?php echo htmlspecialchars(addslashes($nomeEmpresa)); ?>', '<?php echo htmlspecialchars($status); ?>', '<?php echo htmlspecialchars(addslashes($row['empresa'])); ?>')">Ações</button></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <!-- Popup de edição do setor -->
    <div class="popup-overlay" id="editSetorPopupOverlay">
        <div class="popup">
            <h3>Editar Setor</h3>
            <div class="popup-tabs">
                <div class="popup-tab active" onclick="switchTabSetor('info')">Informações</div>
                <div class="popup-tab" onclick="switchTabSetor('funcionarios')">Funcionários</div>
                <div class="popup-tab" onclick="switchTabSetor('actions')">Ações</div>
            </div>
            <div id="info-tab-setor" class="popup-tab-content active">
                <form id="editSetorForm" action="editar-setor.php" method="POST">
                    <input type="hidden" id="edit_setor_id" name="id">
                    <div class="form-group">
                        <label for="edit_setor_nome">Nome do Setor:</label>
                        <input type="text" id="edit_setor_nome" name="nome" required>
                    </div>
                    <div class="form-group" style="display: flex; gap: 12px; align-items: flex-start;">
                        <div style="width: 120px;">
                            <label for="edit_setor_empresa_codigo">Cód. Empresa:</label>
                            <input type="text" id="edit_setor_empresa_codigo" name="empresa_codigo" readonly>
                        </div>
                        <div style="flex: 1;">
                        <label for="edit_setor_empresa">Empresa:</label>
                            <input type="text" id="edit_setor_empresa" name="empresa" readonly>
                        </div>
                    </div>
                    <div class="form-buttons">
                        <button type="button" class="btn-modern btn-cancel" onclick="closeEditSetorPopup()">Cancelar</button>
                        <button type="submit" class="btn-modern btn-confirm">Salvar</button>
                    </div>
                </form>
            </div>
            <div id="actions-tab-setor" class="popup-tab-content">
                <div class="actions-section">
                    <h4>Ações do Setor</h4>
                    <p>Selecione uma ação para executar no setor <strong id="setor-nome-acoes"></strong>:</p>
                    <div class="action-buttons">
                        <button class="btn-modern btn-warning" onclick="inativarSetorFromPopup()" id="btn-inativar-setor" style="display: inline-flex;">
                            <i class="fas fa-pause"></i> Inativar Setor
                        </button>
                        <button class="btn-modern btn-confirm" onclick="ativarSetorFromPopup()" id="btn-ativar-setor" style="display: none;">
                            <i class="fas fa-play"></i> Ativar Setor
                        </button>
                        <button class="btn-modern btn-danger" onclick="excluirSetorFromPopup()">
                            <i class="fas fa-trash"></i> Excluir Setor
                        </button>
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-modern btn-cancel" onclick="closeEditSetorPopup()">Fechar</button>
                </div>
            </div>
            <div id="funcionarios-tab-setor" class="popup-tab-content">
                <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 8px;">
                    <div style="position: relative;">
                        <button id="exportDropdownBtn" style="background: none; border: none; cursor: pointer; font-size: 28px; color: #888; padding: 0; line-height: 1; border-radius: 50%; margin-right: 12px;">⋮</button>
                        <div id="exportDropdownMenu" style="display: none; position: absolute; right: 0; top: 32px; background: #fff; border: 1px solid #e5e7eb; border-radius: 8px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); z-index: 1000; min-width: 180px;">
                            <button onclick="exportFuncionariosSetorExcel()" style="width: 100%; padding: 10px 16px; border: none; background: none; text-align: left; cursor: pointer; font-size: 15px; color: #222; font-weight: 400;">Exportar para Excel</button>
                            <button onclick="exportFuncionariosSetorPDF()" style="width: 100%; padding: 10px 16px; border: none; background: none; text-align: left; cursor: pointer; font-size: 15px; color: #222; font-weight: 400;">Exportar para PDF</button>
                        </div>
                    </div>
                </div>
                <div id="funcionarios-loading" style="display:none; color:#2563eb; margin-bottom:10px;">Carregando funcionários...</div>
                <div id="funcionarios-erro" style="display:none; color:#dc2626; margin-bottom:10px;"></div>
                <table class="modern-table" id="funcionariosSetorTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome</th>
                            <th>Função</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="funcionariosSetorBody">
                        <tr><td colspan="4" style="text-align:center; color:#9ca3af;">Selecione a aba para carregar funcionários...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Formulário oculto para ações do setor -->
    <form id="actionFormSetor" method="POST" action="tabela-setor.php" style="display: none;">
        <input type="hidden" id="delete_id_setor" name="delete_id_setor">
        <input type="hidden" id="inativar_id_setor" name="inativar_id_setor">
        <input type="hidden" id="status_setor" name="status_setor">
    </form>
</div>
<!-- Overlay e Popup de Confirmação (mover para fora do content-container) -->
<div class="confirm-overlay" id="confirmOverlaySetor">
    <div class="confirm-popup" id="confirmPopupSetor">
        <div class="confirm-title" id="confirmTitleSetor"></div>
        <div class="confirm-message" id="confirmMessageSetor"></div>
        <div class="confirm-actions">
            <button class="btn-confirm" id="btnConfirmYesSetor">Confirmar</button>
            <button class="btn-cancel" id="btnConfirmNoSetor">Cancelar</button>
        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>
<script>
let currentSetor = null; // Mover para o topo do script, antes de qualquer função
function abrirPopupAcoes(id, nome, empresa_nome, status = 'ativo', empresa_codigo = null) {
    currentSetor = { id, nome, empresa_nome, status, empresa_codigo };
    document.getElementById('editSetorPopupOverlay').style.display = 'flex';
    document.getElementById('edit_setor_id').value = id;
    document.getElementById('edit_setor_nome').value = nome;
    document.getElementById('edit_setor_empresa').value = empresa_nome;
    document.getElementById('setor-nome-acoes').textContent = nome;
    // Preencher o campo de código da empresa
    document.getElementById('edit_setor_empresa_codigo').value = empresa_codigo;
    // Ativar/inativar botões conforme status
    if (status === 'ativo') {
        document.getElementById('btn-inativar-setor').style.display = 'inline-flex';
        document.getElementById('btn-ativar-setor').style.display = 'none';
    } else {
        document.getElementById('btn-inativar-setor').style.display = 'none';
        document.getElementById('btn-ativar-setor').style.display = 'inline-flex';
    }
    switchTabSetor('info');
}
function closeEditSetorPopup() {
    document.getElementById('editSetorPopupOverlay').style.display = 'none';
    currentSetor = null;
}
// --- Popup de Confirmação Global para Setor ---
let confirmActionSetor = null;
function showConfirmPopupSetor({title, message, onConfirm, type = 'default'}) {
    document.getElementById('confirmTitleSetor').textContent = title;
    document.getElementById('confirmMessageSetor').textContent = message;
    const popup = document.getElementById('confirmPopupSetor');
    popup.classList.remove('btn-danger', 'btn-warning');
    if (type === 'danger') {
        document.getElementById('btnConfirmYesSetor').classList.add('btn-danger');
        document.getElementById('btnConfirmYesSetor').classList.remove('btn-warning');
    } else if (type === 'warning') {
        document.getElementById('btnConfirmYesSetor').classList.add('btn-warning');
        document.getElementById('btnConfirmYesSetor').classList.remove('btn-danger');
    } else {
        document.getElementById('btnConfirmYesSetor').classList.remove('btn-danger', 'btn-warning');
    }
    document.getElementById('confirmOverlaySetor').style.display = 'flex';
    confirmActionSetor = onConfirm;
}
document.getElementById('btnConfirmYesSetor').onclick = function() {
    document.getElementById('confirmOverlaySetor').style.display = 'none';
    if (typeof confirmActionSetor === 'function') confirmActionSetor();
};
document.getElementById('btnConfirmNoSetor').onclick = function() {
    document.getElementById('confirmOverlaySetor').style.display = 'none';
    confirmActionSetor = null;
};
function excluirSetorFromPopup() {
    if (!currentSetor) return;
    showConfirmPopupSetor({
        title: 'Confirmar Exclusão',
        message: `Tem certeza que deseja excluir o setor "${currentSetor.nome}"? Esta ação não pode ser desfeita.`,
        type: 'danger',
        onConfirm: function() {
            document.getElementById('delete_id_setor').value = currentSetor.id;
            document.getElementById('actionFormSetor').submit();
        }
    });
}
function inativarSetorFromPopup() {
    if (!currentSetor) return;
    showConfirmPopupSetor({
        title: 'Confirmar Inativação',
        message: 'Tem certeza que deseja inativar este setor?',
        type: 'warning',
        onConfirm: function() {
            document.getElementById('inativar_id_setor').value = currentSetor.id;
            document.getElementById('status_setor').value = 'inativo';
            document.getElementById('actionFormSetor').submit();
        }
    });
}
function ativarSetorFromPopup() {
    if (!currentSetor) return;
    showConfirmPopupSetor({
        title: 'Confirmar Ativação',
        message: 'Tem certeza que deseja ativar este setor?',
        type: 'warning',
        onConfirm: function() {
            document.getElementById('inativar_id_setor').value = currentSetor.id;
            document.getElementById('status_setor').value = 'ativo';
            document.getElementById('actionFormSetor').submit();
        }
    });
}
// Exportar para Excel
function exportToExcel() {
    let table = document.getElementById('setorTable');
    let csv = [];
    for (let i = 0; i < table.rows.length; i++) {
        let row = [], cols = table.rows[i].querySelectorAll('td, th');
        for (let j = 0; j < cols.length - 1; j++) { // exceto ações
            row.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }
        csv.push(row.join(','));
    }
    let csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
    let downloadLink = document.createElement('a');
    downloadLink.download = 'setores.csv';
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}
// Exportar para PDF
function exportToPDF() {
    const { jsPDF } = window.jspdf;
    let doc = new jsPDF('l', 'pt', 'a4');
    doc.autoTable({ html: '#setorTable', startY: 20, margin: { left: 20, right: 20 }, styles: { fontSize: 10 }, headStyles: { fillColor: [37, 99, 235] } });
    doc.save('setores.pdf');
}
// Dropdown de exportação
function toggleMainDropdown() {
    var menu = document.getElementById('mainExportDropdown');
    menu.classList.toggle('show');
    function closeMenu(e) {
        if (!menu.contains(e.target) && !e.target.closest('.setor-export-btn')) {
            menu.classList.remove('show');
            document.removeEventListener('click', closeMenu);
        }
    }
    document.addEventListener('click', closeMenu);
}
// Pesquisa
const searchVisible = document.getElementById('searchVisible');
const searchForm = document.getElementById('searchForm');
const searchInput = document.getElementById('search');
const searchVisibleBtn = document.getElementById('searchVisibleBtn');
searchVisible.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        searchInput.value = searchVisible.value;
        searchForm.submit();
    }
});
searchVisibleBtn.addEventListener('click', function() {
    searchInput.value = searchVisible.value;
    searchForm.submit();
});
// Filtro popup
const filterBtn = document.getElementById('filterBtn');
const filterPopup = document.getElementById('filterPopup');
const filterPopupOverlay = document.getElementById('filterPopupOverlay');
const closeFilterPopup = document.getElementById('closeFilterPopup');
filterBtn.addEventListener('click', function(e) {
    filterPopup.style.display = 'block';
    filterPopupOverlay.style.display = 'block';
    e.stopPropagation();
});
closeFilterPopup.addEventListener('click', function() {
    filterPopup.style.display = 'none';
    filterPopupOverlay.style.display = 'none';
});
filterPopupOverlay.addEventListener('click', function() {
    filterPopup.style.display = 'none';
    filterPopupOverlay.style.display = 'none';
});
// Filtro popup (exemplo, pode ser expandido)
// document.getElementById('filterBtn').addEventListener('click', function() {
//     alert('Funcionalidade de filtro avançado pode ser implementada aqui.');
// });
// Fechar popup ao clicar fora
var popupAcoesOverlay = document.getElementById('popupAcoesOverlay');
if (popupAcoesOverlay) {
    popupAcoesOverlay.addEventListener('click', fecharPopupAcoes);
}

function switchTabSetor(tab) {
    document.querySelectorAll('.popup-tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.popup-tab-content').forEach(c => c.classList.remove('active'));
    if (tab === 'info') {
        document.querySelector('.popup-tab:nth-child(1)').classList.add('active');
        document.getElementById('info-tab-setor').classList.add('active');
    } else if (tab === 'funcionarios') {
        document.querySelector('.popup-tab:nth-child(2)').classList.add('active');
        document.getElementById('funcionarios-tab-setor').classList.add('active');
        carregarFuncionariosSetor();
    } else if (tab === 'actions') {
        document.querySelector('.popup-tab:nth-child(3)').classList.add('active');
        document.getElementById('actions-tab-setor').classList.add('active');
    }
}
function carregarFuncionariosSetor() {
    var setorId = document.getElementById('edit_setor_id').value;
    var tbody = document.getElementById('funcionariosSetorBody');
    var loading = document.getElementById('funcionarios-loading');
    var erro = document.getElementById('funcionarios-erro');
    tbody.innerHTML = '';
    erro.style.display = 'none';
    loading.style.display = 'block';
    fetch('buscar-funcionarios-por-setor.php?setor=' + encodeURIComponent(setorId))
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';
            if (data.success && data.funcionarios.length > 0) {
                tbody.innerHTML = data.funcionarios.map(f =>
                    `<tr><td>${f.id}</td><td>${f.nome.replace(/</g,'&lt;')}</td><td>${f.funcao ? f.funcao.replace(/</g,'&lt;') : ''}</td><td>${f.status}</td></tr>`
                ).join('');
            } else {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align:center; color:#9ca3af;">Nenhum funcionário encontrado para este setor.</td></tr>';
            }
        })
        .catch(() => {
            loading.style.display = 'none';
            erro.style.display = 'block';
            erro.textContent = 'Erro ao buscar funcionários.';
            tbody.innerHTML = '';
        });
}
// Dropdown de exportação na aba Funcionários
const exportBtn = document.getElementById('exportDropdownBtn');
const exportMenu = document.getElementById('exportDropdownMenu');
if (exportBtn && exportMenu) {
    exportBtn.addEventListener('click', function(e) {
        exportMenu.style.display = exportMenu.style.display === 'block' ? 'none' : 'block';
        e.stopPropagation();
    });
    document.addEventListener('click', function(e) {
        if (!exportMenu.contains(e.target) && e.target !== exportBtn) {
            exportMenu.style.display = 'none';
        }
    });
}
function getSetorEmpresaInfo() {
    return {
        setor_codigo: document.getElementById('edit_setor_id').value,
        setor_nome: document.getElementById('edit_setor_nome').value,
        empresa_codigo: document.getElementById('edit_setor_empresa_codigo').value,
        empresa_nome: document.getElementById('edit_setor_empresa').value
    };
}
function exportFuncionariosSetorExcel() {
    const info = getSetorEmpresaInfo();
    const table = document.getElementById('funcionariosSetorTable');
    let wb = XLSX.utils.book_new();
    // Cabeçalho customizado
    let ws_data = [
        [
            'Setor:', info.setor_codigo + ' - ' + info.setor_nome
        ],
        [
            'Empresa:', info.empresa_codigo + ' - ' + info.empresa_nome
        ],
        []
    ];
    // Adiciona cabeçalho da tabela
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.innerText);
    ws_data.push(headers);
    // Adiciona dados
    Array.from(table.querySelectorAll('tbody tr')).forEach(tr => {
        const row = Array.from(tr.querySelectorAll('td')).map(td => td.innerText);
        if (row.length === headers.length) ws_data.push(row);
    });
    let ws = XLSX.utils.aoa_to_sheet(ws_data);
    XLSX.utils.book_append_sheet(wb, ws, 'Funcionarios');
    XLSX.writeFile(wb, 'funcionarios_setor.xlsx');
    exportMenu.style.display = 'none';
}
function exportFuncionariosSetorPDF() {
    const info = getSetorEmpresaInfo();
    const table = document.getElementById('funcionariosSetorTable');
    const { jsPDF } = window.jspdf;
    let doc = new jsPDF('p', 'pt', 'a4');
    let y = 30;
    doc.setFontSize(13);
    doc.text('Setor: ' + info.setor_codigo + ' - ' + info.setor_nome, 40, y);
    y += 18;
    doc.text('Empresa: ' + info.empresa_codigo + ' - ' + info.empresa_nome, 40, y);
    y += 18;
    doc.autoTable({
        html: '#funcionariosSetorTable',
        startY: y + 10,
        margin: { left: 40, right: 40 },
        styles: { fontSize: 11 },
        headStyles: { fillColor: [37, 99, 235] }
    });
    doc.save('funcionarios_setor.pdf');
    exportMenu.style.display = 'none';
}
</script>
</body>
</html> 
<?php
include 'conexao.php';

// Verificar se os dados foram enviados
if (!isset($_POST['tipo']) || !isset($_POST['codigo']) || !isset($_POST['itens'])) {
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$tipo = $_POST['tipo'];
$codigo = $_POST['codigo'];
$itens = json_decode($_POST['itens'], true);

// Iniciar transação
$conn->begin_transaction();

try {
    // Determinar a tabela e o campo de código corretos com base no tipo
    $tabela_itens = '';
    $campo_codigo = '';
    
    switch ($tipo) {
        case 'requisicao':
            $tabela_itens = 'itens_solicitacao';
            $campo_codigo = 'codigo_solicitacao';
            break;
        case 'pedido_mensal':
            $tabela_itens = 'itens_pedido_mensal';
            $campo_codigo = 'codigo_pedido';
            break;
        default:
            throw new Exception('Tipo de solicitação inválido');
    }
    
    // Excluir itens existentes
    $stmt = $conn->prepare("DELETE FROM $tabela_itens WHERE $campo_codigo = ?");
    $stmt->bind_param("i", $codigo);
    $stmt->execute();
    
    // Inserir novos itens
    if (!empty($itens)) {
        $stmt = $conn->prepare("INSERT INTO $tabela_itens ($campo_codigo, produto, quantidade) VALUES (?, ?, ?)");
        
        foreach ($itens as $item) {
            $stmt->bind_param("isi", $codigo, $item['produto'], $item['quantidade']);
            $stmt->execute();
        }
    }
    
    // Commit da transação
    $conn->commit();
    
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    // Rollback em caso de erro
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
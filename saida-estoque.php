<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

// Obter o próximo código de saída
$result = $conn->query("SELECT MAX(id) as ultimo FROM saidas_estoque");
$row = $result->fetch_assoc();
$codigo_saida = isset($row['ultimo']) ? $row['ultimo'] + 1 : 1;

// Processar requisição AJAX para buscar produto
if (isset($_GET['codigo'])) {
    $codigo = $_GET['codigo'];
    $stmt = $conn->prepare("SELECT nome, quantidade FROM produtos WHERE codigo = ?");
    $stmt->bind_param("s", $codigo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        echo json_encode($row);
    } else {
        echo json_encode(["nome" => "", "quantidade" => 0]);
    }
    exit;
}

// Processar requisição AJAX para buscar empresas
if (isset($_GET['buscar_empresas'])) {
    $termo = isset($_GET['termo']) ? $_GET['termo'] : '';
    $like = "%$termo%";
    $sql = "SELECT codigo_empresa, nome_empresa FROM empresas WHERE nome_empresa LIKE ? AND status = 'ativo'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $like);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $empresas = [];
    while ($row = $result->fetch_assoc()) {
        $empresas[] = $row;
    }
    
    echo json_encode($empresas);
    exit;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saída de Estoque</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            background-color: #111;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .content-container {
            background-color: #ffffff;
            border-radius: 12px;
            margin: 20px 20px 20px 290px;
            padding: 40px;
            min-height: calc(100vh - 40px);
            border: 1px solid #e5e7eb;
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }
        
        form {
            max-width: 600px;
            margin: 0 auto;
            background: #fafafa;
            padding: 40px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-row {
            display: flex;
            align-items: flex-end;
            gap: 12px;
        }
        
        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }
        
        .search-btn {
            width: 48px;
            height: 41px;
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
            padding: 0 !important;
        }
        
        .search-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .search-btn:active {
            transform: translateY(0);
        }
        
        .search-icon {
            width: 24px !important;
            height: 24px !important;
            stroke: white !important;
            fill: none !important;
            display: block !important;
        }
        
        .divider {
            height: 1px;
            background: #d1d5db;
            margin: 32px 0;
            width: 100%;
        }
        
        label {
            display: block;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            color: #111827;
            background: #ffffff;
            transition: all 0.2s ease;
            box-sizing: border-box;
            margin-bottom: 0;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        input:hover, select:hover, textarea:hover {
            border-color: #9ca3af;
        }
        
        button, .btn, .btn-confirm, .btn-cancel {
            width: 100%;
            padding: 14px 24px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
        }
        
        button:hover, .btn:hover, .btn-confirm:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        button:active, .btn:active, .btn-confirm:active {
            transform: translateY(0);
        }
        
        .btn-cancel {
            background: #6b7280;
        }
        .btn-cancel:hover {
            background: #374151;
        }
        
        .error-message {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        /* Seção Produtos Inseridos */
        .produtos-inseridos {
            max-width: 680px;
            margin: 40px auto 0 auto;
            background: #ffffff;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .produtos-inseridos h2 {
            background: #f8fafc;
            color: #111827;
            font-size: 18px;
            font-weight: 600;
            padding: 20px 24px;
            margin: 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .produtos-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .produtos-table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            padding: 16px 24px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .produtos-table td {
            padding: 16px 24px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
            font-size: 14px;
        }
        
        .produtos-table tr:hover {
            background: #f9fafb;
        }
        
        .produtos-table input[type="number"] {
            width: 80px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .btn-remove {
            background: #dc2626;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-remove:hover {
            background: #b91c1c;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 24px;
            color: #6b7280;
            font-size: 14px;
        }
        
        /* Popup melhorado */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 1000;
        }
        
        .popup-overlay.active {
            display: flex;
        }
        
        .popup {
            background: #fff;
            padding: 32px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            max-width: 800px;
            width: 95%;
            max-height: 80vh;
            overflow-y: auto;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .popup h3 {
            margin: 0;
            color: #111827;
            font-size: 20px;
            font-weight: 600;
        }
        
        .popup-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
            width: auto;
            margin: 0;
        }
        
        .popup-close:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .popup-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .popup input[type="text"] {
            width: 100%;
            max-width: 400px;
            margin-bottom: 20px;
        }
        
        .popup-table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        .popup table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .popup table th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .popup table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #111827;
            font-size: 14px;
        }
        
        .popup table tr:hover {
            background: #f9fafb;
        }
        
        .btn-select {
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn-select:hover {
            background: #1d4ed8;
        }
        
        .btn-close {
            background: #6b7280;
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            margin-top: 16px;
            align-self: flex-end;
            transition: background 0.2s;
            width: auto;
        }
        
        .btn-close:hover {
            background: #374151;
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                padding: 20px;
            }
            form {
                padding: 24px;
            }
            h1 {
                font-size: 24px;
            }
            .form-row {
                flex-direction: column;
                gap: 16px;
            }
            .search-btn {
                align-self: flex-end;
            }
        }
        
        /* Estilos para notificações */
        .notificacao {
            position: fixed;
            top: 20px;
            right: -300px;
            width: 280px;
            padding: 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            transition: right 0.5s ease;
        }

        .notificacao.success {
            background-color: #4CAF50;
        }

        .notificacao.error {
            background-color: #f44336;
        }

        .form-buttons {
            max-width: 680px;
            margin: 32px auto 0 auto;
            display: flex;
            flex-direction: row;
            gap: 10px;
        }

        /* Popup de aviso de limite */
        .popup-limite-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10001;
        }

        .popup-limite-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 500px;
            width: 90%;
            animation: popup-bounce 0.3s ease-out;
        }

        .popup-limite-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .popup-limite-icon.aviso {
            color: #f59e0b;
        }

        .popup-limite-icon.bloqueado {
            color: #dc2626;
        }

        .popup-limite-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #111827;
        }

        .popup-limite-message {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .popup-limite-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .popup-limite-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .popup-limite-btn.continuar {
            background: #059669;
            color: white;
        }

        .popup-limite-btn.continuar:hover {
            background: #047857;
        }

        .popup-limite-btn.cancelar {
            background: #6b7280;
            color: white;
        }

        .popup-limite-btn.cancelar:hover {
            background: #4b5563;
        }

        .popup-limite-btn.ok {
            background: #2563eb;
            color: white;
        }

        .popup-limite-btn.ok:hover {
            background: #1d4ed8;
        }

        @keyframes popup-bounce {
            0% {
                transform: scale(0.7);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
    <h1>Saída de Estoque</h1>

    <form id="saidaForm">
        <div class="form-row">
            <div class="form-group">
                <label for="codigo_saida">Código da Saída:</label>
                <input type="text" id="codigo_saida" name="codigo_saida" value="<?php echo $codigo_saida; ?>" readonly>
            </div>
            <div class="form-group">
                <label for="responsavel">Responsável pela Saída:</label>
                <input type="text" id="responsavel" name="responsavel" value="<?php echo htmlspecialchars($_SESSION['nome_usuario']); ?>" readonly required>
            </div>
            <div class="form-group">
                <label for="data_saida">Data da Saída:</label>
                <input type="date" id="data_saida" name="data_saida" value="<?php echo date('Y-m-d'); ?>" required>
            </div>
        </div>
        
        <div class="divider"></div>
        
        <div class="form-group">
            <label for="empresa_destino_nome">Empresa de Destino:</label>
            <div class="form-row">
                <div class="form-group">
                    <input type="hidden" id="empresa_destino" name="empresa_destino" required>
                    <input type="text" id="empresa_destino_nome" name="empresa_destino_nome" required placeholder="Digite ou selecione a empresa" readonly>
                </div>
                <button type="button" class="search-btn" onclick="abrirPopupEmpresas()" title="Pesquisar Empresa">
                    <svg class="search-icon" viewBox="0 0 24 24">
                      <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                      <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="form-group">
            <label for="destinatario">Destinatário:</label>
            <div class="form-row">
                <div class="form-group">
                    <input type="text" id="destinatario" name="destinatario" required placeholder="Digite o nome do destinatário">
                </div>
                <button type="button" class="search-btn" onclick="abrirPopupPessoas()" title="Pesquisar Pessoa">
                    <svg class="search-icon" viewBox="0 0 24 24">
                      <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                      <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="form-group">
            <label for="setor_destinatario">Setor do Destinatário:</label>
            <input type="text" id="setor_destinatario" name="setor_destinatario" readonly>
        </div>
        <input type="hidden" id="setor_destinatario_id" name="setor_destinatario_id">
        <div class="form-group">
            <label for="motivo">Motivo da Saída:</label>
            <select id="motivo" name="motivo" required>
                <option value="">Selecione um motivo</option>
                <option value="Requisição">Requisição</option>
                <option value="Pedido Mensal">Pedido Mensal</option>
                <option value="Pedido Especial">Pedido Especial</option>
                <option value="Transferência">Transferência</option>
                <option value="Devolução">Devolução</option>
                <option value="Descarte">Descarte</option>
                <option value="Outro">Outro</option>
            </select>
        </div>
        <div class="form-group">
            <label for="observacoes">Observações:</label>
            <textarea id="observacoes" name="observacoes"></textarea>
        </div>
        
        <div class="divider"></div>
        
        <h2>Produtos</h2>
        <div class="form-group">
            <label for="codigo">Código do Produto:</label>
            <div class="form-row">
                <div class="form-group">
                    <input type="text" id="codigo" name="codigo" oninput="buscarNomeProduto()" placeholder="Digite o código do produto">
                </div>
                <button type="button" class="search-btn" onclick="abrirPopupProdutos()" title="Pesquisar Produto">
                    <svg class="search-icon" viewBox="0 0 24 24">
                      <circle cx="11" cy="11" r="7" stroke="white" stroke-width="2.2" fill="none"/>
                      <line x1="16.5" y1="16.5" x2="21" y2="21" stroke="white" stroke-width="2.2" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="form-group">
            <label for="nome">Nome do Produto:</label>
            <input type="text" id="nome" name="nome" readonly>
        </div>
        <div class="form-group">
            <label for="quantidade">Quantidade:</label>
            <input type="number" id="quantidade" name="quantidade" min="1">
        </div>
        <div class="form-group">
            <button type="button" onclick="inserirProduto()" class="btn-adicionar">Adicionar Produto</button>
        </div>
    </form>

    <div class="produtos-inseridos">
        <h2>Produtos Inseridos</h2>
        <table class="produtos-table" id="produtos-tabela">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Quantidade</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <tr class="empty-state-row">
                    <td colspan="4" class="empty-state">Nenhum produto inserido ainda</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="form-buttons">
        <button type="button" onclick="registrarSaida()" class="btn-confirm">Registrar Saída</button>
    </div>

    <!-- Popup de confirmação de saída -->
    <div class="popup-overlay" id="confirmacaoPopup">
        <div class="popup">
            <div class="popup-header">
                <h3>Confirmar Saída de Estoque</h3>
                <button class="popup-close" onclick="fecharConfirmacaoPopup()">&times;</button>
            </div>
            <div class="popup-content">
                <p>Tem certeza que deseja registrar a saída dos seguintes itens?</p>
                
                <div class="popup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Nome</th>
                                <th>Quantidade</th>
                            </tr>
                        </thead>
                        <tbody id="tabelaConfirmacao">
                            <!-- Será preenchido dinamicamente -->
                        </tbody>
                    </table>
                </div>
                
                <div class="form-buttons">
                    <button onclick="fecharConfirmacaoPopup()" class="btn btn-cancel">Cancelar</button>
                    <button onclick="confirmarSaida()" class="btn btn-confirm">Confirmar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pop-up de Empresas -->
    <div class="popup-overlay" id="popupEmpresasOverlay">
        <div class="popup">
            <div class="popup-header">
                <h3>Selecionar Empresa</h3>
                <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
            </div>
            <div class="popup-content">
                <input type="text" id="pesquisaEmpresa" placeholder="Pesquisar empresa..." oninput="filtrarEmpresas()">
                <div class="popup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Nome</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody id="listaEmpresas">
                            <!-- Será preenchido via JS -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pop-up de Produtos -->
    <div class="popup-overlay" id="popupProdutosOverlay">
        <div class="popup">
            <div class="popup-header">
                <h3>Selecionar Produto</h3>
                <button class="popup-close" onclick="fecharPopupProdutos()">&times;</button>
            </div>
            <div class="popup-content">
                <input type="text" id="pesquisaProduto" placeholder="Pesquisar produto..." oninput="filtrarProdutos()">
                <div class="popup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Nome</th>
                                <th>Estoque</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody id="listaProdutos">
                            <?php
                            $result = $conn->query("SELECT codigo, nome, quantidade FROM produtos WHERE status = 'ativo'");
                            while ($row = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?= $row['codigo'] ?></td>
                                    <td><?= $row['nome'] ?></td>
                                    <td><?= $row['quantidade'] ?></td>
                                    <td><button class="btn-select" onclick="selecionarProduto('<?= $row['codigo'] ?>', '<?= $row['nome'] ?>')">Selecionar</button></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pop-up de Funcionários -->
    <div class="popup-overlay" id="popupPessoasOverlay">
        <div class="popup">
            <div class="popup-header">
                <h3>Selecionar Funcionário</h3>
                <button class="popup-close" onclick="fecharPopupPessoas()">&times;</button>
            </div>
            <div class="popup-content">
                <input type="text" id="pesquisaPessoa" placeholder="Pesquisar funcionário..." oninput="filtrarPessoas()">
                <div class="popup-table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome</th>
                                <th>Setor</th>
                                <th>Ação</th>
                            </tr>
                        </thead>
                        <tbody id="listaPessoas">
                            <?php
                            $result = $conn->query("SELECT id, nome, setor FROM pessoas WHERE status = 'ativo'");
                            while ($row = $result->fetch_assoc()): ?>
                                <tr>
                                    <td><?= $row['id'] ?></td>
                                    <td><?= $row['nome'] ?></td>
                                    <td><?= $row['setor'] ?></td>
                                    <td><button class="btn-select" onclick="selecionarPessoa('<?= $row['id'] ?>', '<?= $row['nome'] ?>', '<?= $row['setor'] ?>')">Selecionar</button></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Array para armazenar produtos inseridos
    let produtosInseridos = [];

    // Função para buscar nome do produto pelo código
    function buscarNomeProduto() {
        const codigo = document.getElementById("codigo").value;
        if (codigo.trim() === "") {
            document.getElementById("nome").value = "";
            return;
        }
        
        fetch(`saida-estoque.php?codigo=${codigo}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById("nome").value = data.nome || "";
                // Se o produto não existir, limpar o campo
                if (!data.nome) {
                    alert("Produto não encontrado!");
                    document.getElementById("codigo").value = "";
                }
            })
            .catch(error => {
                console.error("Erro:", error);
            });
    }

    // Função para inserir produto na tabela
    function inserirProduto() {
        const codigo = document.getElementById("codigo").value;
        const nome = document.getElementById("nome").value;
        const quantidade = parseInt(document.getElementById("quantidade").value);
        
        // Validar campos
        if (!codigo || !nome || !quantidade || quantidade <= 0) {
            alert("Preencha todos os campos corretamente!");
            return;
        }
        
        // Verificar se o produto já foi inserido
        const produtoExistente = produtosInseridos.findIndex(p => p.codigo === codigo);
        
        if (produtoExistente !== -1) {
            // Atualizar quantidade se o produto já existir
            produtosInseridos[produtoExistente].quantidade += quantidade;
        } else {
            // Adicionar novo produto
            produtosInseridos.push({
                codigo: codigo,
                nome: nome,
                quantidade: quantidade
            });
        }
        
        // Atualizar tabela
        atualizarTabelaProdutos();
        
        // Limpar campos
        document.getElementById("codigo").value = "";
        document.getElementById("nome").value = "";
        document.getElementById("quantidade").value = "";
        
        // Focar no campo de código
        document.getElementById("codigo").focus();
    }

    // Função para atualizar a tabela de produtos
    function atualizarTabelaProdutos() {
        const tbody = document.getElementById("produtos-tabela").getElementsByTagName("tbody")[0];
        tbody.innerHTML = "";
        
        if (produtosInseridos.length === 0) {
            tbody.innerHTML = '<tr class="empty-state-row"><td colspan="4" class="empty-state">Nenhum produto inserido ainda</td></tr>';
            return;
        }
        
        produtosInseridos.forEach((produto, index) => {
            const tr = document.createElement("tr");
            tr.innerHTML = `
                <td>${produto.codigo}</td>
                <td>${produto.nome}</td>
                <td>${produto.quantidade}</td>
                <td>
                    <button type="button" class="btn-remove" onclick="removerProduto(${index})">Excluir</button>
                </td>
            `;
            tbody.appendChild(tr);
        });
    }

    // Função para remover produto da tabela
    function removerProduto(index) {
        produtosInseridos.splice(index, 1);
        atualizarTabelaProdutos();
    }

    // Função para registrar saída
    function registrarSaida() {
        // Validar se há produtos
        if (produtosInseridos.length === 0) {
            alert("Adicione pelo menos um produto!");
            return;
        }
        
        // Validar campos obrigatórios
        const responsavel = document.getElementById("responsavel").value;
        const data_saida = document.getElementById("data_saida").value;
        const empresa_destino = document.getElementById("empresa_destino").value;
        const motivo = document.getElementById("motivo").value;
        
        console.log('empresa_destino:', empresa_destino, 'responsavel:', responsavel, 'data_saida:', data_saida, 'motivo:', motivo);
        
        if (!responsavel || !data_saida || !empresa_destino || !motivo) {
            alert("Preencha todos os campos obrigatórios!");
            return;
        }
        
        // Preencher tabela de confirmação
        preencherTabelaConfirmacao();
        
        // Mostrar popup de confirmação
        document.getElementById("confirmacaoPopup").classList.add("active");
    }

    // Função para fechar popup de confirmação
    function fecharConfirmacaoPopup() {
        document.getElementById("confirmacaoPopup").classList.remove("active");
    }

    // Inicializar tabela vazia e configurar eventos
    document.addEventListener("DOMContentLoaded", function() {
        atualizarTabelaProdutos();
        
        // Definir data atual no campo de data
        const hoje = new Date().toISOString().split('T')[0];
        document.getElementById("data_saida").value = hoje;
        
        // Adicionar evento para fechar popup ao clicar fora
        document.getElementById("confirmacaoPopup").addEventListener("click", function(e) {
            if (e.target === this) {
                fecharConfirmacaoPopup();
            }
        });
        
        // Adicionar eventos para fechar popups ao clicar fora
        document.getElementById("popupEmpresasOverlay").addEventListener("click", function(e) {
            if (e.target === this) {
                fecharPopupEmpresas();
            }
        });
        
        document.getElementById("popupProdutosOverlay").addEventListener("click", function(e) {
            if (e.target === this) {
                fecharPopupProdutos();
            }
        });
        
        document.getElementById("popupPessoasOverlay").addEventListener("click", function(e) {
            if (e.target === this) {
                fecharPopupPessoas();
            }
        });
    });

    // Função para confirmar e processar a saída
    function confirmarSaida() {
        // Verificar limites antes de confirmar
        const empresa_destino_nome = document.getElementById("empresa_destino_nome").value;
        const setor = document.getElementById("setor_destinatario").value;

        if (!empresa_destino_nome) {
            alert("Selecione uma empresa antes de confirmar a saída");
            return;
        }

        // Calcular valor total dos produtos
        let valorTotal = 0;
        produtosInseridos.forEach(produto => {
            valorTotal += produto.quantidade * 15; // Valor médio estimado por produto
        });

        // Verificar limites antes de prosseguir
        verificarLimitesAntesEnvio(empresa_destino_nome, setor, valorTotal, function(podeEnviar) {
            if (podeEnviar) {
                processarSaida();
            }
        });
    }
    
    // Função para processar a saída (separada da verificação de limites)
    function processarSaida() {
        // Obter dados do formulário
        let formData = {
            codigo_saida: document.getElementById("codigo_saida").value,
            responsavel: document.getElementById("responsavel").value,
            data_saida: document.getElementById("data_saida").value,
            empresa_destino: document.getElementById("empresa_destino").value,
            destinatario: document.getElementById("destinatario").value,
            setor_destinatario: document.getElementById("setor_destinatario").value,
            setor_destinatario_id: document.getElementById("setor_destinatario_id").value,
            motivo: document.getElementById("motivo").value,
            observacoes: document.getElementById("observacoes").value,
            produtos: produtosInseridos
        };
        
        console.log("Enviando dados:", formData); // Log para debug
        
        // Enviar dados para o servidor
        fetch("processar-saida.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(formData)
        })
        .then(response => {
            console.log("Status da resposta:", response.status); // Log para debug
            return response.json();
        })
        .then(data => {
            console.log("Resposta do servidor:", data); // Log para debug
            
            if (data.success) {
                // Fechar popup de confirmação
                fecharConfirmacaoPopup();
                
                // Mostrar notificação de sucesso
                mostrarNotificacao("Saída registrada com sucesso!", "success");
                
                // Limpar o formulário para uma nova saída
                limparFormulario();
                
                // Atualizar o código da saída para o próximo
                atualizarCodigoSaida();
            } else {
                mostrarNotificacao("Erro ao registrar saída: " + data.message, "error");
                fecharConfirmacaoPopup();
            }
        })
        .catch(error => {
            console.error("Erro:", error);
            mostrarNotificacao("Erro ao processar a requisição!", "error");
            fecharConfirmacaoPopup();
        });
    }

    // Função para mostrar notificação
    function mostrarNotificacao(mensagem, tipo) {
        const notificacao = document.createElement("div");
        notificacao.className = "notificacao " + tipo;
        notificacao.textContent = mensagem;
        
        document.body.appendChild(notificacao);
        
        // Animar entrada
        setTimeout(() => {
            notificacao.style.right = "20px";
        }, 10);
        
        // Remover após 3 segundos
        setTimeout(() => {
            notificacao.style.right = "-300px";
            setTimeout(() => {
                document.body.removeChild(notificacao);
            }, 500);
        }, 3000);
    }

    // Funções para manipular o popup de empresas
    function abrirPopupEmpresas() {
        document.getElementById("popupEmpresasOverlay").classList.add("active");
        document.getElementById("pesquisaEmpresa").focus();
        filtrarEmpresas(); // Carregar lista ao abrir
    }

    function fecharPopupEmpresas() {
        document.getElementById("popupEmpresasOverlay").classList.remove("active");
    }

    function selecionarEmpresa(codigo, nome) {
        document.getElementById("empresa_destino").value = codigo;
        document.getElementById("empresa_destino_nome").value = nome;
        fecharPopupEmpresas();
    }

    function filtrarEmpresas() {
        const termo = document.getElementById("pesquisaEmpresa").value.toLowerCase();
        // Fazer requisição AJAX para buscar empresas
        fetch(`saida-estoque.php?buscar_empresas=1&termo=${termo}`)
            .then(response => response.json())
            .then(empresas => {
                const listaEmpresas = document.getElementById("listaEmpresas");
                listaEmpresas.innerHTML = "";
                if (empresas.length === 0) {
                    listaEmpresas.innerHTML = '<tr><td colspan="3">Nenhuma empresa encontrada</td></tr>';
                    return;
                }
                empresas.forEach(empresa => {
                    const tr = document.createElement("tr");
                    tr.innerHTML = `
                        <td>${empresa.codigo_empresa}</td>
                        <td>${empresa.nome_empresa}</td>
                        <td><button class="btn-select" onclick="selecionarEmpresa('${empresa.codigo_empresa}', '${empresa.nome_empresa}')">Selecionar</button></td>
                    `;
                    listaEmpresas.appendChild(tr);
                });
            })
            .catch(error => {
                console.error("Erro:", error);
            });
    }

    // Funções para manipular o popup de produtos
    function abrirPopupProdutos() {
        document.getElementById("popupProdutosOverlay").classList.add("active");
        document.getElementById("pesquisaProduto").focus();
    }

    function fecharPopupProdutos() {
        document.getElementById("popupProdutosOverlay").classList.remove("active");
    }

    function selecionarProduto(codigo, nome) {
        document.getElementById("codigo").value = codigo;
        document.getElementById("nome").value = nome;
        fecharPopupProdutos();
        document.getElementById("quantidade").focus();
    }

    function filtrarProdutos() {
        const termo = document.getElementById("pesquisaProduto").value.toLowerCase();
        const linhas = document.getElementById("listaProdutos").getElementsByTagName("tr");
        
        for (let i = 0; i < linhas.length; i++) {
            const codigo = linhas[i].getElementsByTagName("td")[0].textContent.toLowerCase();
            const nome = linhas[i].getElementsByTagName("td")[1].textContent.toLowerCase();
            
            if (codigo.includes(termo) || nome.includes(termo)) {
                linhas[i].style.display = "";
            } else {
                linhas[i].style.display = "none";
            }
        }
    }

    // Função para preencher a tabela de confirmação
    function preencherTabelaConfirmacao() {
        const tbody = document.getElementById("tabelaConfirmacao");
        tbody.innerHTML = "";
        
        produtosInseridos.forEach(produto => {
            const tr = document.createElement("tr");
            tr.innerHTML = `
                <td>${produto.codigo}</td>
                <td>${produto.nome}</td>
                <td>${produto.quantidade}</td>
            `;
            tbody.appendChild(tr);
        });
    }

    // Função para verificar limites de gasto
    function verificarLimitesGasto() {
        const empresa = document.getElementById("empresa_destino").value;
        const setor = document.getElementById("setor_destinatario_id").value;
        
        if (!empresa) {
            alert("Selecione uma empresa antes de verificar os limites");
            return;
        }
        
        // Calcular valor total dos produtos
        let valorTotal = 0;
        produtosInseridos.forEach(produto => {
            // Aqui você pode adicionar lógica para obter o preço do produto
            // Por enquanto, vamos usar um valor estimado baseado na quantidade
            valorTotal += produto.quantidade * 10; // Valor estimado por unidade
        });
        
        // Fazer requisição para verificar limites
        fetch(`verificar-limite-gasto.php?empresa=${encodeURIComponent(empresa)}&setor=${encodeURIComponent(setor)}&valor=${valorTotal}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.tem_aviso) {
                    let mensagem = "⚠️ ATENÇÃO - Limites de Gasto:\n\n";
                    
                    data.avisos.forEach(aviso => {
                        if (aviso.tipo === 'excedido') {
                            mensagem += `🚨 LIMITE EXCEDIDO para ${aviso.categoria.toUpperCase()}: ${aviso.nome}\n`;
                            mensagem += `   Limite: R$ ${aviso.limite.toFixed(2)}\n`;
                            mensagem += `   Gasto atual: R$ ${aviso.gasto_atual.toFixed(2)}\n`;
                            mensagem += `   Gasto projetado: R$ ${aviso.gasto_projetado.toFixed(2)}\n`;
                            mensagem += `   Excesso: R$ ${aviso.excesso.toFixed(2)}\n`;
                            mensagem += `   Período: ${aviso.periodo}\n\n`;
                        } else {
                            mensagem += `⚠️ AVISO para ${aviso.categoria.toUpperCase()}: ${aviso.nome}\n`;
                            mensagem += `   Limite: R$ ${aviso.limite.toFixed(2)}\n`;
                            mensagem += `   Gasto atual: R$ ${aviso.gasto_atual.toFixed(2)}\n`;
                            mensagem += `   Gasto projetado: R$ ${aviso.gasto_projetado.toFixed(2)}\n`;
                            mensagem += `   Saldo restante: R$ ${aviso.saldo_restante.toFixed(2)}\n`;
                            mensagem += `   Período: ${aviso.periodo}\n\n`;
                        }
                    });
                    
                    mensagem += "Deseja continuar mesmo assim?";
                    
                    if (!confirm(mensagem)) {
                        return false;
                    }
                }
                return true;
            })
            .catch(error => {
                console.error("Erro ao verificar limites:", error);
                return true; // Continuar mesmo se houver erro na verificação
        });
    }

    // Função para limpar o formulário após uma saída bem-sucedida
    function limparFormulario() {
        // Resetar campos para valores padrão
        document.getElementById("responsavel").value = "<?php echo htmlspecialchars($_SESSION['nome_usuario']); ?>";
        document.getElementById("data_saida").value = "<?php echo date('Y-m-d'); ?>";
        document.getElementById("empresa_destino").value = "";
        document.getElementById("empresa_destino_nome").value = "";
        document.getElementById("destinatario").value = "";
        document.getElementById("setor_destinatario").value = "";
        document.getElementById("setor_destinatario_id").value = "";
        document.getElementById("motivo").selectedIndex = 0;
        document.getElementById("observacoes").value = "";
        
        // Limpar campos de produto
        document.getElementById("codigo").value = "";
        document.getElementById("nome").value = "";
        document.getElementById("quantidade").value = "";
        
        // Limpar a tabela de produtos inseridos
        produtosInseridos = [];
        atualizarTabelaProdutos();
    }

    // Função para atualizar o código da saída para o próximo
    function atualizarCodigoSaida() {
        // Obter o código atual e incrementar
        const codigoAtual = parseInt(document.getElementById("codigo_saida").value);
        const proximoCodigo = codigoAtual + 1;
        
        // Atualizar o campo com o próximo código
        document.getElementById("codigo_saida").value = proximoCodigo;
        
        // Alternativamente, podemos fazer uma requisição AJAX para obter o próximo código do servidor
        // fetch("obter-proximo-codigo.php")
        //     .then(response => response.json())
        //     .then(data => {
        //         document.getElementById("codigo_saida").value = data.proximo_codigo;
        //     });
    }

    // Função para verificar limites antes do envio
    function verificarLimitesAntesEnvio(empresa, setor, valorTotal, callback) {
        fetch('verificar-limite-antes-envio.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                empresa: empresa,
                setor: setor,
                valor_total: valorTotal
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (!data.tem_limite) {
                    callback(true);
                } else if (data.status === 'limite_excedido') {
                    mostrarPopupLimite(data.message, 'bloqueado', false, callback);
                } else if (data.status === 'aviso') {
                    mostrarPopupLimite(data.message, 'aviso', true, callback);
                } else {
                    callback(true);
                }
            } else {
                console.error('Erro ao verificar limite:', data.message);
                callback(true);
            }
        })
        .catch(error => {
            console.error('Erro na requisição:', error);
            callback(true);
        });
    }

    // Função para mostrar popup de limite
    function mostrarPopupLimite(mensagem, tipo, podeConfirmar, callback) {
        let popup = document.getElementById('popup-limite-overlay');
        if (!popup) {
            popup = document.createElement('div');
            popup.id = 'popup-limite-overlay';
            popup.className = 'popup-limite-overlay';
            document.body.appendChild(popup);
        }

        const icone = tipo === 'aviso' ? '⚠️' : '🚫';
        const titulo = tipo === 'aviso' ? 'Aviso de Limite' : 'Limite Excedido';

        let botoes = '';
        if (podeConfirmar) {
            botoes = `
                <button class="popup-limite-btn continuar" onclick="confirmarEnvioComLimite(true)">Continuar Mesmo Assim</button>
                <button class="popup-limite-btn cancelar" onclick="confirmarEnvioComLimite(false)">Cancelar</button>
            `;
        } else {
            botoes = `
                <button class="popup-limite-btn ok" onclick="confirmarEnvioComLimite(false)">Entendi</button>
            `;
        }

        popup.innerHTML = `
            <div class="popup-limite-content">
                <div class="popup-limite-icon ${tipo}">${icone}</div>
                <div class="popup-limite-title">${titulo}</div>
                <div class="popup-limite-message">${mensagem}</div>
                <div class="popup-limite-buttons">
                    ${botoes}
                </div>
            </div>
        `;

        popup.style.display = 'flex';
        window.limiteCallback = callback;
    }

    // Função para confirmar envio com limite
    function confirmarEnvioComLimite(prosseguir) {
        document.getElementById('popup-limite-overlay').style.display = 'none';
        if (window.limiteCallback) {
            window.limiteCallback(prosseguir);
            window.limiteCallback = null;
        }
    }

    // Funções para manipular o popup de pessoas
    function abrirPopupPessoas() {
        // Verificar se uma empresa foi selecionada
        const empresaCodigo = document.getElementById("empresa_destino").value;

        if (!empresaCodigo) {
            alert("Por favor, selecione uma empresa primeiro!");
            return;
        }

        // Carregar funcionários da empresa selecionada
        carregarFuncionariosPorEmpresa(empresaCodigo);

        document.getElementById("popupPessoasOverlay").classList.add("active");
        document.getElementById("pesquisaPessoa").focus();
    }

    function fecharPopupPessoas() {
        document.getElementById("popupPessoasOverlay").classList.remove("active");
    }

    // Função para carregar funcionários por empresa
    function carregarFuncionariosPorEmpresa(empresaCodigo) {
        const listaPessoas = document.getElementById("listaPessoas");

        // Mostrar loading
        listaPessoas.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px;">Carregando funcionários...</td></tr>';

        // Fazer requisição AJAX
        fetch(`buscar-funcionarios-por-empresa.php?empresa_codigo=${empresaCodigo}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.funcionarios.length > 0) {
                        let html = '';
                        data.funcionarios.forEach(funcionario => {
                            html += `
                                <tr>
                                    <td>${funcionario.id}</td>
                                    <td>${funcionario.nome}</td>
                                    <td>${funcionario.setor}</td>
                                    <td><button class="btn-select" onclick="selecionarPessoa('${funcionario.id}', '${funcionario.nome}', '${funcionario.setor}')">Selecionar</button></td>
                                </tr>
                            `;
                        });
                        listaPessoas.innerHTML = html;

                        // Atualizar título do popup para mostrar a empresa
                        const popupHeader = document.querySelector("#popupPessoasOverlay .popup-header h3");
                        popupHeader.textContent = `Funcionários - ${data.empresa.nome}`;
                    } else {
                        listaPessoas.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#888;">Nenhum funcionário encontrado para esta empresa.</td></tr>';
                    }
                } else {
                    listaPessoas.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#dc2626;">Erro ao carregar funcionários: ' + data.message + '</td></tr>';
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                listaPessoas.innerHTML = '<tr><td colspan="4" style="text-align:center; padding:20px; color:#dc2626;">Erro ao carregar funcionários.</td></tr>';
            });
    }

    function selecionarPessoa(id, nome, setorId) {
        document.getElementById("destinatario").value = nome;
        document.getElementById("setor_destinatario_id").value = setorId;
        // Usar o código do setor diretamente
        document.getElementById("setor_destinatario").value = setorId || "";
        fecharPopupPessoas();
    }

    function filtrarPessoas() {
        const termo = document.getElementById("pesquisaPessoa").value.toLowerCase();
        const linhas = document.getElementById("listaPessoas").getElementsByTagName("tr");
        
        for (let i = 0; i < linhas.length; i++) {
            const nome = linhas[i].getElementsByTagName("td")[1].textContent.toLowerCase();
            const setor = linhas[i].getElementsByTagName("td")[2].textContent.toLowerCase();
            
            if (nome.includes(termo) || setor.includes(termo)) {
                linhas[i].style.display = "";
            } else {
                linhas[i].style.display = "none";
            }
        }
    }
</script>
</body>
</html>














<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

include 'conexao.php';

$feedbackMessage = "";
$feedbackType = ""; // "success" ou "error"

// Verificar se a coluna status existe, se não, criar
$result = $conn->query("SHOW COLUMNS FROM pessoas LIKE 'status'");
if ($result->num_rows == 0) {
    $conn->query("ALTER TABLE pessoas ADD COLUMN status VARCHAR(10) DEFAULT 'ativo'");
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['delete_id']) && !empty($_POST['delete_id'])) {
        $deleteId = $_POST['delete_id'];
        $sql = "DELETE FROM pessoas WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $deleteId);
        if ($stmt->execute()) {
            header('Location: tabela-pessoas.php?status=delete-success');
        } else {
            header('Location: tabela-pessoas.php?status=error&message=' . urlencode('Erro ao excluir o registro.'));
        }
        $stmt->close();
        exit;
    } elseif (isset($_POST['inativar_id']) && !empty($_POST['inativar_id'])) {
        $pessoaId = $_POST['inativar_id'];
        $status = $_POST['status'];
        $sql = "UPDATE pessoas SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $status, $pessoaId);
        if ($stmt->execute()) {
            header('Location: tabela-pessoas.php?status=success&message=' . urlencode('Status da pessoa atualizado com sucesso!'));
        } else {
            header('Location: tabela-pessoas.php?status=error&message=' . urlencode('Erro ao atualizar status da pessoa.'));
        }
        $stmt->close();
        exit;
    }
}

// Verificando o status da URL para exibir o feedback
if (isset($_GET['status'])) {
    if ($_GET['status'] == 'edit-success') {
        $feedbackMessage = "Edição realizada com sucesso!";
        $feedbackType = "success";
    } elseif ($_GET['status'] == 'edit-error') {
        $feedbackMessage = "Erro ao editar o registro.";
        $feedbackType = "error";
    } elseif ($_GET['status'] == 'delete-success') {
        $feedbackMessage = "Exclusão realizada com sucesso!";
        $feedbackType = "delete-success";
    }
}

// Obter lista de postos para o filtro
$sql_postos = "SELECT DISTINCT posto FROM pessoas WHERE posto IS NOT NULL AND posto != '' ORDER BY posto ASC";
$result_postos = $conn->query($sql_postos);
$postos = [];
while ($row = $result_postos->fetch_assoc()) {
    $postos[] = $row['posto'];
}
// Obter lista de setores para o filtro
$sql_setores = "SELECT DISTINCT setor FROM pessoas WHERE setor IS NOT NULL AND setor != '' ORDER BY setor ASC";
$result_setores = $conn->query($sql_setores);
$setores = [];
while ($row = $result_setores->fetch_assoc()) {
    $setores[] = $row['setor'];
}

// Definir filtro de status padrão (ativo)
$statusFilter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'ativo';
$search = isset($_GET['search']) ? $_GET['search'] : '';
$setorFilter = isset($_GET['setor_filter']) ? $_GET['setor_filter'] : '';
$postoFilter = isset($_GET['posto_filter']) ? $_GET['posto_filter'] : '';

// Construir a cláusula WHERE com base nos filtros
$whereClause = [];

if ($search) {
    $whereClause[] = "(nome LIKE '%$search%' OR posto LIKE '%$search%' OR funcao LIKE '%$search%')";
}

if ($statusFilter !== 'todos') {
    $whereClause[] = "status = '$statusFilter'";
}

if ($setorFilter !== '' && $setorFilter !== 'todos') {
    $whereClause[] = "setor = '" . $conn->real_escape_string($setorFilter) . "'";
}
if ($postoFilter !== '' && $postoFilter !== 'todos') {
    $whereClause[] = "posto = '" . $conn->real_escape_string($postoFilter) . "'";
}

// Montar a cláusula WHERE final
$whereSQL = "";
if (!empty($whereClause)) {
    $whereSQL = "WHERE " . implode(" AND ", $whereClause);
}

$result = $conn->query("SELECT * FROM pessoas $whereSQL ORDER BY id");
if (!$result) { echo $conn->error; }
$dados = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $dados[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <title>Tabela de Pessoas</title>
    <style>
        /* Estilos gerais */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #111;
            margin: 0;
            padding: 0;
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 290px; /* 270px da topbar + 20px de margem */
            padding: 30px;
            min-height: calc(100vh - 40px);
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
            position: relative;
            z-index: 1;
        }
        
        h1 {
            color: #111827;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
        }
        
        /* Estilos modernos para a tabela principal */
        .modern-table-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e5e7eb;
            margin-bottom: 30px;
        }
        
        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        
        .modern-table thead {
            background: #f8fafc;
        }
        
        .modern-table th {
            color: #374151;
            font-weight: 600;
            padding: 16px 20px;
            text-align: left;
            font-size: 14px;
            border: none;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modern-table tbody tr {
            transition: background 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .modern-table tbody tr:hover {
            background: #f9fafb;
        }
        
        .modern-table tbody tr:last-child {
            border-bottom: none;
        }
        
        .modern-table td {
            padding: 16px 20px;
            color: #111827;
            font-size: 14px;
            border: none;
        }
        
        .modern-table td:first-child {
            font-weight: 600;
            color: #2563eb;
        }
        
        /* Status badges minimalistas */
        .status-badge {
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-ativo {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inativo {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* Botões minimalistas */
        .btn-modern {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 13px;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            background: white;
            color: #374151;
            min-width: 80px;
            margin: 2px;
        }
        
        .btn-confirm {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }
        
        .btn-confirm:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }
        
        .btn-cancel {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }
        
        .btn-cancel:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
            border-color: #b91c1c;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }
        
        .btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
        }
        
        /* Filtros e busca */
        .filters-container {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
        }
        
        .filters-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* Responsividade */
        @media (max-width: 768px) {
            .content-container {
                margin: 10px;
                border-radius: 15px;
                padding: 20px;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .modern-table {
                font-size: 12px;
            }
            
            .modern-table th,
            .modern-table td {
                padding: 12px 8px;
            }
        }
        
        /* Feedback messages */
        .feedback {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .feedback.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback.delete-success {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Dropdown no canto superior direito da div principal */
        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .main-title {
            font-size: 28px;
            font-weight: 600;
            color: #111827;
        }
        
        .main-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .main-dropdown-toggle {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px 14px;
            cursor: pointer;
            font-size: 18px;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .main-dropdown-toggle:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .main-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        
        .main-dropdown-menu.show {
            display: block;
        }
        
        .main-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
            transition: background 0.2s ease;
        }
        
        .main-dropdown-item:hover {
            background: #f9fafb;
        }
        
        .main-dropdown-item:first-child {
            border-radius: 8px 8px 0 0;
        }
        
        .main-dropdown-item:last-child {
            border-radius: 0 0 8px 8px;
        }
        
        /* Popup de edição */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .popup {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .popup h3 {
            margin-top: 0;
            color: #111827;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        /* Abas do popup */
        .popup-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }
        
        .popup-tab {
            padding: 12px 20px;
            cursor: pointer;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 6px 6px 0 0;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .popup-tab.active {
            background: white;
            color: #2563eb;
            border-color: #2563eb;
        }
        
        .popup-tab:hover {
            background: #f3f4f6;
        }
        
        .popup-tab-content {
            display: none;
        }
        
        .popup-tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 8px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        /* Seção de ações */
        .actions-section {
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .actions-section h4 {
            margin-top: 0;
            color: #111827;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        /* Popup de feedback */
        .feedback-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }
        
        .feedback-popup.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .feedback-popup.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .feedback-popup.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .feedback-popup.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        /* Ordenação de colunas */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 25px !important;
        }
        
        .sortable:hover {
            background: #f3f4f6;
        }
        
        .sort-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #9ca3af;
            display: none;
        }
        
        .sort-icon.active {
            display: inline;
            color: #2563eb;
        }
        
        .sort-icon.asc::after {
            content: "▲";
        }
        
        .sort-icon.desc::after {
            content: "▼";
        }
        
        .sort-icon.default::after {
            content: "▲▼";
        }
        
        .popup-overlay#popupEmpresasOverlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.35);
            z-index: 1100;
            display: none;
        }
        .popup#popupEmpresas {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            min-width: 0;
            max-width: 520px;
            width: 98vw;
            max-height: 80vh;
            overflow: auto;
            padding: 0;
            border: none;
            z-index: 1101;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 22px 28px 10px 28px;
            border-bottom: 1px solid #f3f4f6;
            background: transparent;
        }
        .popup-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #2563eb;
        }
        .popup-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            margin-left: 10px;
        }
        .popup-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .popup-content {
            padding: 22px 28px 18px 28px;
        }
        .popup-content input[type="text"] {
            width: 85%;
            margin-bottom: 16px;
            padding: 12px 16px 12px 38px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 15px;
            background: #f8fafc;
            color: #222;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            outline: none;
            transition: border 0.2s;
            position: relative;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        .popup-content input[type="text"]:focus {
            border-color: #2563eb;
            background: #fff;
        }
        .popup-content .input-icon {
            position: absolute;
            left: 38px;
            top: 50%;
            transform: translateY(-50%);
            color: #2563eb;
            font-size: 1.1em;
            pointer-events: none;
        }
        .popup-table-container {
            max-height: 50vh;
            overflow: auto;
            border-radius: 10px;
            border: 1px solid #f3f4f6;
            background: #fff;
            margin-top: 8px;
        }
        .popup-table-container table {
            width: 100%;
            table-layout: fixed;
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
            font-size: 14px;
        }
        .popup-table-container th {
            background: #f8fafc;
            color: #2563eb;
            font-weight: 600;
            font-size: 14px;
            padding: 14px 10px;
            border: none;
        }
        .popup-table-container td {
            padding: 13px 10px;
            font-size: 14px;
            color: #222;
            border: none;
            border-bottom: 1px solid #f3f4f6;
            background: #fff;
        }
        .popup-table-container tr:last-child td {
            border-bottom: none;
        }
        .popup-table-container tr:hover {
            background: #f3f4f6;
        }
        .popup-table-container th, .popup-table-container td {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .popup-content .btn-modern.btn-confirm {
            background: #2563eb;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 14px;
            font-weight: 600;
            transition: background 0.2s;
            box-shadow: none;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .popup-content .btn-modern.btn-confirm:hover {
            background: #1d4ed8;
        }
        .popup-content .btn-modern.btn-cancel {
            background: #f44336;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 14px;
            font-weight: 600;
            margin-top: 16px;
            float: right;
            box-shadow: none;
        }
        .popup-content .btn-modern.btn-cancel:hover {
            background: #b91c1c;
        }
        .confirm-overlay {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.5);
            z-index: 20000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .confirm-popup {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.25);
            padding: 32px 24px;
            min-width: 280px;
            max-width: 90vw;
            text-align: center;
            z-index: 21000;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 18px;
        }
        .confirm-popup .confirm-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .confirm-popup .confirm-message {
            font-size: 1.1em;
            margin-bottom: 12px;
        }
        .confirm-popup .confirm-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }
        .confirm-popup .btn-confirm {
            background: #22c55e;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-cancel {
            background: #e5e7eb;
            color: #111;
            border: none;
            border-radius: 6px;
            padding: 8px 18px;
            font-size: 1em;
            cursor: pointer;
            font-weight: 500;
        }
        .confirm-popup .btn-danger {
            background: #ef4444;
            color: #fff;
        }
        .confirm-popup .btn-warning {
            background: #facc15;
            color: #92400e;
        }
        .search-bar-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 32px 0 18px 0;
            background: none;
            box-shadow: none;
            border: none;
            padding: 0;
        }
        .search-bar-wrapper {
            position: relative;
            width: 900px;
            max-width: 98vw;
            display: flex;
            align-items: center;
        }
        .search-bar-input {
            width: 100%;
            padding: 13px 44px 13px 14px;
            border: 1.5px solid #d1d5db;
            border-radius: 8px;
            font-size: 1.08em;
            outline: none;
            background: transparent;
            color: #222;
            transition: border 0.2s;
            box-shadow: none;
            height: 51px;
            margin-right: 0;
        }
        .search-bar-btn-inside {
            position: absolute;
            right: 0px;
            top: 1px;
            height: 50px;
            width: 55px;
            background: none;
            color: #111;
            border: none;
            border-radius: 8px 8px 8px 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
            z-index: 2;
        }
        .search-bar-btn-inside i {
            color: #fff;
            font-size: 1.3em;
        }
        .filter-btn {
            margin-left: 10px;
            margin-bottom: 10px;
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
            margin-top: -7px;
        }
        .filter-slide-menu {
            display: none;
            position: absolute;
            top: 45px;
            right: 0px;
            background: #fff;
            border: 1.5px solid #d1d5db;
            border-radius: 10px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.10);
            min-width: 26px;
            z-index: 100;
            padding: 16px 18px;
        }
        .filter-slide-menu.open {
            display: block;
        }
        .close-slide-menu {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 22px;
            height: 22px;
            background: none;
            border: none;
            color: #111;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            padding: 0;
            z-index: 10;
            border-radius: 50%;
            transition: background 0.2s;
        }
        .close-slide-menu:hover {
            background: none;
            color: #000;
        }
        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 18px;
        }
        .filter-popup, .popup-filtros, .popup-filtro {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header, .popup-filtros-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup, .popup-filtros-close {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover, .popup-filtros-close:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        .filter-popup-overlay {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(17, 17, 17, 0.4);
            z-index: 1001;
        }
        .filter-popup {
            display: none;
            position: fixed;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.18);
            padding: 32px 28px 24px;
            min-width: 320px;
            z-index: 1002;
            min-height: 220px;
            max-width: 95vw;
        }
        .filter-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 18px;
        }
        .close-filter-popup {
            background: none;
            border: none;
            font-size: 28px;
            color: #6b7280;
            cursor: pointer;
            border-radius: 50%;
            width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center;
            transition: background 0.2s, color 0.2s;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .close-filter-popup:hover {
            background: #f3f4f6;
            color: #2563eb;
        }
        /* Garante que não há marker ou bullet na célula do botão Selecionar */
        .popup-table-container td:last-child, .popup-table-container td .btn-modern {
            list-style: none !important;
            display: block;
        }
        .popup-table-container td {
            list-style: none !important;
        }
        .popup-table-container td::marker, .popup-table-container td *::marker {
            display: none !important;
        }
        /* Popup de seleção de setor (sempre acima do popup de edição) */
        #popupSetoresOverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(17, 17, 17, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }
        #popupSetoresOverlay.active {
            display: flex;
        }
        #popupSetores {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: 600px;
            width: 90%;
            max-height: 70vh;
            overflow-y: auto;
            z-index: 2100;
            display: none;
        }
    </style>
</head>
<body>
<?php include 'topbar.php'; ?>

<div class="content-container">
    <div class="main-header">
        <h1 class="main-title">Tabela de Pessoas</h1>
        <div class="main-dropdown">
            <button class="btn-modern btn-cancel main-dropdown-toggle" onclick="toggleMainDropdown()">
                <i class="fas fa-ellipsis-v"></i>
            </button>
            <div class="main-dropdown-menu" id="mainExportDropdown">
                <div class="main-dropdown-item" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Exportar para Excel
                </div>
                <div class="main-dropdown-item" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Exportar para PDF
                </div>
            </div>
        </div>
    </div>

    <?php if ($feedbackMessage): ?>
        <!-- Removido aviso fixo, feedback só via popup -->
    <?php endif; ?>

    <!-- Popup de feedback -->
    <div id="feedbackPopup" class="feedback-popup"></div>

    <!-- Barra de busca e filtro -->
    <div class="search-bar-container">
        <div style="display: flex; align-items: center; gap: 20px; justify-content: center;">
            <div class="search-bar-wrapper">
                <input type="text" class="search-bar-input" id="searchVisible" placeholder="Pesquisar..." value="<?= htmlspecialchars($search) ?>">
                <button type="button" class="search-bar-btn-inside no-bg" id="searchVisibleBtn" title="Buscar">
                    <i class="fas fa-search" style="color: #111;"></i>
                </button>
            </div>
            <button type="button" class="filter-btn" id="filterBtn" title="Filtros avançados" style="background: none; border: none; border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #222; cursor: pointer; transition: background 0.2s; margin-left: 16px;"><i class="fas fa-filter"></i></button>
        </div>
    </div>
    <!-- Formulário oculto para busca -->
    <form id="searchForm" method="get" style="display:none;">
        <input type="hidden" id="search" name="search" value="<?= htmlspecialchars($search) ?>">
        <!-- Manter filtros avançados ao pesquisar -->
        <input type="hidden" name="status_filter" value="<?= htmlspecialchars($statusFilter) ?>">
        <input type="hidden" name="setor_filter" value="<?= htmlspecialchars($setorFilter) ?>">
        <input type="hidden" name="posto_filter" value="<?= htmlspecialchars($postoFilter) ?>">
    </form>
    <!-- Popup de filtros moderno -->
    <div id="filterPopupOverlay" class="filter-popup-overlay"></div>
    <div id="filterPopup" class="filter-popup">
        <div class="filter-popup-header">
            <span>Filtrar por:</span>
            <button class="close-filter-popup" id="closeFilterPopup" title="Fechar">&times;</button>
        </div>
        <form method="get" class="filter-popup-form" style="display: flex; flex-direction: column; gap: 18px;">
            <div class="filter-popup-group">
                <label for="status_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Status:</label>
                <select id="status_filter" name="status_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="ativo" <?= $statusFilter == 'ativo' ? 'selected' : '' ?>>Ativo</option>
                            <option value="inativo" <?= $statusFilter == 'inativo' ? 'selected' : '' ?>>Inativo</option>
                            <option value="todos" <?= $statusFilter == 'todos' ? 'selected' : '' ?>>Todos</option>
                        </select>
            </div>
            <div class="filter-popup-group">
                <label for="setor_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Setor:</label>
                <select id="setor_filter" name="setor_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="">Todos os setores</option>
                            <?php foreach ($setores as $setor): ?>
                                <option value="<?= htmlspecialchars($setor) ?>" <?= $setorFilter == $setor ? 'selected' : '' ?>><?= htmlspecialchars($setor) ?></option>
                            <?php endforeach; ?>
                        </select>
            </div>
            <div class="filter-popup-group">
                <label for="posto_filter" style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 6px;">Posto:</label>
                <select id="posto_filter" name="posto_filter" class="filter-popup-input" style="width: 100%; padding: 10px 14px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 15px; background: #fff; color: #111827;">
                            <option value="">Todos os postos</option>
                            <?php foreach ($postos as $posto): ?>
                                <option value="<?= htmlspecialchars($posto) ?>" <?= $postoFilter == $posto ? 'selected' : '' ?>><?= htmlspecialchars($posto) ?></option>
                            <?php endforeach; ?>
                        </select>
            </div>
            <div class="filter-popup-actions" style="display: flex; gap: 12px; margin-top: 10px;">
                <a href="tabela-pessoas.php" class="btn-modern btn-cancel" style="background: #f44336; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600; text-decoration: none; display: flex; align-items: center; justify-content: center;">Limpar</a>
                <button type="submit" class="btn-modern btn-confirm" style="background: #2563eb; color: #fff; border: none; border-radius: 6px; padding: 10px 18px; font-size: 14px; font-weight: 600;">Filtrar</button>
                        </div>
                    </form>
    </div>
    <!-- Fim da barra de busca/filtro -->

    <div class="modern-table-container">
        <table class="modern-table" id="pessoasTable">
            <thead>
                <tr>
                    <th class="sortable" data-sort="id">
                        ID
                    </th>
                    <th class="sortable" data-sort="nome">
                        Nome
                    </th>
                    <th class="sortable" data-sort="posto">
                        Posto
                    </th>
                    <th class="sortable" data-sort="funcao">
                        Função
                    </th>
                    <th class="sortable" data-sort="setor">
                        Setor
                    </th>
                    <th class="sortable" data-sort="data_admissao">
                        Data de Admissão
                    </th>
                    <th class="sortable" data-sort="status">
                        Status
                    </th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($dados as $row): ?>
                    <?php 
                        $status = isset($row['status']) ? $row['status'] : 'ativo';
                        $statusClass = $status === 'ativo' ? 'status-ativo' : 'status-inativo';
                    ?>
                    <tr>
                        <td><?= $row['id'] ?></td>
                        <td><?= $row['nome'] ?></td>
                        <td><?= $row['posto'] ?></td>
                        <td><?= $row['funcao'] ?></td>
                        <td><?= $row['setor'] ?></td>
                        <td><?= !empty($row['data_admissao']) ? date('d/m/Y', strtotime($row['data_admissao'])) : '' ?></td>
                        <td><span class="status-badge <?= $statusClass ?>"><?= ucfirst($status) ?></span></td>
                        <td>
                            <button class="btn-modern btn-confirm" onclick="openEditPopup(<?= htmlspecialchars(json_encode($row)) ?>)">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <a href="cadastro-pessoas.php" class="btn-modern btn-confirm">
            <i class="fas fa-plus"></i> Adicionar Nova Pessoa
        </a>
    </div>
</div>

<!-- Popup de edição -->
<div id="editPopup" class="popup-overlay">
    <div class="popup">
        <h3>Editar Pessoa</h3>
        
        <div class="popup-tabs">
            <div class="popup-tab active" onclick="switchTab('info')">Informações</div>
            <div class="popup-tab" onclick="switchTab('actions')">Ações</div>
        </div>
        
        <div id="info-tab" class="popup-tab-content active">
            <form id="editForm" action="editar-pessoa.php" method="POST">
                <input type="hidden" id="edit_id" name="id">
                
                <div class="form-group">
                    <label for="edit_nome">Nome:</label>
                    <input type="text" id="edit_nome" name="nome" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_posto">Empresa:</label>
                    <div style="display: flex; align-items: flex-start;">
                        <div style="flex: 1; min-width:0; display: flex; align-items: flex-start; gap: 8px;">
                            <input type="text" id="edit_posto" name="posto" required readonly placeholder="Código da empresa" style="width: 90px; display: inline-block; height: 44px; font-size: 14px; padding: 0 16px; box-sizing: border-box; line-height: 44px; border: 1px solid #d1d5db; border-radius: 6px; background: #fff;">
                            <span id="edit_nome_empresa_exibicao" style="color: #2563eb; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; border: 1px solid #d1d5db; border-radius: 6px; padding: 0 8px; background: #fff; width: 330px; height: 44px; display: inline-block; font-size: 14px; line-height: 44px; box-sizing: border-box; margin: 0;"></span>
                        </div>
                        <div style="flex-shrink: 0; margin-left: 8px; display: flex; align-items: flex-start;">
                        <button type="button" class="btn-modern btn-confirm" onclick="abrirPopupEmpresas()" title="Selecionar empresa" style="width: 60px; height: 44px; display: flex; align-items: center; justify-content: center; padding: 0; border-radius: 6px;"><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="edit_funcao">Função:</label>
                    <input type="text" id="edit_funcao" name="funcao" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_setor">Setor:</label>
                    <div style="display: flex; align-items: flex-start;">
                        <div style="flex: 1; min-width:0; display: flex; align-items: flex-start; gap: 8px;">
                            <input type="text" id="edit_setor" name="setor" required readonly placeholder="Código do setor" style="width: 90px; display: inline-block; height: 44px; font-size: 14px; padding: 0 16px; box-sizing: border-box; line-height: 44px; border: 1px solid #d1d5db; border-radius: 6px; background: #fff;">
                            <span id="edit_nome_setor_exibicao" style="color: #2563eb; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; border: 1px solid #d1d5db; border-radius: 6px; padding: 0 8px; background: #fff; width: 330px; height: 44px; display: inline-block; font-size: 14px; line-height: 44px; box-sizing: border-box; margin: 0;"></span>
                        </div>
                        <div style="flex-shrink: 0; margin-left: 8px; display: flex; align-items: flex-start;">
                            <button type="button" class="btn-modern btn-confirm" onclick="abrirPopupSetores()" title="Selecionar setor" style="width: 60px; height: 44px; display: flex; align-items: center; justify-content: center; padding: 0; border-radius: 6px;"><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="edit_data_admissao">Data de Admissão:</label>
                    <input type="date" id="edit_data_admissao" name="data_admissao">
                </div>
                
                <div class="form-buttons">
                    <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Cancelar</button>
                    <button type="submit" class="btn-modern btn-confirm">Salvar</button>
                </div>
            </form>
        </div>
        
        <div id="actions-tab" class="popup-tab-content">
            <div class="actions-section">
                <h4>Ações da Pessoa</h4>
                <p>Selecione uma ação para executar na pessoa <strong id="pessoa-nome-acoes"></strong>:</p>
                
                <div class="action-buttons">
                    <button class="btn-modern btn-warning" onclick="window.inativarPessoaFromPopup()">
                        <i class="fas fa-pause"></i> Inativar Pessoa
                    </button>
                    <button class="btn-modern btn-confirm" onclick="window.ativarPessoaFromPopup()" id="btn-ativar-popup" style="display: none;">
                        <i class="fas fa-play"></i> Ativar Pessoa
                    </button>
                    <button class="btn-modern btn-danger" onclick="window.excluirPessoaFromPopup()">
                        <i class="fas fa-trash"></i> Excluir Pessoa
                    </button>
                </div>
            </div>
            
            <div class="form-buttons">
                <button type="button" class="btn-modern btn-cancel" onclick="closeEditPopup()">Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Formulário oculto para ações -->
<form id="actionForm" method="POST" action="tabela-pessoas.php" style="display: none;">
    <input type="hidden" id="delete_id" name="delete_id">
    <input type="hidden" id="inativar_id" name="inativar_id">
    <input type="hidden" id="status" name="status">
</form>

<!-- Bibliotecas para exportação -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.7.0/jspdf.plugin.autotable.min.js"></script>

<script>
    let currentPessoa = null;
    let currentSort = { column: null, direction: 'asc' };
    
    // Função para mostrar popup de feedback
    function showFeedback(message, type = 'success') {
        const popup = document.getElementById('feedbackPopup');
        popup.textContent = message;
        popup.className = `feedback-popup ${type}`;
        popup.classList.add('show');
        
        setTimeout(() => {
            popup.classList.remove('show');
        }, 5000);
    }
    
    // Verificar se há mensagem de feedback na URL
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    if (status) {
        let message = '';
        let type = 'success';
        
        switch(status) {
            case 'edit-success':
                message = 'Edição realizada com sucesso!';
                type = 'success'; // verde
                break;
            case 'edit-error':
                message = 'Erro ao editar o registro.';
                type = 'error'; // vermelho
                break;
            case 'delete-success':
                message = 'Exclusão realizada com sucesso!';
                type = 'error'; // vermelho
                break;
            case 'success':
                message = urlParams.get('message') || 'Operação realizada com sucesso!';
                type = 'warning'; // amarelo para ativar/inativar
                break;
            case 'error':
                message = urlParams.get('message') || 'Erro ao realizar a operação.';
                type = 'error'; // vermelho
                break;
        }
        
        if (message) {
            showFeedback(message, type);
        }
    }
    
    // Fechar dropdown quando clicar fora
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('.main-dropdown');
        const dropdownMenu = document.getElementById('mainExportDropdown');
        
        if (!dropdown.contains(event.target)) {
            dropdownMenu.classList.remove('show');
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // --- Popup de Confirmação Global ---
        let confirmAction = null;
        window.showConfirmPopup = function({title, message, onConfirm, type = 'default'}) {
            document.getElementById('confirmTitle').textContent = title;
            document.getElementById('confirmMessage').textContent = message;
            const popup = document.getElementById('confirmPopup');
            popup.classList.remove('btn-danger', 'btn-warning');
            if (type === 'danger') {
                document.getElementById('btnConfirmYes').classList.add('btn-danger');
                document.getElementById('btnConfirmYes').classList.remove('btn-warning');
            } else if (type === 'warning') {
                document.getElementById('btnConfirmYes').classList.add('btn-warning');
                document.getElementById('btnConfirmYes').classList.remove('btn-danger');
            } else {
                document.getElementById('btnConfirmYes').classList.remove('btn-danger', 'btn-warning');
            }
            document.getElementById('confirmOverlay').style.display = 'flex';
            confirmAction = onConfirm;
        };
        document.getElementById('btnConfirmYes').onclick = function() {
            document.getElementById('confirmOverlay').style.display = 'none';
            if (typeof confirmAction === 'function') confirmAction();
        };
        document.getElementById('btnConfirmNo').onclick = function() {
            document.getElementById('confirmOverlay').style.display = 'none';
            confirmAction = null;
        };
    });

    // Substituir ações por popups de confirmação
    function excluirPessoa(id, nome) {
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir a pessoa "${nome}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = id;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarPessoa(id) {
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar esta pessoa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = id;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarPessoa(id) {
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar esta pessoa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = id;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function excluirPessoaFromPopup() {
        if (!currentPessoa) return;
        showConfirmPopup({
            title: 'Confirmar Exclusão',
            message: `Tem certeza que deseja excluir a pessoa "${currentPessoa.nome}"? Esta ação não pode ser desfeita.`,
            type: 'danger',
            onConfirm: function() {
                document.getElementById('delete_id').value = currentPessoa.id;
                document.getElementById('actionForm').submit();
            }
        });
    }
    function inativarPessoaFromPopup() {
        if (!currentPessoa) return;
        showConfirmPopup({
            title: 'Confirmar Inativação',
            message: 'Tem certeza que deseja inativar esta pessoa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentPessoa.id;
                document.getElementById('status').value = 'inativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    function ativarPessoaFromPopup() {
        if (!currentPessoa) return;
        showConfirmPopup({
            title: 'Confirmar Ativação',
            message: 'Tem certeza que deseja ativar esta pessoa?',
            type: 'warning',
            onConfirm: function() {
                document.getElementById('inativar_id').value = currentPessoa.id;
                document.getElementById('status').value = 'ativo';
                document.getElementById('actionForm').submit();
            }
        });
    }
    // Confirmação ao salvar edição
    function attachEditFormConfirm() {
        const editForm = document.getElementById('editForm');
        if (editForm) {
            editForm.onsubmit = function(e) {
                e.preventDefault();
                showConfirmPopup({
                    title: 'Confirmar Edição',
                    message: 'Deseja salvar as alterações desta pessoa?',
                    type: 'confirm',
                    onConfirm: function() {
                        attachEditFormConfirm(); // reanexa o handler para próximas edições
                        editForm.submit();
                    }
                });
                return false;
            };
        }
    }
    attachEditFormConfirm();

    function openEditPopup(rowData) {
        currentPessoa = rowData;
        document.getElementById('edit_id').value = rowData.id;
        document.getElementById('edit_nome').value = rowData.nome;
        document.getElementById('edit_posto').value = rowData.posto;
        buscarNomeEmpresa(rowData.posto, function(nomeEmpresa) {
            document.getElementById('edit_nome_empresa_exibicao').textContent = nomeEmpresa;
        });
        document.getElementById('edit_funcao').value = rowData.funcao;
        document.getElementById('edit_setor').value = rowData.setor;
        // Buscar e exibir o nome do setor ao lado do código
        buscarNomeSetor(rowData.setor, function(nomeSetor) {
            document.getElementById('edit_nome_setor_exibicao').textContent = nomeSetor;
        });
        let data = rowData.data_admissao;
        document.getElementById('edit_data_admissao').value = data ? data.split(' ')[0] : '';
        document.getElementById('editPopup').style.display = 'flex';
    }

    function closeEditPopup() {
        document.getElementById('editPopup').style.display = 'none';
        currentPessoa = null;
    }

    function switchTab(tabName) {
        // Desativar todas as abas
        document.querySelectorAll('.popup-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.popup-tab-content').forEach(content => content.classList.remove('active'));
        
        // Ativar a aba selecionada
        document.querySelector(`.popup-tab[onclick="switchTab('${tabName}')"]`).classList.add('active');
        document.getElementById(`${tabName}-tab`).classList.add('active');
    }

    function toggleMainDropdown() {
        document.getElementById('mainExportDropdown').classList.toggle('show');
    }

    // Função para ordenar a tabela
    function sortTable(column) {
        const table = document.getElementById('pessoasTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        // Determinar direção da ordenação
        if (currentSort.column === column) {
            currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.column = column;
            currentSort.direction = 'asc';
        }
        // Remover todos os sort-icons
        document.querySelectorAll('.sortable .sort-icon').forEach(icon => icon.remove());
        // Adicionar sort-icon apenas na coluna ordenada
        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        const icon = document.createElement('span');
        icon.className = `sort-icon active ${currentSort.direction}`;
        currentHeader.appendChild(icon);
        // Ordenar as linhas
        rows.sort((a, b) => {
            let aValue = a.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            let bValue = b.querySelector(`td:nth-child(${getColumnIndex(column)})`).textContent.trim();
            // Converter para número se for numérico
            if (column === 'id') {
                aValue = parseFloat(aValue) || 0;
                bValue = parseFloat(bValue) || 0;
            }
            // Converter para data se for data_admissao
            if (column === 'data_admissao') {
                // Espera-se formato dd/mm/yyyy
                function toDate(str) {
                    if (!str) return new Date(0);
                    const parts = str.split('/');
                    if (parts.length === 3) {
                        return new Date(parts[2], parts[1] - 1, parts[0]);
                    }
                    // Se vier yyyy-mm-dd
                    const parts2 = str.split('-');
                    if (parts2.length === 3) {
                        return new Date(parts2[0], parts2[1] - 1, parts2[2]);
                    }
                    return new Date(str);
                }
                aValue = toDate(aValue);
                bValue = toDate(bValue);
                if (aValue < bValue) return currentSort.direction === 'asc' ? -1 : 1;
                if (aValue > bValue) return currentSort.direction === 'asc' ? 1 : -1;
                return 0;
            }
            if (currentSort.direction === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
        // Reordenar as linhas na tabela
        rows.forEach(row => tbody.appendChild(row));
    }
    
    function getColumnIndex(column) {
        const columnMap = {
            'id': 1,
            'nome': 2,
            'posto': 3,
            'funcao': 4,
            'setor': 5,
            'data_admissao': 6,
            'status': 7
        };
        return columnMap[column];
    }
    
    // Adicionar listeners para ordenação
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                sortTable(column);
            });
        });
    });

    function exportToExcel() {
        const table = document.getElementById('pessoasTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const wb = XLSX.utils.table_to_book(clonedTable, {sheet: "Pessoas"});
        XLSX.writeFile(wb, 'pessoas.xlsx');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    function exportToPDF() {
        const table = document.getElementById('pessoasTable');
        const clonedTable = table.cloneNode(true);
        
        // Remover a coluna de ações
        const rows = clonedTable.querySelectorAll('tr');
        rows.forEach(function(row) {
            const cells = row.querySelectorAll('td, th');
            if (cells.length > 0) {
                row.removeChild(cells[cells.length - 1]);
            }
        });
        
        const doc = new jspdf.jsPDF('l', 'pt', 'a4');
        doc.autoTable({ html: clonedTable });
        doc.save('pessoas.pdf');
        
        // Fechar dropdown
        document.getElementById('mainExportDropdown').classList.remove('show');
    }

    // --- Popup de Seleção de Empresa ---
    function abrirPopupEmpresas() {
        document.getElementById('popupEmpresas').style.display = 'block';
        document.getElementById('popupEmpresasOverlay').style.display = 'block';
        document.getElementById('pesquisaEmpresa').value = '';
        filtrarEmpresas();
    }
    function fecharPopupEmpresas() {
        document.getElementById('popupEmpresas').style.display = 'none';
        document.getElementById('popupEmpresasOverlay').style.display = 'none';
    }
    function selecionarEmpresa(codigo, nome) {
        document.getElementById('edit_posto').value = codigo;
        document.getElementById('edit_nome_empresa_exibicao').textContent = nome;
        fecharPopupEmpresas();
    }
    function filtrarEmpresas() {
        let termo = document.getElementById('pesquisaEmpresa').value.toLowerCase();
        let linhas = document.querySelectorAll('#listaEmpresas tr');
        linhas.forEach(tr => {
            let nome = tr.children[1].textContent.toLowerCase();
            tr.style.display = nome.includes(termo) ? '' : 'none';
        });
    }
    function buscarNomeEmpresa(codigo, callback) {
        if (!codigo) { callback(''); return; }
        fetch('buscar-empresa.php?codigo_empresa=' + encodeURIComponent(codigo))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    callback(data.nome_empresa);
                } else {
                    callback('');
                }
            })
            .catch(() => callback(''));
    }

    // --- Popup de Seleção de Setor ---
    function abrirPopupSetores() {
        document.getElementById('popupSetores').style.display = 'block';
        document.getElementById('popupSetoresOverlay').style.display = 'flex';
        document.getElementById('popupSetoresOverlay').classList.add('active');
        document.getElementById('pesquisaSetor').value = '';
        filtrarSetores();
    }
    function fecharPopupSetores() {
        document.getElementById('popupSetores').style.display = 'none';
        document.getElementById('popupSetoresOverlay').style.display = 'none';
        document.getElementById('popupSetoresOverlay').classList.remove('active');
    }
    function selecionarSetor(codigo, nome) {
        document.getElementById('edit_setor').value = codigo;
        document.getElementById('edit_nome_setor_exibicao').textContent = nome;
        fecharPopupSetores();
    }
    function filtrarSetores() {
        let termo = document.getElementById('pesquisaSetor').value.toLowerCase();
        let linhas = document.querySelectorAll('#listaSetores tr');
        linhas.forEach(tr => {
            let nome = tr.children[1].textContent.toLowerCase();
            tr.style.display = nome.includes(termo) ? '' : 'none';
        });
    }

    const searchVisible = document.getElementById('searchVisible');
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('search');
    const searchVisibleBtn = document.getElementById('searchVisibleBtn');
    searchVisible.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchInput.value = searchVisible.value;
            searchForm.submit();
        }
    });
    searchVisibleBtn.addEventListener('click', function() {
        searchInput.value = searchVisible.value;
        searchForm.submit();
    });
    // Popup de filtros moderno
    const filterBtn = document.getElementById('filterBtn');
    const filterPopup = document.getElementById('filterPopup');
    const filterPopupOverlay = document.getElementById('filterPopupOverlay');
    const closeFilterPopup = document.getElementById('closeFilterPopup');
    filterBtn.addEventListener('click', function(e) {
        filterPopup.style.display = 'block';
        filterPopupOverlay.style.display = 'block';
        e.stopPropagation();
    });
    closeFilterPopup.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });
    filterPopupOverlay.addEventListener('click', function() {
        filterPopup.style.display = 'none';
        filterPopupOverlay.style.display = 'none';
    });

    // Função para buscar o nome do setor pelo código
    function buscarNomeSetor(codigo, callback) {
        if (!codigo) { callback(''); return; }
        fetch('buscar-setor-por-id.php?id=' + encodeURIComponent(codigo))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    callback(data.nome);
                } else {
                    callback('');
                }
            })
            .catch(() => callback(''));
    }
</script>

<!-- Popup de Seleção de Empresa -->
<div class="popup-overlay" id="popupEmpresasOverlay" style="display:none;"></div>
<div class="popup" id="popupEmpresas" style="display:none;">
    <div class="popup-header">
        <h3>Selecionar Empresa</h3>
        <button class="popup-close" onclick="fecharPopupEmpresas()">&times;</button>
    </div>
    <div class="popup-content" style="position:relative;">
        <span class="input-icon" style="position:absolute; left:38px; top:38px;"><i class="fas fa-search"></i></span>
        <input type="text" id="pesquisaEmpresa" placeholder="Pesquisar empresas..." oninput="filtrarEmpresas()" style="padding-left:38px;">
        <div class="popup-table-container">
            <table>
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome</th>
                        <th>CNPJ</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="listaEmpresas">
                    <?php $resEmp = $conn->query("SELECT codigo_empresa, nome_empresa, cnpj FROM empresas WHERE status = 'ativo' ORDER BY nome_empresa");
                    while ($row = $resEmp->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['codigo_empresa'] ?></td>
                        <td><?= htmlspecialchars($row['nome_empresa']) ?></td>
                        <td><?= htmlspecialchars($row['cnpj']) ?></td>
                        <td><button type="button" class="btn-modern btn-confirm" onclick="selecionarEmpresa('<?= $row['codigo_empresa'] ?>', '<?= htmlspecialchars($row['nome_empresa'], ENT_QUOTES) ?>')">Selecionar</button></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <button class="btn-modern btn-cancel" style="margin-top:16px; float:right;">Fechar</button>
    </div>
</div>

<!-- Popup de Seleção de Setor -->
<div class="popup-overlay" id="popupSetoresOverlay" style="display:none;"></div>
<div class="popup" id="popupSetores" style="display:none;">
    <div class="popup-header">
        <h3>Selecionar Setor</h3>
        <button class="popup-close" onclick="fecharPopupSetores()">&times;</button>
    </div>
    <div class="popup-content" style="position:relative;">
        <span class="input-icon" style="position:absolute; left:38px; top:38px;"><i class="fas fa-search"></i></span>
        <input type="text" id="pesquisaSetor" placeholder="Pesquisar setores..." oninput="filtrarSetores()" style="padding-left:38px;">
        <div class="popup-table-container">
            <table>
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Nome</th>
                        <th>Ação</th>
                    </tr>
                </thead>
                <tbody id="listaSetores">
                    <?php $resSetor = $conn->query("SELECT id, nome FROM setor WHERE status = 'ativo' OR status IS NULL ORDER BY nome");
                    while ($row = $resSetor->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['id'] ?></td>
                        <td><?= htmlspecialchars($row['nome']) ?></td>
                        <td><button type="button" class="btn-modern btn-confirm" onclick="selecionarSetor('<?= $row['id'] ?>', '<?= htmlspecialchars($row['nome'], ENT_QUOTES) ?>')">Selecionar</button></td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <button class="btn-modern btn-cancel" style="margin-top:16px; float:right;" onclick="fecharPopupSetores()">Fechar</button>
    </div>
</div>

<!-- Overlay e Popup de Confirmação -->
<div class="confirm-overlay" id="confirmOverlay">
    <div class="confirm-popup" id="confirmPopup">
        <div class="confirm-title" id="confirmTitle"></div>
        <div class="confirm-message" id="confirmMessage"></div>
        <div class="confirm-actions">
            <button class="btn-confirm" id="btnConfirmYes">Confirmar</button>
            <button class="btn-cancel" id="btnConfirmNo">Cancelar</button>
        </div>
    </div>
</div>

</body>
</html>

<?php
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['usuario_id'])) {
    header("Location: login.php");
    exit();
}

// Função para verificar se o usuário tem permissão de administrador
function verificarAdmin() {
    if (!isset($_SESSION['tipo_usuario']) || $_SESSION['tipo_usuario'] !== 'administrador') {
        header("Location: index.php");
        exit();
    }
}

// Função para verificar se o usuário tem permissão de supervisor ou administrador
function verificarSupervisor() {
    if (!isset($_SESSION['tipo_usuario']) || 
        ($_SESSION['tipo_usuario'] !== 'supervisor' && $_SESSION['tipo_usuario'] !== 'administrador')) {
        header("Location: index.php");
        exit();
    }
}

// Função para verificar se o usuário tem permissão de almoxarife, supervisor ou administrador
function verificarAlmoxarife() {
    if (!isset($_SESSION['tipo_usuario']) || 
        ($_SESSION['tipo_usuario'] !== 'almoxarife' && 
         $_SESSION['tipo_usuario'] !== 'supervisor' && 
         $_SESSION['tipo_usuario'] !== 'administrador')) {
        header("Location: index.php");
        exit();
    }
}
?> 
<?php
include 'conexao.php';

echo "<h2>Teste do Aviso Urgente em Requisições Feitas</h2>";

// Criar uma requisição urgente de teste
$sql = "INSERT INTO requisicoes (solicitante, empresa, contrato, finalidade, funcionario, funcao, setor, observacao, requisicao_urgente, motivo_urgente, data_solicitacao) 
        VALUES ('Teste Aviso Req Feitas', '001', 'REQ-FEITAS', 'Teste do aviso em requisicoes feitas', '1', 'Teste', 'TI', 'Requisição para testar aviso em requisicoes-feitas.php', 1, 'Limite excedido', NOW())";

if ($conn->query($sql)) {
    $codigo_requisicao = $conn->insert_id;
    echo "<p style='color: green;'>✅ Requisição urgente criada com código: <strong>$codigo_requisicao</strong></p>";
    
    // Adicionar alguns itens de teste
    $sql_item = "INSERT INTO itens_solicitacao (codigo_solicitacao, produto, quantidade) VALUES (?, 'REQ-FEITAS', 1)";
    $stmt = $conn->prepare($sql_item);
    $stmt->bind_param("i", $codigo_requisicao);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Item adicionado à requisição</p>";
    } else {
        echo "<p style='color: red;'>❌ Erro ao adicionar item: " . $stmt->error . "</p>";
    }
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎯 Aviso Urgente Implementado!</h3>";
    echo "<p><strong>Código da requisição:</strong> $codigo_requisicao</p>";
    echo "<p><strong>Implementação realizada:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>CSS adicionado:</strong> Estilos para .aviso-urgente</li>";
    echo "<li>✅ <strong>HTML adicionado:</strong> Div do aviso no popup</li>";
    echo "<li>✅ <strong>JavaScript modificado:</strong> Lógica para mostrar/ocultar aviso</li>";
    echo "<li>✅ <strong>Visual consistente:</strong> Mesmo design do todas-solitacoes-estoque.php</li>";
    echo "<li>✅ <strong>Funcionalidade completa:</strong> Detecta requisicao_urgente = 1</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 5px; border-left: 4px solid #0ea5e9;'>";
    echo "<h4>🧪 Como testar o aviso:</h4>";
    echo "<ol>";
    echo "<li>Ir para 'Solicitações Realizadas' (requisicoes-feitas.php)</li>";
    echo "<li>Localizar a requisição criada (código $codigo_requisicao)</li>";
    echo "<li>Clicar no botão 'Informações' da requisição</li>";
    echo "<li><strong>Verificar:</strong> Aviso '🚨 Pedido urgente após valor limite excedido' aparece</li>";
    echo "<li><strong>Verificar:</strong> Aviso tem fundo vermelho claro</li>";
    echo "<li><strong>Verificar:</strong> Título permanece 'Detalhes da Solicitação'</li>";
    echo "<li>Testar com requisições normais (sem urgente) para verificar que o aviso não aparece</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>❌ Erro ao criar requisição de teste: " . $conn->error . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='requisicoes-feitas.php' style='background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>🔗 Testar em Requisições Feitas</a></p>";

$conn->close();
?>

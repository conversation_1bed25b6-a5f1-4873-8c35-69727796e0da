<?php
header('Content-Type: application/json');
include 'conexao.php';

$setor = isset($_GET['setor']) ? $_GET['setor'] : '';

if (empty($setor)) {
    echo json_encode(['error' => 'Parâmetro setor é inválido.']);
    exit;
}

$conn->set_charset("utf8");

$detalhes = [];

// --- Lógica para descobrir as colunas de data ---
$col_data_requisicoes = 'data_hora';
$check_req_data = $conn->query("SHOW COLUMNS FROM requisicoes LIKE '%data%'");
if ($check_req_data && $check_req_data->num_rows > 0) {
    $col = $check_req_data->fetch_assoc();
    $col_data_requisicoes = $col['Field'];
}

$col_data_pedidos_mensais = 'data_pedido';
$check_pm_data = $conn->query("SHOW COLUMNS FROM pedidos_mensais LIKE '%data%'");
if ($check_pm_data && $check_pm_data->num_rows > 0) {
    $col = $check_pm_data->fetch_assoc();
    $col_data_pedidos_mensais = $col['Field'];
}

// 1. Saídas diretas (registro-saidas.php)
$sqlSaidas = "
    SELECT 
        p.nome as produto, 
        ps.quantidade, 
        p.valor as valor_unitario,
        s.data_saida as data,
        s.destinatario,
        'Saída Direta' as origem,
        s.id as origem_id
    FROM saidas_estoque s
    JOIN produtos_saida ps ON s.id = ps.saida_id
    JOIN produtos p ON ps.codigo = p.codigo
    WHERE (s.status IS NULL OR s.status = 'ativa') AND s.setor_destinatario " . ($setor === 'Não informado' ? " = '' OR s.setor_destinatario IS NULL" : " = ?");

$stmt = $conn->prepare($sqlSaidas);
if ($setor !== 'Não informado') {
    $stmt->bind_param('s', $setor);
}
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $detalhes[] = $row;
}
$stmt->close();

// 2. Requisições e Pedidos via Tabela Pessoas
$funcionarios = [];
$sqlPessoas = "SELECT nome FROM pessoas WHERE setor " . ($setor === 'Não informado' ? " = '' OR setor IS NULL" : "= ?");
$stmtPessoas = $conn->prepare($sqlPessoas);
if ($setor !== 'Não informado') {
    $stmtPessoas->bind_param('s', $setor);
}
$stmtPessoas->execute();
$resPessoas = $stmtPessoas->get_result();
while($p = $resPessoas->fetch_assoc()){
    $funcionarios[] = $p['nome'];
}
$stmtPessoas->close();

if (!empty($funcionarios)) {
    $inClause = "IN ('" . implode("','", array_map([$conn, 'real_escape_string'], $funcionarios)) . "')";

    // 2.1 Requisições concluídas
    $sqlReqs = "
        SELECT
            p.nome as produto,
            i.quantidade,
            p.valor as valor_unitario,
            r.{$col_data_requisicoes} as data,
            r.funcionario as destinatario,
            'Requisição' as origem,
            r.codigo_solicitacao as origem_id
        FROM requisicoes r
        JOIN itens_solicitacao i ON r.codigo_solicitacao = i.codigo_solicitacao
        JOIN produtos p ON i.produto = p.codigo
        WHERE r.status = 'concluido' AND r.funcionario {$inClause}";
    $result = $conn->query($sqlReqs);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $detalhes[] = $row;
        }
    }

    // 2.2 Pedidos Mensais concluídos
    $sqlPMs = "
        SELECT
            p.nome as produto,
            i.quantidade,
            p.valor as valor_unitario,
            pm.{$col_data_pedidos_mensais} as data,
            pm.destinatario,
            'Pedido Mensal' as origem,
            pm.codigo_pedido as origem_id
        FROM pedidos_mensais pm
        JOIN itens_pedido_mensal i ON pm.codigo_pedido = i.codigo_pedido
        JOIN produtos p ON i.produto = p.codigo
        WHERE pm.status = 'concluido' AND pm.destinatario {$inClause}";
    $result = $conn->query($sqlPMs);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $detalhes[] = $row;
        }
    }
}

// Calcular valor_total e ordenar
foreach ($detalhes as $key => $row) {
    $detalhes[$key]['valor_total'] = (float)$row['quantidade'] * (float)$row['valor_unitario'];
}

// Sort results by date
if (!empty($detalhes)) {
    usort($detalhes, function($a, $b) {
        if (strtotime($a['data']) == strtotime($b['data'])) {
            return 0;
        }
        return (strtotime($a['data']) > strtotime($b['data'])) ? -1 : 1;
    });
}

echo json_encode($detalhes);
$conn->close();
?> 
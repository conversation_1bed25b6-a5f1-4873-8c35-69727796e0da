<?php
session_start();
include 'conexao.php';

header('Content-Type: application/json');

// Log da requisição
error_log("Verificação de admin solicitada. Sessão: " . print_r($_SESSION, true));

if (!isset($_SESSION['usuario_id'])) {
    echo json_encode(['success' => false, 'message' => 'Não autenticado', 'debug' => 'Sem usuario_id na sessão']);
    exit;
}

$usuario_id = $_SESSION['usuario_id'];

try {
    // Verificar se o usuário é administrador
    $stmt = $conn->prepare("SELECT nivel, tipo, nome FROM usuarios WHERE id = ?");
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Usuário não encontrado', 'debug' => "ID $usuario_id não existe"]);
        exit;
    }

    $usuario = $result->fetch_assoc();
    error_log("Dados do usuário: " . print_r($usuario, true));

    // Verificar se é administrador com lógica mais abrangente
    $isAdmin = false;
    $criterios = [];

    // 1. Verificar por campo 'nivel'
    if (isset($usuario['nivel']) && !empty($usuario['nivel'])) {
        $nivel = strtolower(trim($usuario['nivel']));
        if ($nivel === 'admin' || $nivel === 'administrador' || $nivel === '1' || $nivel === 'administrator') {
            $isAdmin = true;
            $criterios[] = "nivel: $nivel";
        }
    }

    // 2. Verificar por campo 'tipo'
    if (isset($usuario['tipo']) && !empty($usuario['tipo'])) {
        $tipo = strtolower(trim($usuario['tipo']));
        if ($tipo === 'admin' || $tipo === 'administrador' || $tipo === '1' || $tipo === 'administrator') {
            $isAdmin = true;
            $criterios[] = "tipo: $tipo";
        }
    }

    // 3. Verificar por nome do usuário (fallback)
    if (!$isAdmin && isset($usuario['nome'])) {
        $nomeUsuario = strtolower(trim($usuario['nome']));
        if (strpos($nomeUsuario, 'admin') !== false || strpos($nomeUsuario, 'administrador') !== false) {
            $isAdmin = true;
            $criterios[] = "nome: $nomeUsuario";
        }
    }

    // 4. Verificar se é o primeiro usuário (ID 1) como fallback
    if (!$isAdmin && $usuario_id == 1) {
        $isAdmin = true;
        $criterios[] = "primeiro usuário (ID 1)";
    }

    $response = [
        'success' => true,
        'is_admin' => $isAdmin,
        'usuario' => [
            'id' => $usuario_id,
            'nome' => $usuario['nome'],
            'nivel' => $usuario['nivel'] ?? null,
            'tipo' => $usuario['tipo'] ?? null
        ],
        'criterios_admin' => $criterios,
        'debug' => "Verificação concluída. Admin: " . ($isAdmin ? 'SIM' : 'NÃO')
    ];

    error_log("Resposta verificação admin: " . print_r($response, true));
    echo json_encode($response);

} catch (Exception $e) {
    $error_response = ['success' => false, 'message' => 'Erro ao verificar permissões: ' . $e->getMessage()];
    error_log("Erro na verificação admin: " . print_r($error_response, true));
    echo json_encode($error_response);
}

$conn->close();
?>

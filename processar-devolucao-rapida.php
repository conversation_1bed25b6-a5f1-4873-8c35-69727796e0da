<?php
session_start();
if (!isset($_SESSION['usuario_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit();
}

require_once 'conexao.php';

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit();
}

// Obter dados JSON
$input = file_get_contents('php://input');
$dados = json_decode($input, true);

if (!$dados || !isset($dados['devolucoes'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dados inválidos']);
    exit();
}

$devolucoes = $dados['devolucoes'];

if (empty($devolucoes)) {
    echo json_encode(['success' => false, 'message' => 'Nenhum item para devolução']);
    exit();
}

try {
    // Iniciar transação
    $conn->autocommit(false);
    
    // Verificar se a tabela de devoluções rápidas existe, se não, criar
    $result = $conn->query("SHOW TABLES LIKE 'devolucoes_rapidas'");
    if ($result->num_rows == 0) {
        $sql_create_table = "
            CREATE TABLE devolucoes_rapidas (
                id INT AUTO_INCREMENT PRIMARY KEY,
                produto_id VARCHAR(50) NOT NULL,
                produto_nome VARCHAR(255) NOT NULL,
                quantidade INT NOT NULL,
                estado VARCHAR(50) NOT NULL,
                data_devolucao DATETIME DEFAULT CURRENT_TIMESTAMP,
                usuario_id INT NOT NULL,
                INDEX idx_produto_id (produto_id),
                INDEX idx_data_devolucao (data_devolucao)
            )
        ";
        
        if (!$conn->query($sql_create_table)) {
            throw new Exception("Erro ao criar tabela de devoluções rápidas: " . $conn->error);
        }
    }
    
    $usuario_id = $_SESSION['usuario_id'];
    
    foreach ($devolucoes as $devolucao) {
        $produto_id = $devolucao['produto_id'];
        $produto_nome = $devolucao['produto_nome'];
        $quantidade = intval($devolucao['quantidade']);
        $estado = $devolucao['estado'];
        
        // 1. Registrar a devolução rápida
        $sql_devolucao = "
            INSERT INTO devolucoes_rapidas 
            (produto_id, produto_nome, quantidade, estado, usuario_id) 
            VALUES (?, ?, ?, ?, ?)
        ";
        
        $stmt = $conn->prepare($sql_devolucao);
        $stmt->bind_param("ssisi", $produto_id, $produto_nome, $quantidade, $estado, $usuario_id);
        
        if (!$stmt->execute()) {
            throw new Exception("Erro ao registrar devolução rápida: " . $stmt->error);
        }
        
        // 2. Processar retorno ao estoque baseado no estado
        if ($estado === 'novo') {
            // Novo/Sem Uso: Adicionar quantidade ao produto original
            $sql_update_original = "UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?";
            $stmt_update_original = $conn->prepare($sql_update_original);
            $stmt_update_original->bind_param("is", $quantidade, $produto_id);
            
            if (!$stmt_update_original->execute()) {
                throw new Exception("Erro ao atualizar estoque do produto original: " . $stmt_update_original->error);
            }
            
        } elseif ($estado === 'usado_bom' || $estado === 'usado_leve') {
            // Usado: Criar ou atualizar produto SEMI
            $nome_produto_semi = $produto_nome . ' SEMI';
            
            // Verificar se produto SEMI já existe
            $sql_check_semi = "SELECT codigo, quantidade FROM produtos WHERE nome = ?";
            $stmt_check_semi = $conn->prepare($sql_check_semi);
            $stmt_check_semi->bind_param("s", $nome_produto_semi);
            $stmt_check_semi->execute();
            $result_semi = $stmt_check_semi->get_result();
            
            if ($result_semi->num_rows > 0) {
                // Produto SEMI existe, atualizar quantidade
                $produto_semi = $result_semi->fetch_assoc();
                $sql_update_semi = "UPDATE produtos SET quantidade = quantidade + ? WHERE codigo = ?";
                $stmt_update_semi = $conn->prepare($sql_update_semi);
                $stmt_update_semi->bind_param("is", $quantidade, $produto_semi['codigo']);
                
                if (!$stmt_update_semi->execute()) {
                    throw new Exception("Erro ao atualizar produto SEMI: " . $stmt_update_semi->error);
                }
            } else {
                // Produto SEMI não existe, criar novo
                // Buscar dados do produto original
                $sql_produto_original = "SELECT * FROM produtos WHERE codigo = ?";
                $stmt_original = $conn->prepare($sql_produto_original);
                $stmt_original->bind_param("s", $produto_id);
                $stmt_original->execute();
                $result_original = $stmt_original->get_result();
                
                if ($result_original->num_rows > 0) {
                    $produto_original = $result_original->fetch_assoc();
                    
                    // Gerar novo código para produto SEMI
                    $sql_max_codigo = "SELECT MAX(CAST(codigo AS UNSIGNED)) as max_codigo FROM produtos WHERE codigo REGEXP '^[0-9]+$'";
                    $result_max = $conn->query($sql_max_codigo);
                    $max_codigo = $result_max->fetch_assoc()['max_codigo'] ?? 0;
                    $novo_codigo = $max_codigo + 1;
                    
                    // Inserir novo produto SEMI
                    $sql_insert_semi = "
                        INSERT INTO produtos 
                        (codigo, unidade_medida, nome, categoria, ca, valor, valor_bruto, quantidade, validade, validade_uso) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ";
                    
                    $stmt_insert = $conn->prepare($sql_insert_semi);
                    $stmt_insert->bind_param(
                        "sssssddisd", 
                        $novo_codigo,
                        $produto_original['unidade_medida'],
                        $nome_produto_semi,
                        $produto_original['categoria'],
                        $produto_original['ca'],
                        $produto_original['valor'],
                        $produto_original['valor_bruto'],
                        $quantidade,
                        $produto_original['validade'],
                        $produto_original['validade_uso']
                    );
                    
                    if (!$stmt_insert->execute()) {
                        throw new Exception("Erro ao criar produto SEMI: " . $stmt_insert->error);
                    }
                } else {
                    throw new Exception("Produto original não encontrado para criar SEMI");
                }
            }
        }
        // Para 'danificado_reparo', 'danificado_irrecuperavel', 'vencido', 'descarte' não adiciona ao estoque
    }
    
    // Confirmar transação
    $conn->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => 'Devolução rápida processada com sucesso',
        'itens_processados' => count($devolucoes)
    ]);
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    $conn->rollback();
    
    echo json_encode([
        'success' => false, 
        'message' => 'Erro ao processar devolução rápida: ' . $e->getMessage()
    ]);
}

$conn->autocommit(true);
$conn->close();
?>
